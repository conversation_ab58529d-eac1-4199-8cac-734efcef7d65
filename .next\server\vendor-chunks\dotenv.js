/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/dotenv";
exports.ids = ["vendor-chunks/dotenv"];
exports.modules = {

/***/ "(ssr)/./node_modules/dotenv/lib/main.js":
/*!*****************************************!*\
  !*** ./node_modules/dotenv/lib/main.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const fs = __webpack_require__(/*! fs */ \"fs\")\nconst path = __webpack_require__(/*! path */ \"path\")\nconst os = __webpack_require__(/*! os */ \"os\")\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\")\nconst packageJson = __webpack_require__(/*! ../package.json */ \"(ssr)/./node_modules/dotenv/package.json\")\n\nconst version = packageJson.version\n\nconst LINE = /(?:^|^)\\s*(?:export\\s+)?([\\w.-]+)(?:\\s*=\\s*?|:\\s+?)(\\s*'(?:\\\\'|[^'])*'|\\s*\"(?:\\\\\"|[^\"])*\"|\\s*`(?:\\\\`|[^`])*`|[^#\\r\\n]+)?\\s*(?:#.*)?(?:$|$)/mg\n\n// Parse src into an Object\nfunction parse (src) {\n  const obj = {}\n\n  // Convert buffer to string\n  let lines = src.toString()\n\n  // Convert line breaks to same format\n  lines = lines.replace(/\\r\\n?/mg, '\\n')\n\n  let match\n  while ((match = LINE.exec(lines)) != null) {\n    const key = match[1]\n\n    // Default undefined or null to empty string\n    let value = (match[2] || '')\n\n    // Remove whitespace\n    value = value.trim()\n\n    // Check if double quoted\n    const maybeQuote = value[0]\n\n    // Remove surrounding quotes\n    value = value.replace(/^(['\"`])([\\s\\S]*)\\1$/mg, '$2')\n\n    // Expand newlines if double quoted\n    if (maybeQuote === '\"') {\n      value = value.replace(/\\\\n/g, '\\n')\n      value = value.replace(/\\\\r/g, '\\r')\n    }\n\n    // Add to object\n    obj[key] = value\n  }\n\n  return obj\n}\n\nfunction _parseVault (options) {\n  options = options || {}\n\n  const vaultPath = _vaultPath(options)\n  options.path = vaultPath // parse .env.vault\n  const result = DotenvModule.configDotenv(options)\n  if (!result.parsed) {\n    const err = new Error(`MISSING_DATA: Cannot parse ${vaultPath} for an unknown reason`)\n    err.code = 'MISSING_DATA'\n    throw err\n  }\n\n  // handle scenario for comma separated keys - for use with key rotation\n  // example: DOTENV_KEY=\"dotenv://:<EMAIL>/vault/.env.vault?environment=prod,dotenv://:<EMAIL>/vault/.env.vault?environment=prod\"\n  const keys = _dotenvKey(options).split(',')\n  const length = keys.length\n\n  let decrypted\n  for (let i = 0; i < length; i++) {\n    try {\n      // Get full key\n      const key = keys[i].trim()\n\n      // Get instructions for decrypt\n      const attrs = _instructions(result, key)\n\n      // Decrypt\n      decrypted = DotenvModule.decrypt(attrs.ciphertext, attrs.key)\n\n      break\n    } catch (error) {\n      // last key\n      if (i + 1 >= length) {\n        throw error\n      }\n      // try next key\n    }\n  }\n\n  // Parse decrypted .env string\n  return DotenvModule.parse(decrypted)\n}\n\nfunction _warn (message) {\n  console.log(`[dotenv@${version}][WARN] ${message}`)\n}\n\nfunction _debug (message) {\n  console.log(`[dotenv@${version}][DEBUG] ${message}`)\n}\n\nfunction _log (message) {\n  console.log(`[dotenv@${version}] ${message}`)\n}\n\nfunction _dotenvKey (options) {\n  // prioritize developer directly setting options.DOTENV_KEY\n  if (options && options.DOTENV_KEY && options.DOTENV_KEY.length > 0) {\n    return options.DOTENV_KEY\n  }\n\n  // secondary infra already contains a DOTENV_KEY environment variable\n  if (process.env.DOTENV_KEY && process.env.DOTENV_KEY.length > 0) {\n    return process.env.DOTENV_KEY\n  }\n\n  // fallback to empty string\n  return ''\n}\n\nfunction _instructions (result, dotenvKey) {\n  // Parse DOTENV_KEY. Format is a URI\n  let uri\n  try {\n    uri = new URL(dotenvKey)\n  } catch (error) {\n    if (error.code === 'ERR_INVALID_URL') {\n      const err = new Error('INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development')\n      err.code = 'INVALID_DOTENV_KEY'\n      throw err\n    }\n\n    throw error\n  }\n\n  // Get decrypt key\n  const key = uri.password\n  if (!key) {\n    const err = new Error('INVALID_DOTENV_KEY: Missing key part')\n    err.code = 'INVALID_DOTENV_KEY'\n    throw err\n  }\n\n  // Get environment\n  const environment = uri.searchParams.get('environment')\n  if (!environment) {\n    const err = new Error('INVALID_DOTENV_KEY: Missing environment part')\n    err.code = 'INVALID_DOTENV_KEY'\n    throw err\n  }\n\n  // Get ciphertext payload\n  const environmentKey = `DOTENV_VAULT_${environment.toUpperCase()}`\n  const ciphertext = result.parsed[environmentKey] // DOTENV_VAULT_PRODUCTION\n  if (!ciphertext) {\n    const err = new Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${environmentKey} in your .env.vault file.`)\n    err.code = 'NOT_FOUND_DOTENV_ENVIRONMENT'\n    throw err\n  }\n\n  return { ciphertext, key }\n}\n\nfunction _vaultPath (options) {\n  let possibleVaultPath = null\n\n  if (options && options.path && options.path.length > 0) {\n    if (Array.isArray(options.path)) {\n      for (const filepath of options.path) {\n        if (fs.existsSync(filepath)) {\n          possibleVaultPath = filepath.endsWith('.vault') ? filepath : `${filepath}.vault`\n        }\n      }\n    } else {\n      possibleVaultPath = options.path.endsWith('.vault') ? options.path : `${options.path}.vault`\n    }\n  } else {\n    possibleVaultPath = path.resolve(process.cwd(), '.env.vault')\n  }\n\n  if (fs.existsSync(possibleVaultPath)) {\n    return possibleVaultPath\n  }\n\n  return null\n}\n\nfunction _resolveHome (envPath) {\n  return envPath[0] === '~' ? path.join(os.homedir(), envPath.slice(1)) : envPath\n}\n\nfunction _configVault (options) {\n  const debug = Boolean(options && options.debug)\n  const quiet = options && 'quiet' in options ? options.quiet : true\n\n  if (debug || !quiet) {\n    _log('Loading env from encrypted .env.vault')\n  }\n\n  const parsed = DotenvModule._parseVault(options)\n\n  let processEnv = process.env\n  if (options && options.processEnv != null) {\n    processEnv = options.processEnv\n  }\n\n  DotenvModule.populate(processEnv, parsed, options)\n\n  return { parsed }\n}\n\nfunction configDotenv (options) {\n  const dotenvPath = path.resolve(process.cwd(), '.env')\n  let encoding = 'utf8'\n  const debug = Boolean(options && options.debug)\n  const quiet = options && 'quiet' in options ? options.quiet : true\n\n  if (options && options.encoding) {\n    encoding = options.encoding\n  } else {\n    if (debug) {\n      _debug('No encoding is specified. UTF-8 is used by default')\n    }\n  }\n\n  let optionPaths = [dotenvPath] // default, look for .env\n  if (options && options.path) {\n    if (!Array.isArray(options.path)) {\n      optionPaths = [_resolveHome(options.path)]\n    } else {\n      optionPaths = [] // reset default\n      for (const filepath of options.path) {\n        optionPaths.push(_resolveHome(filepath))\n      }\n    }\n  }\n\n  // Build the parsed data in a temporary object (because we need to return it).  Once we have the final\n  // parsed data, we will combine it with process.env (or options.processEnv if provided).\n  let lastError\n  const parsedAll = {}\n  for (const path of optionPaths) {\n    try {\n      // Specifying an encoding returns a string instead of a buffer\n      const parsed = DotenvModule.parse(fs.readFileSync(path, { encoding }))\n\n      DotenvModule.populate(parsedAll, parsed, options)\n    } catch (e) {\n      if (debug) {\n        _debug(`Failed to load ${path} ${e.message}`)\n      }\n      lastError = e\n    }\n  }\n\n  let processEnv = process.env\n  if (options && options.processEnv != null) {\n    processEnv = options.processEnv\n  }\n\n  DotenvModule.populate(processEnv, parsedAll, options)\n\n  if (debug || !quiet) {\n    const keysCount = Object.keys(parsedAll).length\n    const shortPaths = []\n    for (const filePath of optionPaths) {\n      try {\n        const relative = path.relative(process.cwd(), filePath)\n        shortPaths.push(relative)\n      } catch (e) {\n        if (debug) {\n          _debug(`Failed to load ${filePath} ${e.message}`)\n        }\n        lastError = e\n      }\n    }\n\n    _log(`injecting env (${keysCount}) from ${shortPaths.join(',')}`)\n  }\n\n  if (lastError) {\n    return { parsed: parsedAll, error: lastError }\n  } else {\n    return { parsed: parsedAll }\n  }\n}\n\n// Populates process.env from .env file\nfunction config (options) {\n  // fallback to original dotenv if DOTENV_KEY is not set\n  if (_dotenvKey(options).length === 0) {\n    return DotenvModule.configDotenv(options)\n  }\n\n  const vaultPath = _vaultPath(options)\n\n  // dotenvKey exists but .env.vault file does not exist\n  if (!vaultPath) {\n    _warn(`You set DOTENV_KEY but you are missing a .env.vault file at ${vaultPath}. Did you forget to build it?`)\n\n    return DotenvModule.configDotenv(options)\n  }\n\n  return DotenvModule._configVault(options)\n}\n\nfunction decrypt (encrypted, keyStr) {\n  const key = Buffer.from(keyStr.slice(-64), 'hex')\n  let ciphertext = Buffer.from(encrypted, 'base64')\n\n  const nonce = ciphertext.subarray(0, 12)\n  const authTag = ciphertext.subarray(-16)\n  ciphertext = ciphertext.subarray(12, -16)\n\n  try {\n    const aesgcm = crypto.createDecipheriv('aes-256-gcm', key, nonce)\n    aesgcm.setAuthTag(authTag)\n    return `${aesgcm.update(ciphertext)}${aesgcm.final()}`\n  } catch (error) {\n    const isRange = error instanceof RangeError\n    const invalidKeyLength = error.message === 'Invalid key length'\n    const decryptionFailed = error.message === 'Unsupported state or unable to authenticate data'\n\n    if (isRange || invalidKeyLength) {\n      const err = new Error('INVALID_DOTENV_KEY: It must be 64 characters long (or more)')\n      err.code = 'INVALID_DOTENV_KEY'\n      throw err\n    } else if (decryptionFailed) {\n      const err = new Error('DECRYPTION_FAILED: Please check your DOTENV_KEY')\n      err.code = 'DECRYPTION_FAILED'\n      throw err\n    } else {\n      throw error\n    }\n  }\n}\n\n// Populate process.env with parsed values\nfunction populate (processEnv, parsed, options = {}) {\n  const debug = Boolean(options && options.debug)\n  const override = Boolean(options && options.override)\n\n  if (typeof parsed !== 'object') {\n    const err = new Error('OBJECT_REQUIRED: Please check the processEnv argument being passed to populate')\n    err.code = 'OBJECT_REQUIRED'\n    throw err\n  }\n\n  // Set process.env\n  for (const key of Object.keys(parsed)) {\n    if (Object.prototype.hasOwnProperty.call(processEnv, key)) {\n      if (override === true) {\n        processEnv[key] = parsed[key]\n      }\n\n      if (debug) {\n        if (override === true) {\n          _debug(`\"${key}\" is already defined and WAS overwritten`)\n        } else {\n          _debug(`\"${key}\" is already defined and was NOT overwritten`)\n        }\n      }\n    } else {\n      processEnv[key] = parsed[key]\n    }\n  }\n}\n\nconst DotenvModule = {\n  configDotenv,\n  _configVault,\n  _parseVault,\n  config,\n  decrypt,\n  parse,\n  populate\n}\n\nmodule.exports.configDotenv = DotenvModule.configDotenv\nmodule.exports._configVault = DotenvModule._configVault\nmodule.exports._parseVault = DotenvModule._parseVault\nmodule.exports.config = DotenvModule.config\nmodule.exports.decrypt = DotenvModule.decrypt\nmodule.exports.parse = DotenvModule.parse\nmodule.exports.populate = DotenvModule.populate\n\nmodule.exports = DotenvModule\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZG90ZW52L2xpYi9tYWluLmpzIiwibWFwcGluZ3MiOiJBQUFBLFdBQVcsbUJBQU8sQ0FBQyxjQUFJO0FBQ3ZCLGFBQWEsbUJBQU8sQ0FBQyxrQkFBTTtBQUMzQixXQUFXLG1CQUFPLENBQUMsY0FBSTtBQUN2QixlQUFlLG1CQUFPLENBQUMsc0JBQVE7QUFDL0Isb0JBQW9CLG1CQUFPLENBQUMsaUVBQWlCOztBQUU3Qzs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdEQUF3RCxXQUFXO0FBQ25FO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGtCQUFrQixZQUFZO0FBQzlCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EseUJBQXlCLFFBQVEsVUFBVSxRQUFRO0FBQ25EOztBQUVBO0FBQ0EseUJBQXlCLFFBQVEsV0FBVyxRQUFRO0FBQ3BEOztBQUVBO0FBQ0EseUJBQXlCLFFBQVEsSUFBSSxRQUFRO0FBQzdDOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EseUNBQXlDLDBCQUEwQjtBQUNuRTtBQUNBO0FBQ0EscUZBQXFGLGdCQUFnQjtBQUNyRztBQUNBO0FBQ0E7O0FBRUEsV0FBVztBQUNYOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwRUFBMEUsU0FBUztBQUNuRjtBQUNBO0FBQ0EsTUFBTTtBQUNOLDhFQUE4RSxhQUFhO0FBQzNGO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUEsV0FBVztBQUNYOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnRUFBZ0UsVUFBVTs7QUFFMUU7QUFDQSxNQUFNO0FBQ047QUFDQSxpQ0FBaUMsTUFBTSxFQUFFLFVBQVU7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQSxtQ0FBbUMsVUFBVSxFQUFFLFVBQVU7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsMkJBQTJCLFVBQVUsU0FBUyxxQkFBcUI7QUFDbkU7O0FBRUE7QUFDQSxhQUFhO0FBQ2IsSUFBSTtBQUNKLGFBQWE7QUFDYjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0EseUVBQXlFLFVBQVU7O0FBRW5GO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsMEJBQTBCLEVBQUUsZUFBZTtBQUN6RCxJQUFJO0FBQ0o7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLG1EQUFtRDtBQUNuRDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxxQkFBcUIsSUFBSTtBQUN6QixVQUFVO0FBQ1YscUJBQXFCLElBQUk7QUFDekI7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsMkJBQTJCO0FBQzNCLDJCQUEyQjtBQUMzQiwwQkFBMEI7QUFDMUIscUJBQXFCO0FBQ3JCLHNCQUFzQjtBQUN0QixvQkFBb0I7QUFDcEIsdUJBQXVCOztBQUV2QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBZG1pblxcRGVza3RvcFxcYmxvZ1xcdGFuZHByb1xcbm9kZV9tb2R1bGVzXFxkb3RlbnZcXGxpYlxcbWFpbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBmcyA9IHJlcXVpcmUoJ2ZzJylcbmNvbnN0IHBhdGggPSByZXF1aXJlKCdwYXRoJylcbmNvbnN0IG9zID0gcmVxdWlyZSgnb3MnKVxuY29uc3QgY3J5cHRvID0gcmVxdWlyZSgnY3J5cHRvJylcbmNvbnN0IHBhY2thZ2VKc29uID0gcmVxdWlyZSgnLi4vcGFja2FnZS5qc29uJylcblxuY29uc3QgdmVyc2lvbiA9IHBhY2thZ2VKc29uLnZlcnNpb25cblxuY29uc3QgTElORSA9IC8oPzpefF4pXFxzKig/OmV4cG9ydFxccyspPyhbXFx3Li1dKykoPzpcXHMqPVxccyo/fDpcXHMrPykoXFxzKicoPzpcXFxcJ3xbXiddKSonfFxccypcIig/OlxcXFxcInxbXlwiXSkqXCJ8XFxzKmAoPzpcXFxcYHxbXmBdKSpgfFteI1xcclxcbl0rKT9cXHMqKD86Iy4qKT8oPzokfCQpL21nXG5cbi8vIFBhcnNlIHNyYyBpbnRvIGFuIE9iamVjdFxuZnVuY3Rpb24gcGFyc2UgKHNyYykge1xuICBjb25zdCBvYmogPSB7fVxuXG4gIC8vIENvbnZlcnQgYnVmZmVyIHRvIHN0cmluZ1xuICBsZXQgbGluZXMgPSBzcmMudG9TdHJpbmcoKVxuXG4gIC8vIENvbnZlcnQgbGluZSBicmVha3MgdG8gc2FtZSBmb3JtYXRcbiAgbGluZXMgPSBsaW5lcy5yZXBsYWNlKC9cXHJcXG4/L21nLCAnXFxuJylcblxuICBsZXQgbWF0Y2hcbiAgd2hpbGUgKChtYXRjaCA9IExJTkUuZXhlYyhsaW5lcykpICE9IG51bGwpIHtcbiAgICBjb25zdCBrZXkgPSBtYXRjaFsxXVxuXG4gICAgLy8gRGVmYXVsdCB1bmRlZmluZWQgb3IgbnVsbCB0byBlbXB0eSBzdHJpbmdcbiAgICBsZXQgdmFsdWUgPSAobWF0Y2hbMl0gfHwgJycpXG5cbiAgICAvLyBSZW1vdmUgd2hpdGVzcGFjZVxuICAgIHZhbHVlID0gdmFsdWUudHJpbSgpXG5cbiAgICAvLyBDaGVjayBpZiBkb3VibGUgcXVvdGVkXG4gICAgY29uc3QgbWF5YmVRdW90ZSA9IHZhbHVlWzBdXG5cbiAgICAvLyBSZW1vdmUgc3Vycm91bmRpbmcgcXVvdGVzXG4gICAgdmFsdWUgPSB2YWx1ZS5yZXBsYWNlKC9eKFsnXCJgXSkoW1xcc1xcU10qKVxcMSQvbWcsICckMicpXG5cbiAgICAvLyBFeHBhbmQgbmV3bGluZXMgaWYgZG91YmxlIHF1b3RlZFxuICAgIGlmIChtYXliZVF1b3RlID09PSAnXCInKSB7XG4gICAgICB2YWx1ZSA9IHZhbHVlLnJlcGxhY2UoL1xcXFxuL2csICdcXG4nKVxuICAgICAgdmFsdWUgPSB2YWx1ZS5yZXBsYWNlKC9cXFxcci9nLCAnXFxyJylcbiAgICB9XG5cbiAgICAvLyBBZGQgdG8gb2JqZWN0XG4gICAgb2JqW2tleV0gPSB2YWx1ZVxuICB9XG5cbiAgcmV0dXJuIG9ialxufVxuXG5mdW5jdGlvbiBfcGFyc2VWYXVsdCAob3B0aW9ucykge1xuICBvcHRpb25zID0gb3B0aW9ucyB8fCB7fVxuXG4gIGNvbnN0IHZhdWx0UGF0aCA9IF92YXVsdFBhdGgob3B0aW9ucylcbiAgb3B0aW9ucy5wYXRoID0gdmF1bHRQYXRoIC8vIHBhcnNlIC5lbnYudmF1bHRcbiAgY29uc3QgcmVzdWx0ID0gRG90ZW52TW9kdWxlLmNvbmZpZ0RvdGVudihvcHRpb25zKVxuICBpZiAoIXJlc3VsdC5wYXJzZWQpIHtcbiAgICBjb25zdCBlcnIgPSBuZXcgRXJyb3IoYE1JU1NJTkdfREFUQTogQ2Fubm90IHBhcnNlICR7dmF1bHRQYXRofSBmb3IgYW4gdW5rbm93biByZWFzb25gKVxuICAgIGVyci5jb2RlID0gJ01JU1NJTkdfREFUQSdcbiAgICB0aHJvdyBlcnJcbiAgfVxuXG4gIC8vIGhhbmRsZSBzY2VuYXJpbyBmb3IgY29tbWEgc2VwYXJhdGVkIGtleXMgLSBmb3IgdXNlIHdpdGgga2V5IHJvdGF0aW9uXG4gIC8vIGV4YW1wbGU6IERPVEVOVl9LRVk9XCJkb3RlbnY6Ly86a2V5XzEyMzRAZG90ZW52eC5jb20vdmF1bHQvLmVudi52YXVsdD9lbnZpcm9ubWVudD1wcm9kLGRvdGVudjovLzprZXlfNzg5MEBkb3RlbnZ4LmNvbS92YXVsdC8uZW52LnZhdWx0P2Vudmlyb25tZW50PXByb2RcIlxuICBjb25zdCBrZXlzID0gX2RvdGVudktleShvcHRpb25zKS5zcGxpdCgnLCcpXG4gIGNvbnN0IGxlbmd0aCA9IGtleXMubGVuZ3RoXG5cbiAgbGV0IGRlY3J5cHRlZFxuICBmb3IgKGxldCBpID0gMDsgaSA8IGxlbmd0aDsgaSsrKSB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIEdldCBmdWxsIGtleVxuICAgICAgY29uc3Qga2V5ID0ga2V5c1tpXS50cmltKClcblxuICAgICAgLy8gR2V0IGluc3RydWN0aW9ucyBmb3IgZGVjcnlwdFxuICAgICAgY29uc3QgYXR0cnMgPSBfaW5zdHJ1Y3Rpb25zKHJlc3VsdCwga2V5KVxuXG4gICAgICAvLyBEZWNyeXB0XG4gICAgICBkZWNyeXB0ZWQgPSBEb3RlbnZNb2R1bGUuZGVjcnlwdChhdHRycy5jaXBoZXJ0ZXh0LCBhdHRycy5rZXkpXG5cbiAgICAgIGJyZWFrXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIC8vIGxhc3Qga2V5XG4gICAgICBpZiAoaSArIDEgPj0gbGVuZ3RoKSB7XG4gICAgICAgIHRocm93IGVycm9yXG4gICAgICB9XG4gICAgICAvLyB0cnkgbmV4dCBrZXlcbiAgICB9XG4gIH1cblxuICAvLyBQYXJzZSBkZWNyeXB0ZWQgLmVudiBzdHJpbmdcbiAgcmV0dXJuIERvdGVudk1vZHVsZS5wYXJzZShkZWNyeXB0ZWQpXG59XG5cbmZ1bmN0aW9uIF93YXJuIChtZXNzYWdlKSB7XG4gIGNvbnNvbGUubG9nKGBbZG90ZW52QCR7dmVyc2lvbn1dW1dBUk5dICR7bWVzc2FnZX1gKVxufVxuXG5mdW5jdGlvbiBfZGVidWcgKG1lc3NhZ2UpIHtcbiAgY29uc29sZS5sb2coYFtkb3RlbnZAJHt2ZXJzaW9ufV1bREVCVUddICR7bWVzc2FnZX1gKVxufVxuXG5mdW5jdGlvbiBfbG9nIChtZXNzYWdlKSB7XG4gIGNvbnNvbGUubG9nKGBbZG90ZW52QCR7dmVyc2lvbn1dICR7bWVzc2FnZX1gKVxufVxuXG5mdW5jdGlvbiBfZG90ZW52S2V5IChvcHRpb25zKSB7XG4gIC8vIHByaW9yaXRpemUgZGV2ZWxvcGVyIGRpcmVjdGx5IHNldHRpbmcgb3B0aW9ucy5ET1RFTlZfS0VZXG4gIGlmIChvcHRpb25zICYmIG9wdGlvbnMuRE9URU5WX0tFWSAmJiBvcHRpb25zLkRPVEVOVl9LRVkubGVuZ3RoID4gMCkge1xuICAgIHJldHVybiBvcHRpb25zLkRPVEVOVl9LRVlcbiAgfVxuXG4gIC8vIHNlY29uZGFyeSBpbmZyYSBhbHJlYWR5IGNvbnRhaW5zIGEgRE9URU5WX0tFWSBlbnZpcm9ubWVudCB2YXJpYWJsZVxuICBpZiAocHJvY2Vzcy5lbnYuRE9URU5WX0tFWSAmJiBwcm9jZXNzLmVudi5ET1RFTlZfS0VZLmxlbmd0aCA+IDApIHtcbiAgICByZXR1cm4gcHJvY2Vzcy5lbnYuRE9URU5WX0tFWVxuICB9XG5cbiAgLy8gZmFsbGJhY2sgdG8gZW1wdHkgc3RyaW5nXG4gIHJldHVybiAnJ1xufVxuXG5mdW5jdGlvbiBfaW5zdHJ1Y3Rpb25zIChyZXN1bHQsIGRvdGVudktleSkge1xuICAvLyBQYXJzZSBET1RFTlZfS0VZLiBGb3JtYXQgaXMgYSBVUklcbiAgbGV0IHVyaVxuICB0cnkge1xuICAgIHVyaSA9IG5ldyBVUkwoZG90ZW52S2V5KVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGlmIChlcnJvci5jb2RlID09PSAnRVJSX0lOVkFMSURfVVJMJykge1xuICAgICAgY29uc3QgZXJyID0gbmV3IEVycm9yKCdJTlZBTElEX0RPVEVOVl9LRVk6IFdyb25nIGZvcm1hdC4gTXVzdCBiZSBpbiB2YWxpZCB1cmkgZm9ybWF0IGxpa2UgZG90ZW52Oi8vOmtleV8xMjM0QGRvdGVudnguY29tL3ZhdWx0Ly5lbnYudmF1bHQ/ZW52aXJvbm1lbnQ9ZGV2ZWxvcG1lbnQnKVxuICAgICAgZXJyLmNvZGUgPSAnSU5WQUxJRF9ET1RFTlZfS0VZJ1xuICAgICAgdGhyb3cgZXJyXG4gICAgfVxuXG4gICAgdGhyb3cgZXJyb3JcbiAgfVxuXG4gIC8vIEdldCBkZWNyeXB0IGtleVxuICBjb25zdCBrZXkgPSB1cmkucGFzc3dvcmRcbiAgaWYgKCFrZXkpIHtcbiAgICBjb25zdCBlcnIgPSBuZXcgRXJyb3IoJ0lOVkFMSURfRE9URU5WX0tFWTogTWlzc2luZyBrZXkgcGFydCcpXG4gICAgZXJyLmNvZGUgPSAnSU5WQUxJRF9ET1RFTlZfS0VZJ1xuICAgIHRocm93IGVyclxuICB9XG5cbiAgLy8gR2V0IGVudmlyb25tZW50XG4gIGNvbnN0IGVudmlyb25tZW50ID0gdXJpLnNlYXJjaFBhcmFtcy5nZXQoJ2Vudmlyb25tZW50JylcbiAgaWYgKCFlbnZpcm9ubWVudCkge1xuICAgIGNvbnN0IGVyciA9IG5ldyBFcnJvcignSU5WQUxJRF9ET1RFTlZfS0VZOiBNaXNzaW5nIGVudmlyb25tZW50IHBhcnQnKVxuICAgIGVyci5jb2RlID0gJ0lOVkFMSURfRE9URU5WX0tFWSdcbiAgICB0aHJvdyBlcnJcbiAgfVxuXG4gIC8vIEdldCBjaXBoZXJ0ZXh0IHBheWxvYWRcbiAgY29uc3QgZW52aXJvbm1lbnRLZXkgPSBgRE9URU5WX1ZBVUxUXyR7ZW52aXJvbm1lbnQudG9VcHBlckNhc2UoKX1gXG4gIGNvbnN0IGNpcGhlcnRleHQgPSByZXN1bHQucGFyc2VkW2Vudmlyb25tZW50S2V5XSAvLyBET1RFTlZfVkFVTFRfUFJPRFVDVElPTlxuICBpZiAoIWNpcGhlcnRleHQpIHtcbiAgICBjb25zdCBlcnIgPSBuZXcgRXJyb3IoYE5PVF9GT1VORF9ET1RFTlZfRU5WSVJPTk1FTlQ6IENhbm5vdCBsb2NhdGUgZW52aXJvbm1lbnQgJHtlbnZpcm9ubWVudEtleX0gaW4geW91ciAuZW52LnZhdWx0IGZpbGUuYClcbiAgICBlcnIuY29kZSA9ICdOT1RfRk9VTkRfRE9URU5WX0VOVklST05NRU5UJ1xuICAgIHRocm93IGVyclxuICB9XG5cbiAgcmV0dXJuIHsgY2lwaGVydGV4dCwga2V5IH1cbn1cblxuZnVuY3Rpb24gX3ZhdWx0UGF0aCAob3B0aW9ucykge1xuICBsZXQgcG9zc2libGVWYXVsdFBhdGggPSBudWxsXG5cbiAgaWYgKG9wdGlvbnMgJiYgb3B0aW9ucy5wYXRoICYmIG9wdGlvbnMucGF0aC5sZW5ndGggPiAwKSB7XG4gICAgaWYgKEFycmF5LmlzQXJyYXkob3B0aW9ucy5wYXRoKSkge1xuICAgICAgZm9yIChjb25zdCBmaWxlcGF0aCBvZiBvcHRpb25zLnBhdGgpIHtcbiAgICAgICAgaWYgKGZzLmV4aXN0c1N5bmMoZmlsZXBhdGgpKSB7XG4gICAgICAgICAgcG9zc2libGVWYXVsdFBhdGggPSBmaWxlcGF0aC5lbmRzV2l0aCgnLnZhdWx0JykgPyBmaWxlcGF0aCA6IGAke2ZpbGVwYXRofS52YXVsdGBcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICBwb3NzaWJsZVZhdWx0UGF0aCA9IG9wdGlvbnMucGF0aC5lbmRzV2l0aCgnLnZhdWx0JykgPyBvcHRpb25zLnBhdGggOiBgJHtvcHRpb25zLnBhdGh9LnZhdWx0YFxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICBwb3NzaWJsZVZhdWx0UGF0aCA9IHBhdGgucmVzb2x2ZShwcm9jZXNzLmN3ZCgpLCAnLmVudi52YXVsdCcpXG4gIH1cblxuICBpZiAoZnMuZXhpc3RzU3luYyhwb3NzaWJsZVZhdWx0UGF0aCkpIHtcbiAgICByZXR1cm4gcG9zc2libGVWYXVsdFBhdGhcbiAgfVxuXG4gIHJldHVybiBudWxsXG59XG5cbmZ1bmN0aW9uIF9yZXNvbHZlSG9tZSAoZW52UGF0aCkge1xuICByZXR1cm4gZW52UGF0aFswXSA9PT0gJ34nID8gcGF0aC5qb2luKG9zLmhvbWVkaXIoKSwgZW52UGF0aC5zbGljZSgxKSkgOiBlbnZQYXRoXG59XG5cbmZ1bmN0aW9uIF9jb25maWdWYXVsdCAob3B0aW9ucykge1xuICBjb25zdCBkZWJ1ZyA9IEJvb2xlYW4ob3B0aW9ucyAmJiBvcHRpb25zLmRlYnVnKVxuICBjb25zdCBxdWlldCA9IG9wdGlvbnMgJiYgJ3F1aWV0JyBpbiBvcHRpb25zID8gb3B0aW9ucy5xdWlldCA6IHRydWVcblxuICBpZiAoZGVidWcgfHwgIXF1aWV0KSB7XG4gICAgX2xvZygnTG9hZGluZyBlbnYgZnJvbSBlbmNyeXB0ZWQgLmVudi52YXVsdCcpXG4gIH1cblxuICBjb25zdCBwYXJzZWQgPSBEb3RlbnZNb2R1bGUuX3BhcnNlVmF1bHQob3B0aW9ucylcblxuICBsZXQgcHJvY2Vzc0VudiA9IHByb2Nlc3MuZW52XG4gIGlmIChvcHRpb25zICYmIG9wdGlvbnMucHJvY2Vzc0VudiAhPSBudWxsKSB7XG4gICAgcHJvY2Vzc0VudiA9IG9wdGlvbnMucHJvY2Vzc0VudlxuICB9XG5cbiAgRG90ZW52TW9kdWxlLnBvcHVsYXRlKHByb2Nlc3NFbnYsIHBhcnNlZCwgb3B0aW9ucylcblxuICByZXR1cm4geyBwYXJzZWQgfVxufVxuXG5mdW5jdGlvbiBjb25maWdEb3RlbnYgKG9wdGlvbnMpIHtcbiAgY29uc3QgZG90ZW52UGF0aCA9IHBhdGgucmVzb2x2ZShwcm9jZXNzLmN3ZCgpLCAnLmVudicpXG4gIGxldCBlbmNvZGluZyA9ICd1dGY4J1xuICBjb25zdCBkZWJ1ZyA9IEJvb2xlYW4ob3B0aW9ucyAmJiBvcHRpb25zLmRlYnVnKVxuICBjb25zdCBxdWlldCA9IG9wdGlvbnMgJiYgJ3F1aWV0JyBpbiBvcHRpb25zID8gb3B0aW9ucy5xdWlldCA6IHRydWVcblxuICBpZiAob3B0aW9ucyAmJiBvcHRpb25zLmVuY29kaW5nKSB7XG4gICAgZW5jb2RpbmcgPSBvcHRpb25zLmVuY29kaW5nXG4gIH0gZWxzZSB7XG4gICAgaWYgKGRlYnVnKSB7XG4gICAgICBfZGVidWcoJ05vIGVuY29kaW5nIGlzIHNwZWNpZmllZC4gVVRGLTggaXMgdXNlZCBieSBkZWZhdWx0JylcbiAgICB9XG4gIH1cblxuICBsZXQgb3B0aW9uUGF0aHMgPSBbZG90ZW52UGF0aF0gLy8gZGVmYXVsdCwgbG9vayBmb3IgLmVudlxuICBpZiAob3B0aW9ucyAmJiBvcHRpb25zLnBhdGgpIHtcbiAgICBpZiAoIUFycmF5LmlzQXJyYXkob3B0aW9ucy5wYXRoKSkge1xuICAgICAgb3B0aW9uUGF0aHMgPSBbX3Jlc29sdmVIb21lKG9wdGlvbnMucGF0aCldXG4gICAgfSBlbHNlIHtcbiAgICAgIG9wdGlvblBhdGhzID0gW10gLy8gcmVzZXQgZGVmYXVsdFxuICAgICAgZm9yIChjb25zdCBmaWxlcGF0aCBvZiBvcHRpb25zLnBhdGgpIHtcbiAgICAgICAgb3B0aW9uUGF0aHMucHVzaChfcmVzb2x2ZUhvbWUoZmlsZXBhdGgpKVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8vIEJ1aWxkIHRoZSBwYXJzZWQgZGF0YSBpbiBhIHRlbXBvcmFyeSBvYmplY3QgKGJlY2F1c2Ugd2UgbmVlZCB0byByZXR1cm4gaXQpLiAgT25jZSB3ZSBoYXZlIHRoZSBmaW5hbFxuICAvLyBwYXJzZWQgZGF0YSwgd2Ugd2lsbCBjb21iaW5lIGl0IHdpdGggcHJvY2Vzcy5lbnYgKG9yIG9wdGlvbnMucHJvY2Vzc0VudiBpZiBwcm92aWRlZCkuXG4gIGxldCBsYXN0RXJyb3JcbiAgY29uc3QgcGFyc2VkQWxsID0ge31cbiAgZm9yIChjb25zdCBwYXRoIG9mIG9wdGlvblBhdGhzKSB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIFNwZWNpZnlpbmcgYW4gZW5jb2RpbmcgcmV0dXJucyBhIHN0cmluZyBpbnN0ZWFkIG9mIGEgYnVmZmVyXG4gICAgICBjb25zdCBwYXJzZWQgPSBEb3RlbnZNb2R1bGUucGFyc2UoZnMucmVhZEZpbGVTeW5jKHBhdGgsIHsgZW5jb2RpbmcgfSkpXG5cbiAgICAgIERvdGVudk1vZHVsZS5wb3B1bGF0ZShwYXJzZWRBbGwsIHBhcnNlZCwgb3B0aW9ucylcbiAgICB9IGNhdGNoIChlKSB7XG4gICAgICBpZiAoZGVidWcpIHtcbiAgICAgICAgX2RlYnVnKGBGYWlsZWQgdG8gbG9hZCAke3BhdGh9ICR7ZS5tZXNzYWdlfWApXG4gICAgICB9XG4gICAgICBsYXN0RXJyb3IgPSBlXG4gICAgfVxuICB9XG5cbiAgbGV0IHByb2Nlc3NFbnYgPSBwcm9jZXNzLmVudlxuICBpZiAob3B0aW9ucyAmJiBvcHRpb25zLnByb2Nlc3NFbnYgIT0gbnVsbCkge1xuICAgIHByb2Nlc3NFbnYgPSBvcHRpb25zLnByb2Nlc3NFbnZcbiAgfVxuXG4gIERvdGVudk1vZHVsZS5wb3B1bGF0ZShwcm9jZXNzRW52LCBwYXJzZWRBbGwsIG9wdGlvbnMpXG5cbiAgaWYgKGRlYnVnIHx8ICFxdWlldCkge1xuICAgIGNvbnN0IGtleXNDb3VudCA9IE9iamVjdC5rZXlzKHBhcnNlZEFsbCkubGVuZ3RoXG4gICAgY29uc3Qgc2hvcnRQYXRocyA9IFtdXG4gICAgZm9yIChjb25zdCBmaWxlUGF0aCBvZiBvcHRpb25QYXRocykge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgcmVsYXRpdmUgPSBwYXRoLnJlbGF0aXZlKHByb2Nlc3MuY3dkKCksIGZpbGVQYXRoKVxuICAgICAgICBzaG9ydFBhdGhzLnB1c2gocmVsYXRpdmUpXG4gICAgICB9IGNhdGNoIChlKSB7XG4gICAgICAgIGlmIChkZWJ1Zykge1xuICAgICAgICAgIF9kZWJ1ZyhgRmFpbGVkIHRvIGxvYWQgJHtmaWxlUGF0aH0gJHtlLm1lc3NhZ2V9YClcbiAgICAgICAgfVxuICAgICAgICBsYXN0RXJyb3IgPSBlXG4gICAgICB9XG4gICAgfVxuXG4gICAgX2xvZyhgaW5qZWN0aW5nIGVudiAoJHtrZXlzQ291bnR9KSBmcm9tICR7c2hvcnRQYXRocy5qb2luKCcsJyl9YClcbiAgfVxuXG4gIGlmIChsYXN0RXJyb3IpIHtcbiAgICByZXR1cm4geyBwYXJzZWQ6IHBhcnNlZEFsbCwgZXJyb3I6IGxhc3RFcnJvciB9XG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuIHsgcGFyc2VkOiBwYXJzZWRBbGwgfVxuICB9XG59XG5cbi8vIFBvcHVsYXRlcyBwcm9jZXNzLmVudiBmcm9tIC5lbnYgZmlsZVxuZnVuY3Rpb24gY29uZmlnIChvcHRpb25zKSB7XG4gIC8vIGZhbGxiYWNrIHRvIG9yaWdpbmFsIGRvdGVudiBpZiBET1RFTlZfS0VZIGlzIG5vdCBzZXRcbiAgaWYgKF9kb3RlbnZLZXkob3B0aW9ucykubGVuZ3RoID09PSAwKSB7XG4gICAgcmV0dXJuIERvdGVudk1vZHVsZS5jb25maWdEb3RlbnYob3B0aW9ucylcbiAgfVxuXG4gIGNvbnN0IHZhdWx0UGF0aCA9IF92YXVsdFBhdGgob3B0aW9ucylcblxuICAvLyBkb3RlbnZLZXkgZXhpc3RzIGJ1dCAuZW52LnZhdWx0IGZpbGUgZG9lcyBub3QgZXhpc3RcbiAgaWYgKCF2YXVsdFBhdGgpIHtcbiAgICBfd2FybihgWW91IHNldCBET1RFTlZfS0VZIGJ1dCB5b3UgYXJlIG1pc3NpbmcgYSAuZW52LnZhdWx0IGZpbGUgYXQgJHt2YXVsdFBhdGh9LiBEaWQgeW91IGZvcmdldCB0byBidWlsZCBpdD9gKVxuXG4gICAgcmV0dXJuIERvdGVudk1vZHVsZS5jb25maWdEb3RlbnYob3B0aW9ucylcbiAgfVxuXG4gIHJldHVybiBEb3RlbnZNb2R1bGUuX2NvbmZpZ1ZhdWx0KG9wdGlvbnMpXG59XG5cbmZ1bmN0aW9uIGRlY3J5cHQgKGVuY3J5cHRlZCwga2V5U3RyKSB7XG4gIGNvbnN0IGtleSA9IEJ1ZmZlci5mcm9tKGtleVN0ci5zbGljZSgtNjQpLCAnaGV4JylcbiAgbGV0IGNpcGhlcnRleHQgPSBCdWZmZXIuZnJvbShlbmNyeXB0ZWQsICdiYXNlNjQnKVxuXG4gIGNvbnN0IG5vbmNlID0gY2lwaGVydGV4dC5zdWJhcnJheSgwLCAxMilcbiAgY29uc3QgYXV0aFRhZyA9IGNpcGhlcnRleHQuc3ViYXJyYXkoLTE2KVxuICBjaXBoZXJ0ZXh0ID0gY2lwaGVydGV4dC5zdWJhcnJheSgxMiwgLTE2KVxuXG4gIHRyeSB7XG4gICAgY29uc3QgYWVzZ2NtID0gY3J5cHRvLmNyZWF0ZURlY2lwaGVyaXYoJ2Flcy0yNTYtZ2NtJywga2V5LCBub25jZSlcbiAgICBhZXNnY20uc2V0QXV0aFRhZyhhdXRoVGFnKVxuICAgIHJldHVybiBgJHthZXNnY20udXBkYXRlKGNpcGhlcnRleHQpfSR7YWVzZ2NtLmZpbmFsKCl9YFxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnN0IGlzUmFuZ2UgPSBlcnJvciBpbnN0YW5jZW9mIFJhbmdlRXJyb3JcbiAgICBjb25zdCBpbnZhbGlkS2V5TGVuZ3RoID0gZXJyb3IubWVzc2FnZSA9PT0gJ0ludmFsaWQga2V5IGxlbmd0aCdcbiAgICBjb25zdCBkZWNyeXB0aW9uRmFpbGVkID0gZXJyb3IubWVzc2FnZSA9PT0gJ1Vuc3VwcG9ydGVkIHN0YXRlIG9yIHVuYWJsZSB0byBhdXRoZW50aWNhdGUgZGF0YSdcblxuICAgIGlmIChpc1JhbmdlIHx8IGludmFsaWRLZXlMZW5ndGgpIHtcbiAgICAgIGNvbnN0IGVyciA9IG5ldyBFcnJvcignSU5WQUxJRF9ET1RFTlZfS0VZOiBJdCBtdXN0IGJlIDY0IGNoYXJhY3RlcnMgbG9uZyAob3IgbW9yZSknKVxuICAgICAgZXJyLmNvZGUgPSAnSU5WQUxJRF9ET1RFTlZfS0VZJ1xuICAgICAgdGhyb3cgZXJyXG4gICAgfSBlbHNlIGlmIChkZWNyeXB0aW9uRmFpbGVkKSB7XG4gICAgICBjb25zdCBlcnIgPSBuZXcgRXJyb3IoJ0RFQ1JZUFRJT05fRkFJTEVEOiBQbGVhc2UgY2hlY2sgeW91ciBET1RFTlZfS0VZJylcbiAgICAgIGVyci5jb2RlID0gJ0RFQ1JZUFRJT05fRkFJTEVEJ1xuICAgICAgdGhyb3cgZXJyXG4gICAgfSBlbHNlIHtcbiAgICAgIHRocm93IGVycm9yXG4gICAgfVxuICB9XG59XG5cbi8vIFBvcHVsYXRlIHByb2Nlc3MuZW52IHdpdGggcGFyc2VkIHZhbHVlc1xuZnVuY3Rpb24gcG9wdWxhdGUgKHByb2Nlc3NFbnYsIHBhcnNlZCwgb3B0aW9ucyA9IHt9KSB7XG4gIGNvbnN0IGRlYnVnID0gQm9vbGVhbihvcHRpb25zICYmIG9wdGlvbnMuZGVidWcpXG4gIGNvbnN0IG92ZXJyaWRlID0gQm9vbGVhbihvcHRpb25zICYmIG9wdGlvbnMub3ZlcnJpZGUpXG5cbiAgaWYgKHR5cGVvZiBwYXJzZWQgIT09ICdvYmplY3QnKSB7XG4gICAgY29uc3QgZXJyID0gbmV3IEVycm9yKCdPQkpFQ1RfUkVRVUlSRUQ6IFBsZWFzZSBjaGVjayB0aGUgcHJvY2Vzc0VudiBhcmd1bWVudCBiZWluZyBwYXNzZWQgdG8gcG9wdWxhdGUnKVxuICAgIGVyci5jb2RlID0gJ09CSkVDVF9SRVFVSVJFRCdcbiAgICB0aHJvdyBlcnJcbiAgfVxuXG4gIC8vIFNldCBwcm9jZXNzLmVudlxuICBmb3IgKGNvbnN0IGtleSBvZiBPYmplY3Qua2V5cyhwYXJzZWQpKSB7XG4gICAgaWYgKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChwcm9jZXNzRW52LCBrZXkpKSB7XG4gICAgICBpZiAob3ZlcnJpZGUgPT09IHRydWUpIHtcbiAgICAgICAgcHJvY2Vzc0VudltrZXldID0gcGFyc2VkW2tleV1cbiAgICAgIH1cblxuICAgICAgaWYgKGRlYnVnKSB7XG4gICAgICAgIGlmIChvdmVycmlkZSA9PT0gdHJ1ZSkge1xuICAgICAgICAgIF9kZWJ1ZyhgXCIke2tleX1cIiBpcyBhbHJlYWR5IGRlZmluZWQgYW5kIFdBUyBvdmVyd3JpdHRlbmApXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgX2RlYnVnKGBcIiR7a2V5fVwiIGlzIGFscmVhZHkgZGVmaW5lZCBhbmQgd2FzIE5PVCBvdmVyd3JpdHRlbmApXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgcHJvY2Vzc0VudltrZXldID0gcGFyc2VkW2tleV1cbiAgICB9XG4gIH1cbn1cblxuY29uc3QgRG90ZW52TW9kdWxlID0ge1xuICBjb25maWdEb3RlbnYsXG4gIF9jb25maWdWYXVsdCxcbiAgX3BhcnNlVmF1bHQsXG4gIGNvbmZpZyxcbiAgZGVjcnlwdCxcbiAgcGFyc2UsXG4gIHBvcHVsYXRlXG59XG5cbm1vZHVsZS5leHBvcnRzLmNvbmZpZ0RvdGVudiA9IERvdGVudk1vZHVsZS5jb25maWdEb3RlbnZcbm1vZHVsZS5leHBvcnRzLl9jb25maWdWYXVsdCA9IERvdGVudk1vZHVsZS5fY29uZmlnVmF1bHRcbm1vZHVsZS5leHBvcnRzLl9wYXJzZVZhdWx0ID0gRG90ZW52TW9kdWxlLl9wYXJzZVZhdWx0XG5tb2R1bGUuZXhwb3J0cy5jb25maWcgPSBEb3RlbnZNb2R1bGUuY29uZmlnXG5tb2R1bGUuZXhwb3J0cy5kZWNyeXB0ID0gRG90ZW52TW9kdWxlLmRlY3J5cHRcbm1vZHVsZS5leHBvcnRzLnBhcnNlID0gRG90ZW52TW9kdWxlLnBhcnNlXG5tb2R1bGUuZXhwb3J0cy5wb3B1bGF0ZSA9IERvdGVudk1vZHVsZS5wb3B1bGF0ZVxuXG5tb2R1bGUuZXhwb3J0cyA9IERvdGVudk1vZHVsZVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dotenv/lib/main.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dotenv/package.json":
/*!******************************************!*\
  !*** ./node_modules/dotenv/package.json ***!
  \******************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"name":"dotenv","version":"16.6.1","description":"Loads environment variables from .env file","main":"lib/main.js","types":"lib/main.d.ts","exports":{".":{"types":"./lib/main.d.ts","require":"./lib/main.js","default":"./lib/main.js"},"./config":"./config.js","./config.js":"./config.js","./lib/env-options":"./lib/env-options.js","./lib/env-options.js":"./lib/env-options.js","./lib/cli-options":"./lib/cli-options.js","./lib/cli-options.js":"./lib/cli-options.js","./package.json":"./package.json"},"scripts":{"dts-check":"tsc --project tests/types/tsconfig.json","lint":"standard","pretest":"npm run lint && npm run dts-check","test":"tap run --allow-empty-coverage --disable-coverage --timeout=60000","test:coverage":"tap run --show-full-coverage --timeout=60000 --coverage-report=text --coverage-report=lcov","prerelease":"npm test","release":"standard-version"},"repository":{"type":"git","url":"git://github.com/motdotla/dotenv.git"},"homepage":"https://github.com/motdotla/dotenv#readme","funding":"https://dotenvx.com","keywords":["dotenv","env",".env","environment","variables","config","settings"],"readmeFilename":"README.md","license":"BSD-2-Clause","devDependencies":{"@types/node":"^18.11.3","decache":"^4.6.2","sinon":"^14.0.1","standard":"^17.0.0","standard-version":"^9.5.0","tap":"^19.2.0","typescript":"^4.8.4"},"engines":{"node":">=12"},"browser":{"fs":false}}');

/***/ })

};
;