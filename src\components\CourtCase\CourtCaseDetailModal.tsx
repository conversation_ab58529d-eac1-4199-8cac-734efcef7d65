"use client";

import { CourtCaseItemType } from "@/schemaValidations/courtCase.schema";
import { formatDate } from "@/utils/formatters";
import { usePermissions } from "@/hooks/usePermissions";
import { useState, useEffect } from "react";
import { Edit, Trash2 } from "lucide-react";
import customFieldApiRequest, { CustomField } from "@/apiRequests/customField";
import fieldConfigurationApiRequest from "@/apiRequests/fieldConfiguration";
import dateCountdownApiRequest, { DateCountdown } from "@/apiRequests/dateCountdown";
import DateCountdownBadge from "@/components/DateCountdown/DateCountdownBadge";
import { toast } from "react-toastify";

interface CourtCaseDetailModalProps {
  courtCase: CourtCaseItemType;
  onClose: () => void;
  onEdit: (courtCase: CourtCaseItemType) => void;
  onDelete: (caseId: string) => void;
}

const CourtCaseDetailModal: React.FC<CourtCaseDetailModalProps> = ({
  courtCase,
  onClose,
  onEdit,
  onDelete
}) => {
  const { hasPermission } = usePermissions();
  const [allFields, setAllFields] = useState<any[]>([]);
  const [countdowns, setCountdowns] = useState<DateCountdown[]>([]);
  const [loading, setLoading] = useState(true);

  // Default fields configuration
  const DEFAULT_FIELDS = [
    { name: 'stt', label: 'STT', dataType: 'number', required: true },
    { name: 'soVanThu', label: 'Số văn thư', dataType: 'text', required: false },
    { name: 'ngayNhanVanThu', label: 'Ngày nhận văn thư', dataType: 'date', required: false },
    { name: 'loaiAn', label: 'Loại án', dataType: 'text', required: false },
    { name: 'soThuLy', label: 'Số thụ lý', dataType: 'text', required: false },
    { name: 'ngayThuLy', label: 'Ngày thụ lý', dataType: 'date', required: false },
    { name: 'tand', label: 'TAND', dataType: 'text', required: false },
    { name: 'soBanAn', label: 'Số bản án/quyết định', dataType: 'text', required: false },
    { name: 'ngayBanHanh', label: 'Ngày ban hành', dataType: 'date', required: false },
    { name: 'biCaoNguoiKhieuKien', label: 'Bị cáo/Nguyên đơn/Người khiếu kiện', dataType: 'textarea', required: false },
    { name: 'toiDanhNoiDung', label: 'Tội danh/Bồi dưỡng/Nội dung khiếu kiện', dataType: 'textarea', required: false },
    { name: 'quanHePhatLuat', label: 'Tội danh/Quan hệ pháp luật', dataType: 'textarea', required: false },
    { name: 'hinhThucXuLy', label: 'Hình thức xử lý', dataType: 'text', required: false },
    { name: 'thuTucApDung', label: 'Thủ tục áp dụng', dataType: 'text', required: false },
    { name: 'thamPhanPhuTrach', label: 'Thẩm phán phụ trách', dataType: 'text', required: false },
    { name: 'truongPhoPhongKTNV', label: 'Trưởng/Phó phòng KTNV/Thẩm tra viên', dataType: 'text', required: false },
    { name: 'trangThaiGiaiQuyet', label: 'Trạng thái giải quyết', dataType: 'select', required: false },
    { name: 'ghiChu', label: 'Ghi chú', dataType: 'textarea', required: false },
    { name: 'ghiChuKetQua', label: 'Ghi chú kết quả', dataType: 'textarea', required: false }
  ];

  useEffect(() => {
    fetchFieldsAndCountdowns();
  }, []);

  const fetchFieldsAndCountdowns = async () => {
    try {
      setLoading(true);
      const sessionToken = localStorage.getItem("sessionToken") || "";

      // Fetch custom fields, field configurations, and countdowns
      const [customFieldsResult, configResult, countdownsResult] = await Promise.all([
        customFieldApiRequest.getCustomFields("CourtCase", sessionToken),
        fieldConfigurationApiRequest.getFieldConfiguration("CourtCase", sessionToken),
        dateCountdownApiRequest.getDateCountdowns("CourtCase", sessionToken)
      ]);

      if (customFieldsResult.payload.success && configResult.payload.success) {
        const fieldConfigs = configResult.payload.configuration.fieldConfigs;
        const combined: any[] = [];

        // Add default fields with configuration
        DEFAULT_FIELDS.forEach((defaultField) => {
          const config = fieldConfigs.find(fc => fc.fieldName === defaultField.name);
          combined.push({
            ...defaultField,
            _id: `default_${defaultField.name}`,
            isDefault: true,
            config: {
              showInList: config?.showInList ?? true,
              showInDetail: config?.showInDetail ?? true,
              columnWidth: config?.columnWidth ?? 150,
              sortOrder: config?.sortOrder ?? 0,
              required: defaultField.required,
              options: defaultField.options || []
            }
          });
        });

        // Add custom fields with configuration
        customFieldsResult.payload.fields.forEach((customField: CustomField) => {
          const config = fieldConfigs.find(fc => fc.fieldName === customField.name);
          combined.push({
            ...customField,
            isDefault: false,
            config: {
              ...customField.config,
              showInDetail: config?.showInDetail ?? true,
              columnWidth: config?.columnWidth ?? customField.config.columnWidth,
              sortOrder: config?.sortOrder ?? customField.config.sortOrder
            }
          });
        });

        // Sort by sortOrder
        combined.sort((a, b) => (a.config.sortOrder || 0) - (b.config.sortOrder || 0));
        setAllFields(combined);
      }

      if (countdownsResult.payload.success) {
        setCountdowns(countdownsResult.payload.countdowns);
      }
    } catch (error) {
      console.error('Error fetching fields and countdowns:', error);
      toast.error('Có lỗi xảy ra khi tải thông tin trường');
    } finally {
      setLoading(false);
    }
  };

  const getFieldValue = (field: any) => {
    if (field.isDefault) {
      // Default field
      const value = courtCase[field.name as keyof CourtCaseItemType];
      if (field.dataType === 'date' && value) {
        return formatDate(value as string);
      }
      return value || '';
    } else {
      // Custom field
      return courtCase.customFields?.[field.name] || '';
    }
  };

  const renderFieldValue = (field: any, value: any) => {
    if (!value && value !== 0) return <span className="text-gray-400 italic">Chưa có dữ liệu</span>;

    switch (field.dataType) {
      case 'select':
        if (field.name === 'trangThaiGiaiQuyet') {
          return (
            <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusBadgeClass(value)}`}>
              {value}
            </span>
          );
        }
        return <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">{value}</span>;
      case 'textarea':
        return <div className="whitespace-pre-wrap">{value}</div>;
      case 'number':
        return <span className="font-mono">{value.toLocaleString()}</span>;
      case 'date':
        return <span>{value}</span>;
      case 'boolean':
        return value ? '✅ Có' : '❌ Không';
      default:
        return <span>{value}</span>;
    }
  };

  const isDateField = (field: any) => {
    return field.dataType === 'date' || field.dataType === 'datetime';
  };

  const getCountdownForField = (fieldName: string) => {
    return countdowns.find(c => c.fieldName === fieldName && c.countdownConfig.enabled && c.countdownConfig.showCountdownBadge);
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'Đã giải quyết':
        return 'bg-green-100 text-green-800';
      case 'Đang giải quyết':
        return 'bg-yellow-100 text-yellow-800';
      case 'Chưa giải quyết':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string | undefined) => {
    if (!type) return '📄';

    // Check for common case types
    const lowerType = type.toLowerCase();
    if (lowerType.includes('hình sự')) return '⚖️';
    if (lowerType.includes('dân sự')) return '🏠';
    if (lowerType.includes('hành chính')) return '🏛️';
    if (lowerType.includes('kinh tế')) return '💼';
    if (lowerType.includes('lao động')) return '👷';

    // Default icon for any other type
    return '📋';
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50" style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}>
      <div 
        className="p-6 rounded-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto"
        style={{ backgroundColor: '#ffffff', color: '#111827' }}
      >
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <span className="text-3xl mr-3">{getTypeIcon(courtCase.loaiAn)}</span>
            <div>
              <h2 className="text-2xl font-bold" style={{ color: '#111827' }}>
                Chi tiết vụ việc #{courtCase.stt}
              </h2>
              <p className="text-gray-600">{courtCase.soThuLy}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-xl font-bold p-2 rounded hover:bg-gray-100"
            style={{ color: '#6b7280' }}
          >
            ✕
          </button>
        </div>

        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Đang tải thông tin...</p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Hiển thị tất cả các trường theo cấu hình */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-lg font-semibold mb-4 flex items-center" style={{ color: '#111827' }}>
                📋 Thông tin chi tiết
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {allFields
                  .filter(field => field.config.showInDetail !== false)
                  .map((field) => {
                    const value = getFieldValue(field);
                    const rawValue = field.isDefault ? courtCase[field.name] : courtCase.customFields?.[field.name];
                    const countdown = isDateField(field) ? getCountdownForField(field.name) : null;
                    
                    return (
                      <div key={field._id} className={field.dataType === 'textarea' ? 'md:col-span-2' : ''}>
                        <label className="block text-sm font-medium text-gray-600 mb-1">
                          {field.label}
                          {field.isDefault && (
                            <span className="ml-2 text-xs px-2 py-0.5 bg-blue-100 text-blue-600 rounded">
                              Cơ bản
                            </span>
                          )}
                          {!field.isDefault && (
                            <span className="ml-2 text-xs px-2 py-0.5 bg-green-100 text-green-600 rounded">
                              Tùy chỉnh
                            </span>
                          )}
                        </label>
                        <div className="flex items-center gap-2">
                          <div className="flex-1" style={{ color: '#111827' }}>
                            {renderFieldValue(field, value)}
                          </div>
                          {countdown && rawValue && (
                            <DateCountdownBadge
                              date={rawValue}
                              warningDays={countdown.countdownConfig.warningDays}
                              dangerDays={countdown.countdownConfig.dangerDays}
                              size="sm"
                              showIcon={true}
                              showText={true}
                            />
                          )}
                        </div>
                      </div>
                    );
                  })}
              </div>
            </div>

            {/* Metadata section */}
            <div className="bg-gray-100 p-4 rounded-lg">
              <h3 className="text-lg font-semibold mb-4 flex items-center" style={{ color: '#111827' }}>
                ℹ️ Thông tin hệ thống
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {courtCase.createdBy && (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-600">Người tạo</label>
                      <p className="text-lg" style={{ color: '#111827' }}>
                        {courtCase.createdBy.username}
                      </p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-600">Ngày tạo</label>
                      <p className="text-lg" style={{ color: '#111827' }}>{formatDate(courtCase.createdAt)}</p>
                    </div>
                  </>
                )}
                {courtCase.updatedBy && (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-600">Người cập nhật cuối</label>
                      <p className="text-lg" style={{ color: '#111827' }}>
                        {courtCase.updatedBy.username}
                      </p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-600">Ngày cập nhật cuối</label>
                      <p className="text-lg" style={{ color: '#111827' }}>{formatDate(courtCase.updatedAt)}</p>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end gap-4 pt-6 border-t mt-6">
          <button
            onClick={onClose}
            className="px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
            style={{ color: '#111827' }}
          >
            Đóng
          </button>
          {hasPermission('court_case_edit') && (
            <button
              onClick={() => {
                onClose(); // Đóng modal chi tiết trước
                onEdit(courtCase); // Sau đó mở form chỉnh sửa
              }}
              className="flex items-center gap-2 px-6 py-2 rounded-md hover:opacity-90 transition-opacity"
              style={{ backgroundColor: '#059669', color: '#ffffff' }}
            >
              <Edit size={16} />
              Chỉnh sửa
            </button>
          )}
          {hasPermission('court_case_delete') && (
            <button
              onClick={() => {
                if (confirm('Bạn có chắc chắn muốn xóa vụ việc này?')) {
                  onDelete(courtCase._id);
                  onClose();
                }
              }}
              className="flex items-center gap-2 px-6 py-2 rounded-md hover:opacity-90 transition-opacity"
              style={{ backgroundColor: '#dc2626', color: '#ffffff' }}
            >
              <Trash2 size={16} />
              Xóa
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default CourtCaseDetailModal;
