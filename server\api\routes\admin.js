const express = require("express");
const router = express.Router();
const adminController = require("../controllers/admin");
const passport = require('passport')
const {verifyAdmin, verifyManager}  = require('../middleware/is-admin')
const checkBlacklistedToken = require("../middleware/checkBlacklistedToken");
const {
  adminUserValidation,
  adminEditValidation,
  passwordChangeValidation,
  mongoIdValidation
} = require("../middleware/inputValidation");
const {
  verifyDashboardAccess,
  verifyUserView,
  verifyUserAdd,
  verifyUserEdit,
  verifyUserDelete,
  verifySystemSettingsView
} = require("../middleware/permission-check");

router.post("/signup", passport.authenticate('user', { session: false }), checkBlacklistedToken, verifyUserAdd, adminUserValidation, adminController.userSignup);

router.post("/users/", passport.authenticate('user',{session: false}), checkBlacklistedToken, verifyUserView, adminController.get_all_user);

router.post("/users/search", passport.authenticate('user',{session: false}),checkBlacklistedToken , verifyUserView, adminController.search_user);

router.get("/users/:id", passport.authenticate('user',{session: false}), checkBlacklistedToken, verifyUserView, mongoIdValidation, adminController.get_single_user);

router.delete("/users/:id", passport.authenticate('user',{session: false}), checkBlacklistedToken, verifyUserDelete, mongoIdValidation, adminController.delete_user);

router.put('/users/change-pass/', passport.authenticate('user',{session: false}), checkBlacklistedToken, verifyUserEdit, adminController.admin_change_password)

router.post("/refesh-user", passport.authenticate('user',{session: false}), checkBlacklistedToken, verifyUserView, adminController.refesh_user);

router.put('/change-email/:id', passport.authenticate('user',{session: false}),checkBlacklistedToken, verifyUserEdit, mongoIdValidation, adminController.admin_change_email)

router.put('/update-private', passport.authenticate('user',{session: false}), checkBlacklistedToken, verifyUserEdit, mongoIdValidation, adminController.admin_update_user_private)

router.get('/userselect', adminController.getAllUserForSelect);



const { dashboardLimiter } = require("../middleware/security");

router.get("/dashboard-stats", dashboardLimiter, passport.authenticate('user',{session: false}), checkBlacklistedToken, verifyDashboardAccess, adminController.getDashboardStats);

router.get("/recent-activities", dashboardLimiter, passport.authenticate('user',{session: false}), checkBlacklistedToken, verifyDashboardAccess, adminController.getRecentActivities);

// User dashboard routes - with authentication
router.get("/user-dashboard-stats", passport.authenticate('user',{session: false}), checkBlacklistedToken, adminController.getUserDashboardStats);

router.get("/permission-descriptions", adminController.getPermissionDescriptions);



router.put('/change-info/', passport.authenticate('user',{session: false}), checkBlacklistedToken, verifyUserEdit, adminController.adminChangeInfo)

router.get('/log/:id', passport.authenticate('user',{session: false}), checkBlacklistedToken, verifyUserView, mongoIdValidation, adminController.getUserLog)

module.exports = router;
