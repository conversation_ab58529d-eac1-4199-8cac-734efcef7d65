const express = require('express');

const app = express();
const morgan = require('morgan');
const mongoose = require('mongoose');
const dotenv = require('dotenv');
const { accessibleRecordsPlugin } = require('@casl/mongoose');
const cors = require('cors');
const passport = require('passport');
require('./api/middleware/passport');
const path = require("path");

// Import security middleware
const {
  securityHeaders,
  rateLimitConfigs,
  adminIpRestriction,
  sanitizeRequest,
  securityMonitoring
} = require('./api/middleware/securityConfig');

const { securityValidationMiddleware } = require('./api/middleware/securityLogger');

// Import error handling middleware
const { handleError, notFound } = require('./api/middleware/errorHandler');

// Import image handling middleware
const { handleMissingImage } = require('./api/middleware/imageHandler');

mongoose.plugin(accessibleRecordsPlugin);
mongoose.set('strictQuery', true);

dotenv.config();

mongoose.connect(process.env.MONGO_URL);
mongoose.Promise = global.Promise;

// Trust proxy for rate limiting
app.set('trust proxy', 1);

// Security middleware
app.use(securityHeaders);
app.use(sanitizeRequest);
app.use(securityMonitoring);
app.use(securityValidationMiddleware);
app.use(rateLimitConfigs.general);

// CORS configuration - restrictive
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);
    
    const allowedOrigins = [
      'https://toaantphcm.vn',
      'https://www.toaantphcm.vn',
      'https://flscott.com',
      'https://www.flscott.com',
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:8000'
    ];
    
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.warn('CORS blocked origin:', origin);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'Cache-Control',
    'X-Access-Token'
  ]
};

app.use(cors(corsOptions));

// Handle missing images before serving static files
app.use('/uploads', handleMissingImage);

// Note: Static file serving is handled by the main app.js file

// App Use Libary
app.use(morgan('dev'));
// Log thay đổi của app Get/ Post ...
// parse application/x-www-form-urlencoded

app.use(express.json());
app.use(express.urlencoded({
  extended: true,
}));
app.use(passport.initialize());

// Remove manual CORS headers as we're using proper CORS middleware now


// Import router
const userRouter = require('./api/routes/user');
const adminRouter = require('./api/routes/admin');
const settingRouter = require('./api/routes/setting');
const mediaRouter = require('./api/routes/media');
const videoRouter = require('./api/routes/video');
const fileManagerRouter = require('./api/routes/fileManager');
const fileServeRouter = require('./api/routes/fileServe');
const courtCaseRouter = require('./api/routes/courtCase');
const courtCaseUserManagementRouter = require('./api/routes/courtCaseUserManagement');
const departmentRouter = require('./api/routes/department');
const customFieldRouter = require('./api/routes/customField');
const fieldConfigurationRouter = require('./api/routes/fieldConfiguration');
const dateCountdownRouter = require('./api/routes/dateCountdown');


// Export router - Note: /api prefix is handled by the main app.js mounting
app.use('/auth', rateLimitConfigs.auth, userRouter);
app.use('/administrator', rateLimitConfigs.admin, adminRouter); // Temporarily removed adminIpRestriction for testing
app.use('/setting', rateLimitConfigs.general, settingRouter);
app.use('/media', rateLimitConfigs.upload, mediaRouter);
app.use('/video', rateLimitConfigs.video, videoRouter);
app.use('/administrator/files', rateLimitConfigs.admin, adminIpRestriction, fileManagerRouter);
app.use('/files', fileServeRouter);
app.use('/court-cases', rateLimitConfigs.admin, adminIpRestriction, courtCaseRouter);
app.use('/court-cases/user-management', rateLimitConfigs.admin, adminIpRestriction, courtCaseUserManagementRouter);
app.use('/departments', rateLimitConfigs.admin, adminIpRestriction, departmentRouter);
app.use('/custom-fields', rateLimitConfigs.admin, adminIpRestriction, customFieldRouter);
app.use('/field-configurations', rateLimitConfigs.admin, adminIpRestriction, fieldConfigurationRouter);
app.use('/date-countdowns', rateLimitConfigs.admin, adminIpRestriction, dateCountdownRouter);


// Root route handled by Next.js

// Error handling middleware
app.use(notFound);
app.use(handleError);
  

// Test route for debugging
app.get('/test-api-status', (req, res) => {
  res.json({
    message: 'API server is working',
    timestamp: new Date(),
    routes: {
      'custom-fields': 'Available',
      'field-configurations': 'Available', 
      'date-countdowns': 'Available'
    },
    environment: process.env.NODE_ENV || 'development'
  });
});

module.exports = app;
  