"use client";

import { useState, useEffect } from "react";
import { Clock, AlertTriangle, CheckCircle, XCircle } from "lucide-react";

interface DateCountdownBadgeProps {
  date: string | Date | null;
  warningDays?: number;
  dangerDays?: number;
  showIcon?: boolean;
  showText?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export default function DateCountdownBadge({
  date,
  warningDays = 30,
  dangerDays = 7,
  showIcon = true,
  showText = true,
  size = 'md',
  className = ''
}: DateCountdownBadgeProps) {
  const [daysRemaining, setDaysRemaining] = useState<number | null>(null);
  const [status, setStatus] = useState<'expired' | 'danger' | 'warning' | 'safe' | 'none'>('none');

  useEffect(() => {
    if (!date) {
      setDaysRemaining(null);
      setStatus('none');
      return;
    }

    const calculateDaysRemaining = () => {
      const countdownStartDate = new Date(date); // <PERSON><PERSON>y bắt đầu đếm ngược

      // Tính ngày hết hạn = ngày bắt đầu + số ngày cảnh báo
      const deadlineDate = new Date(countdownStartDate);
      deadlineDate.setDate(deadlineDate.getDate() + warningDays);

      // Chuyển đổi sang múi giờ GMT+7 (Việt Nam)
      const vietnamOffset = 7 * 60; // GMT+7 in minutes
      const currentDate = new Date();
      const currentUTC = currentDate.getTime() + (currentDate.getTimezoneOffset() * 60000);
      const vietnamTime = new Date(currentUTC + (vietnamOffset * 60000));

      // Reset time to start of day for accurate calculation
      countdownStartDate.setHours(0, 0, 0, 0);
      deadlineDate.setHours(0, 0, 0, 0);
      vietnamTime.setHours(0, 0, 0, 0);

      // Tính thời gian còn lại từ hiện tại đến ngày hết hạn
      const timeDiff = deadlineDate.getTime() - vietnamTime.getTime();
      const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

      // Trả về số ngày (có thể âm nếu đã quá hạn)
      return daysDiff;
    };

    const days = calculateDaysRemaining();
    setDaysRemaining(days);

    // Determine status
    if (days < 0) {
      setStatus('expired');
    } else {
      // Kiểm tra xem đã bắt đầu đếm ngược chưa
      const countdownStartDate = new Date(date);

      const vietnamOffset = 7 * 60;
      const currentDate = new Date();
      const currentUTC = currentDate.getTime() + (currentDate.getTimezoneOffset() * 60000);
      const vietnamTime = new Date(currentUTC + (vietnamOffset * 60000));

      countdownStartDate.setHours(0, 0, 0, 0);
      vietnamTime.setHours(0, 0, 0, 0);

      const timeToCountdownStart = countdownStartDate.getTime() - vietnamTime.getTime();

      if (timeToCountdownStart > 0) {
        // Chưa đến thời điểm bắt đầu đếm ngược
        setStatus('safe');
      } else if (days <= dangerDays) {
        setStatus('danger');
      } else {
        setStatus('warning');
      }
    }
  }, [date, warningDays, dangerDays]);

  const getStatusConfig = () => {
    const configs = {
      expired: {
        bg: 'bg-red-100',
        text: 'text-red-800',
        border: 'border-red-200',
        icon: XCircle,
        color: 'text-red-600'
      },
      danger: {
        bg: 'bg-red-100',
        text: 'text-red-800',
        border: 'border-red-200',
        icon: AlertTriangle,
        color: 'text-red-600'
      },
      warning: {
        bg: 'bg-yellow-100',
        text: 'text-yellow-800',
        border: 'border-yellow-200',
        icon: Clock,
        color: 'text-yellow-600'
      },
      safe: {
        bg: 'bg-green-100',
        text: 'text-green-800',
        border: 'border-green-200',
        icon: CheckCircle,
        color: 'text-green-600'
      },
      none: {
        bg: 'bg-gray-100',
        text: 'text-gray-800',
        border: 'border-gray-200',
        icon: Clock,
        color: 'text-gray-600'
      }
    };

    return configs[status];
  };

  const formatCountdownText = () => {
    if (daysRemaining === null) return '';

    if (daysRemaining < 0) {
      const overdueDays = Math.abs(daysRemaining);
      return `Quá hạn ${overdueDays} ngày`;
    }

    // Kiểm tra xem đã bắt đầu đếm ngược chưa
    const countdownStartDate = new Date(date);

    const vietnamOffset = 7 * 60;
    const currentDate = new Date();
    const currentUTC = currentDate.getTime() + (currentDate.getTimezoneOffset() * 60000);
    const vietnamTime = new Date(currentUTC + (vietnamOffset * 60000));

    countdownStartDate.setHours(0, 0, 0, 0);
    vietnamTime.setHours(0, 0, 0, 0);

    const timeToCountdownStart = countdownStartDate.getTime() - vietnamTime.getTime();

    if (timeToCountdownStart > 0) {
      // Chưa đến thời điểm bắt đầu đếm ngược - nhưng vẫn hiển thị "Còn X ngày" để nhất quán
      const daysToStart = Math.ceil(timeToCountdownStart / (1000 * 3600 * 24));
      return `Còn ${daysToStart} ngày`;
    }

    // Đã bắt đầu đếm ngược
    if (daysRemaining === 0) return 'Hết hạn hôm nay';
    if (daysRemaining === 1) return 'Còn 1 ngày';

    return `Còn ${daysRemaining} ngày`;
  };

  const getSizeClasses = () => {
    const sizes = {
      sm: {
        container: 'px-2 py-1 text-xs',
        icon: 12,
        gap: 'gap-1'
      },
      md: {
        container: 'px-2.5 py-1.5 text-sm',
        icon: 14,
        gap: 'gap-1.5'
      },
      lg: {
        container: 'px-3 py-2 text-base',
        icon: 16,
        gap: 'gap-2'
      }
    };

    return sizes[size];
  };

  if (!date || daysRemaining === null) {
    return null;
  }

  const config = getStatusConfig();
  const sizeConfig = getSizeClasses();
  const IconComponent = config.icon;

  return (
    <span
      className={`
        inline-flex items-center ${sizeConfig.gap} ${sizeConfig.container}
        ${config.bg} ${config.text} ${config.border}
        border rounded-full font-medium
        ${className}
      `}
      title={`${formatCountdownText()} - ${new Date(date).toLocaleDateString('vi-VN')}`}
    >
      {showIcon && (
        <IconComponent 
          size={sizeConfig.icon} 
          className={config.color}
        />
      )}
      {showText && formatCountdownText()}
    </span>
  );
}

// Hook để sử dụng countdown logic
export const useCountdown = (date: string | Date | null, warningDays = 30, dangerDays = 7) => {
  const [daysRemaining, setDaysRemaining] = useState<number | null>(null);
  const [status, setStatus] = useState<'expired' | 'danger' | 'warning' | 'safe' | 'none'>('none');

  useEffect(() => {
    if (!date) {
      setDaysRemaining(null);
      setStatus('none');
      return;
    }

    const calculateDaysRemaining = () => {
      const countdownStartDate = new Date(date); // Ngày bắt đầu đếm ngược

      // Tính ngày hết hạn = ngày bắt đầu + số ngày cảnh báo
      const deadlineDate = new Date(countdownStartDate);
      deadlineDate.setDate(deadlineDate.getDate() + warningDays);

      // Chuyển đổi sang múi giờ GMT+7 (Việt Nam)
      const vietnamOffset = 7 * 60; // GMT+7 in minutes
      const currentDate = new Date();
      const currentUTC = currentDate.getTime() + (currentDate.getTimezoneOffset() * 60000);
      const vietnamTime = new Date(currentUTC + (vietnamOffset * 60000));

      // Reset time to start of day for accurate calculation
      countdownStartDate.setHours(0, 0, 0, 0);
      deadlineDate.setHours(0, 0, 0, 0);
      vietnamTime.setHours(0, 0, 0, 0);

      // Tính thời gian còn lại từ hiện tại đến ngày hết hạn
      const timeDiff = deadlineDate.getTime() - vietnamTime.getTime();
      const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

      return daysDiff;
    };

    const days = calculateDaysRemaining();
    setDaysRemaining(days);

    if (days < 0) {
      setStatus('expired');
    } else {
      // Kiểm tra xem đã bắt đầu đếm ngược chưa
      const countdownStartDate = new Date(date);

      const vietnamOffset = 7 * 60;
      const currentDate = new Date();
      const currentUTC = currentDate.getTime() + (currentDate.getTimezoneOffset() * 60000);
      const vietnamTime = new Date(currentUTC + (vietnamOffset * 60000));

      countdownStartDate.setHours(0, 0, 0, 0);
      vietnamTime.setHours(0, 0, 0, 0);

      const timeToCountdownStart = countdownStartDate.getTime() - vietnamTime.getTime();

      if (timeToCountdownStart > 0) {
        // Chưa đến thời điểm bắt đầu đếm ngược
        setStatus('safe');
      } else if (days <= dangerDays) {
        setStatus('danger');
      } else {
        setStatus('warning');
      }
    }
  }, [date, warningDays, dangerDays]);

  const formatCountdownText = () => {
    if (daysRemaining === null) return '';
    
    if (daysRemaining < 0) {
      const overdueDays = Math.abs(daysRemaining);
      return `Quá hạn ${overdueDays} ngày`;
    }
    
    if (daysRemaining === 0) return 'Hết hạn hôm nay';
    if (daysRemaining === 1) return 'Còn 1 ngày';
    
    return `Còn ${daysRemaining} ngày`;
  };

  return {
    daysRemaining,
    status,
    countdownText: formatCountdownText(),
    isExpired: daysRemaining !== null && daysRemaining < 0,
    isDanger: status === 'danger',
    isWarning: status === 'warning',
    isSafe: status === 'safe'
  };
};
