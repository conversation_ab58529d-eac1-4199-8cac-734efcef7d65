const { AbilityBuilder, Ability, defineAbility } = require("@casl/ability");

let ANONYMOUS_ABILITY;

const defineAbilityFor = (user) => {
  if (user) {
    return new Ability(defineRulesFor(user));
  }
  ANONYMOUS_ABILITY = ANONYMOUS_ABILITY || new Ability(defineRulesFor({}));
  return ANONYMOUS_ABILITY;
};

function defineRulesFor(user) {
  const builder = new AbilityBuilder(Ability);

  // Permission-based rules for modern user system

  // If user has permissions array, use permission-based rules
  if (user.permissions && Array.isArray(user.permissions)) {
    definePermissionBasedRules(builder, user);
    return builder.rules;
  }

  // Fallback to role-based rules
  switch (user.rule) {
    case "admin":
      defineAdminRules(builder, user);
      break;
    case "department_manager":
      defineDepartmentManagerRules(builder, user);
      break;
    case "department_member":
      defineDepartmentMemberRules(builder, user);
      break;
    case "editor":
      defineEditorRules(builder, user);
      break;
    case "manager":
      defineManagerRules(builder, user);
      break;
    case "user":
      defineUserRules(builder, user);
      break;
    default:
      defineAnonymousRules(builder, user);
      break;
  }
  return builder.rules;
}

function definePermissionBasedRules({ can }, user) {
  // Admin rule always gets full access
  if (user.rule === "admin") {
    can("manage", "all");
    return;
  }

  // Process each permission
  user.permissions.forEach(permission => {

    switch (permission) {
      // User management permissions
      case "user_view":
        can("read", "User");
        break;
      case "user_add":
        can("create", "User");
        break;
      case "user_edit":
        can("update", "User");
        break;
      case "user_delete":
        can("delete", "User");
        break;
      case "user_import":
        can("import", "User");
        break;

      // File management permissions
      case "file_view":
        can("read", "File");
        break;
      case "file_upload":
        can("create", "File");
        break;
      case "file_delete":
        can("delete", "File");
        break;
      case "file_download":
        can("download", "File");
        break;

      // Court case permissions
      case "court_case_view":
        can("read", "CourtCase");
        can("read", "CustomField");
        can("read", "FieldConfiguration");
        can("read", "DateCountdown");
        break;
      case "court_case_create":
        can("create", "CourtCase");
        break;
      case "court_case_edit":
        can("update", "CourtCase");
        break;
      case "court_case_delete":
        can("delete", "CourtCase");
        can("read", "CustomField");
        can("read", "FieldConfiguration");
        can("read", "DateCountdown");
        break;
      case "court_case_export":
        can("export", "CourtCase");
        break;
      case "court_case_import":
        can("import", "CourtCase");
        break;
      case "court_case_stats":
        can("read", "CourtCaseStats");
        can("read", "CustomField");
        can("read", "FieldConfiguration");
        can("read", "DateCountdown");
        break;

      // Custom fields permissions
      case "custom_fields_view":
        can("read", "CustomField");
        can("read", "FieldConfiguration");
        can("read", "DateCountdown");
        break;
      case "custom_fields_create":
        can("create", "CustomField");
        break;
      case "custom_fields_edit":
        can("update", "CustomField");
        can("update", "FieldConfiguration");
        break;
      case "custom_fields_delete":
        can("delete", "CustomField");
        break;
      case "custom_fields_manage":
        can("manage", "CustomField");
        can("manage", "FieldConfiguration");
        can("manage", "DateCountdown");
        break;

      // Department permissions
      case "department_view":
        can("read", "Department");
        break;
      case "department_create":
        can("create", "Department");
        break;
      case "department_edit":
        can("update", "Department");
        break;
      case "department_delete":
        can("delete", "Department");
        break;
      case "department_member_manage":
        can("manage", "DepartmentMember");
        break;

      // System permissions
      case "system_settings_view":
        can("read", "SystemSettings");
        break;
      case "system_settings_edit":
        can("update", "SystemSettings");
        break;
      case "system_logs_view":
        can("read", "SystemLogs");
        break;
      case "permissions_manage":
        can("manage", "Permission");
        break;

      // Special admin permissions
      case "system_admin_full_access":
        can("manage", "all");
        break;
      case "system_departments_manage":
        can("manage", "Department");
        break;
      case "system_users_manage":
        can("manage", "User");
        break;
      case "system_settings_manage":
        can("manage", "SystemSettings");
        break;

      default:
        // Unknown permission - skip silently
        break;
    }
  });
}

function defineAdminRules({ can }, user) {
  can("manage", "all"); // Admins can manage everything including files
  can("read", "dashboard"); // Explicitly allow dashboard access
  can("manage", "CustomField"); // Admins can manage custom fields
}

function defineDepartmentManagerRules({ can, cannot }, user) {
  // Self management
  can(["read", "update"], ["User"], { _id: user._id });

  // Convert user.department to string for consistent comparison (define at function scope)
  const userDeptId = user.department ?
    (user.department._id ? user.department._id.toString() : user.department.toString())
    : null;

  // Department management - only users in the same department
  if (user.department && userDeptId) {
    // Use custom condition function to handle ObjectId vs string comparison
    can(["read", "create", "update", "delete"], ["User"], (subject) => {
      if (!subject.department) return false;
      const subjectDeptId = subject.department._id ? subject.department._id.toString() : subject.department.toString();
      return subjectDeptId === userDeptId;
    });
    can(["read"], ["Department"], { _id: user.department });
  }

  // Department-specific permissions based on user's effective permissions
  if (user.permissions) {
    user.permissions.forEach(permission => {
      switch (permission) {
        // User management permissions
        case "user_view":
          can("read", "User", (subject) => {
            if (!subject.department) return false;
            const subjectDeptId = subject.department._id ? subject.department._id.toString() : subject.department.toString();
            return subjectDeptId === userDeptId;
          });
          break;
        case "user_add":
          can("create", "User", (subject) => {
            if (!subject.department) return false;
            const subjectDeptId = subject.department._id ? subject.department._id.toString() : subject.department.toString();
            return subjectDeptId === userDeptId;
          });
          break;
        case "user_edit":
          can("update", "User", (subject) => {
            if (!subject.department) return false;
            const subjectDeptId = subject.department._id ? subject.department._id.toString() : subject.department.toString();
            return subjectDeptId === userDeptId;
          });
          break;
        case "user_delete":
          can("delete", "User", (subject) => {
            if (!subject.department) return false;
            const subjectDeptId = subject.department._id ? subject.department._id.toString() : subject.department.toString();
            return subjectDeptId === userDeptId;
          });
          break;
        case "user_import":
          can("import", "User");
          break;

        // File management permissions
        case "file_view":
          can("read", "File");
          break;
        case "file_upload":
          can("create", "File");
          break;
        case "file_delete":
          can("delete", "File");
          break;
        case "file_download":
          can("download", "File");
          break;

        // Court case management permissions
        case "court_case_view":
          can("read", "CourtCase");
          // Users with court case view permission can also read custom fields
          can("read", "CustomField");
          can("read", "FieldConfiguration");
          can("read", "DateCountdown");
          break;
        case "court_case_create":
          can("create", "CourtCase");
          // Users with court case create permission can also read custom fields
          can("read", "CustomField");
          can("read", "FieldConfiguration");
          can("read", "DateCountdown");
          break;
        case "court_case_edit":
          can("update", "CourtCase");
          // Users with court case edit permission can also read custom fields
          can("read", "CustomField");
          can("read", "FieldConfiguration");
          can("read", "DateCountdown");
          break;
        case "court_case_delete":
          can("delete", "CourtCase");
          // Users with court case delete permission can also read custom fields
          can("read", "CustomField");
          can("read", "FieldConfiguration");
          can("read", "DateCountdown");
          break;
        case "court_case_export":
          can("export", "CourtCase");
          break;
        case "court_case_import":
          can("import", "CourtCase");
          break;
        case "court_case_stats":
          can("read", "CourtCaseStats");
          // Users with court case stats permission can also read custom fields
          can("read", "CustomField");
          can("read", "FieldConfiguration");
          can("read", "DateCountdown");
          break;

        // Custom Fields Management permissions
        case "custom_fields_view":
          can("read", "CustomField");
          can("read", "FieldConfiguration");
          can("read", "DateCountdown");
          break;
        case "custom_fields_create":
          can("create", "CustomField");
          can("create", "FieldConfiguration");
          can("create", "DateCountdown");
          break;
        case "custom_fields_edit":
          can("update", "CustomField");
          can("update", "FieldConfiguration");
          can("update", "DateCountdown");
          break;
        case "custom_fields_delete":
          can("delete", "CustomField");
          can("delete", "FieldConfiguration");
          can("delete", "DateCountdown");
          break;
        case "custom_fields_manage":
          can("manage", "CustomField");
          can("manage", "FieldConfiguration");
          can("manage", "DateCountdown");
          break;

        // Department management permissions (usually for admin only)
        case "department_view":
          can("read", "Department");
          break;
        case "department_create":
          can("create", "Department");
          break;
        case "department_edit":
          can("update", "Department");
          break;
        case "department_delete":
          can("delete", "Department");
          break;
        case "department_member_manage":
          can("manage", "DepartmentMember");
          break;

        // System settings permissions
        case "system_settings_view":
          can("read", "Setting");
          break;
        case "system_settings_edit":
          can("update", "Setting");
          break;
        case "system_logs_view":
          can("read", "SystemLog");
          break;
      }
    });
  }

  can("read", "dashboard");
}

function defineDepartmentMemberRules({ can, cannot }, user) {
  // Self management
  can(["read", "update"], ["User"], { _id: user._id });

  // Department-specific permissions based on user's effective permissions
  if (user.permissions) {
    user.permissions.forEach(permission => {
      switch (permission) {
        // User management permissions (limited to same department)
        case "user_view":
          if (user.department) {
            const userDeptId = user.department._id ? user.department._id.toString() : user.department.toString();
            can("read", "User", (subject) => {
              if (!subject.department) return false;
              const subjectDeptId = subject.department._id ? subject.department._id.toString() : subject.department.toString();
              return subjectDeptId === userDeptId;
            });
          }
          break;

        // File management permissions
        case "file_view":
          can("read", "File");
          break;
        case "file_upload":
          can("create", "File");
          break;
        case "file_download":
          can("download", "File");
          break;

        // Court case management permissions
        case "court_case_view":
          can("read", "CourtCase");
          break;
        case "court_case_create":
          can("create", "CourtCase");
          break;
        case "court_case_edit":
          can("update", "CourtCase");
          break;
        case "court_case_export":
          can("export", "CourtCase");
          break;
        case "court_case_stats":
          can("read", "CourtCaseStats");
          break;
      }
    });
  }

  can("read", "dashboard");
}
function defineEditorRules({ can, cannot }, user) {
  can(["read", "update"], ["User"], { _id: user._id });
  can(["read", "update", "modify"], ["Post"], { user: user._id });
  can(["read", "update", "modify"], "Setting");
  
  // Check for specific permissions
  if (user.permissions) {
    // User management permissions
    if (user.permissions.includes('user_view')) {
      can("read", "User");
    }
    if (user.permissions.includes('user_add')) {
      can("create", "User");
    }
    if (user.permissions.includes('user_edit')) {
      can("update", "User");
    }
    if (user.permissions.includes('user_delete')) {
      can("delete", "User");
    }

    // File management permissions
    if (user.permissions.includes('file_view')) {
      can("read", "File");
    }
    if (user.permissions.includes('file_upload')) {
      can("create", "File");
    }
    if (user.permissions.includes('file_delete')) {
      can("delete", "File");
    }

    // Court case management permissions
    if (user.permissions.includes('court_case_view')) {
      can("read", "CourtCase");
    }
    if (user.permissions.includes('court_case_create')) {
      can("create", "CourtCase");
    }
    if (user.permissions.includes('court_case_edit')) {
      can("update", "CourtCase");
    }
    if (user.permissions.includes('court_case_delete')) {
      can("delete", "CourtCase");
    }
    if (user.permissions.includes('court_case_export')) {
      can("export", "CourtCase");
    }
    if (user.permissions.includes('court_case_import')) {
      can("import", "CourtCase");
    }
    if (user.permissions.includes('court_case_stats_view')) {
      can("read", "CourtCaseStats");
    }
    if (user.permissions.includes('court_case_detailed_stats_view')) {
      can("read", "CourtCaseDetailedStats");
    }

    // Court Case User Account Management permissions
    if (user.permissions.includes('court_case_user_profile_view')) {
      can("read", "UserProfile");
    }
    if (user.permissions.includes('court_case_user_profile_edit')) {
      can("update", "UserProfile");
    }
    if (user.permissions.includes('court_case_user_password_change')) {
      can("changePassword", "User");
    }
    if (user.permissions.includes('court_case_user_permissions_view')) {
      can("read", "UserPermissions");
    }
    if (user.permissions.includes('court_case_user_permissions_edit')) {
      can("update", "UserPermissions");
    }
    if (user.permissions.includes('court_case_user_activity_log_view')) {
      can("read", "UserActivityLog");
    }
    if (user.permissions.includes('court_case_user_two_factor_manage')) {
      can(["read", "update"], "UserTwoFactor");
    }
  }
}

function defineManagerRules({ can, cannot }, user) {
  can(["read", "update"], ["User"], { _id: user._id });
  can(["read", "update", "modify"], ["Post"]);
  can(["read", "create", "update", "delete"], "File"); // Managers can manage files
  can(["read", "create", "update", "delete"], "CourtCase"); // Managers can manage court cases
  
  // Check for specific permissions
  if (user.permissions) {
    // User management permissions
    if (user.permissions.includes('user_view')) {
      can("read", "User");
    }
    if (user.permissions.includes('user_add')) {
      can("create", "User");
    }
    if (user.permissions.includes('user_edit')) {
      can("update", "User");
    }
    if (user.permissions.includes('user_delete')) {
      can("delete", "User");
    }

    // File management permissions
    if (user.permissions.includes('file_view')) {
      can("read", "File");
    }
    if (user.permissions.includes('file_upload')) {
      can("create", "File");
    }
    if (user.permissions.includes('file_delete')) {
      can("delete", "File");
    }

    // Court case management permissions
    if (user.permissions.includes('court_case_view')) {
      can("read", "CourtCase");
    }
    if (user.permissions.includes('court_case_create')) {
      can("create", "CourtCase");
    }
    if (user.permissions.includes('court_case_edit')) {
      can("update", "CourtCase");
    }
    if (user.permissions.includes('court_case_delete')) {
      can("delete", "CourtCase");
    }
    if (user.permissions.includes('court_case_export')) {
      can("export", "CourtCase");
    }
    if (user.permissions.includes('court_case_import')) {
      can("import", "CourtCase");
    }
    if (user.permissions.includes('court_case_stats_view')) {
      can("read", "CourtCaseStats");
    }
    if (user.permissions.includes('court_case_detailed_stats_view')) {
      can("read", "CourtCaseDetailedStats");
    }

    // Court Case User Account Management permissions
    if (user.permissions.includes('court_case_user_profile_view')) {
      can("read", "UserProfile");
    }
    if (user.permissions.includes('court_case_user_profile_edit')) {
      can("update", "UserProfile");
    }
    if (user.permissions.includes('court_case_user_password_change')) {
      can("changePassword", "User");
    }
    if (user.permissions.includes('court_case_user_permissions_view')) {
      can("read", "UserPermissions");
    }
    if (user.permissions.includes('court_case_user_permissions_edit')) {
      can("update", "UserPermissions");
    }
    if (user.permissions.includes('court_case_user_activity_log_view')) {
      can("read", "UserActivityLog");
    }
    if (user.permissions.includes('court_case_user_two_factor_manage')) {
      can(["read", "update"], "UserTwoFactor");
    }
  }
}

function defineUserRules({ can, cannot }, user) {
  can(["read", "update"], ["User"], { _id: user._id });
  can(["read", "update", "modify"], ["Post"], { user: user._id });
  
  // Check for specific permissions
  if (user.permissions) {
    // User management permissions
    if (user.permissions.includes('user_view')) {
      can("read", "User");
    }
    if (user.permissions.includes('user_add')) {
      can("create", "User");
    }
    if (user.permissions.includes('user_edit')) {
      can("update", "User");
    }
    if (user.permissions.includes('user_delete')) {
      can("delete", "User");
    }
    
    // File management permissions
    if (user.permissions.includes('file_view')) {
      can("read", "File");
    }
    if (user.permissions.includes('file_upload')) {
      can("create", "File");
    }
    if (user.permissions.includes('file_delete')) {
      can("delete", "File");
    }

    // Court case management permissions
    if (user.permissions.includes('court_case_view')) {
      can("read", "CourtCase");
    }
    if (user.permissions.includes('court_case_create')) {
      can("create", "CourtCase");
    }
    if (user.permissions.includes('court_case_edit')) {
      can("update", "CourtCase");
    }
    if (user.permissions.includes('court_case_delete')) {
      can("delete", "CourtCase");
    }
    if (user.permissions.includes('court_case_export')) {
      can("export", "CourtCase");
    }
    if (user.permissions.includes('court_case_import')) {
      can("import", "CourtCase");
    }
    if (user.permissions.includes('court_case_stats_view')) {
      can("read", "CourtCaseStats");
    }
    if (user.permissions.includes('court_case_detailed_stats_view')) {
      can("read", "CourtCaseDetailedStats");
    }

    // Court Case User Account Management permissions
    if (user.permissions.includes('court_case_user_profile_view')) {
      can("read", "UserProfile");
    }
    if (user.permissions.includes('court_case_user_profile_edit')) {
      can("update", "UserProfile");
    }
    if (user.permissions.includes('court_case_user_password_change')) {
      can("changePassword", "User");
    }
    if (user.permissions.includes('court_case_user_permissions_view')) {
      can("read", "UserPermissions");
    }
    if (user.permissions.includes('court_case_user_permissions_edit')) {
      can("update", "UserPermissions");
    }
    if (user.permissions.includes('court_case_user_activity_log_view')) {
      can("read", "UserActivityLog");
    }
    if (user.permissions.includes('court_case_user_two_factor_manage')) {
      can(["read", "update"], "UserTwoFactor");
    }
  }
}

function defineAnonymousRules({ can }) {
  can("read", ["User"]);
}

module.exports = { defineAbilityFor };
