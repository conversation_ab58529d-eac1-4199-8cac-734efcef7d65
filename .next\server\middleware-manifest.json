{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "oUZ3UfuXv2lEoP4mrKf3WzsbPUhYH20/y7r2/pok7Fs=", "__NEXT_PREVIEW_MODE_ID": "b7fe4836a54022d23eec2654564f3c80", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7644d9eaaebc66f0c905d376bffdb4b56c54b34459bcd84520a6d1b64612d04d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1f27e056546c44f693ff8513a8fec1255cc9b1c15a11ecc19af812210784610e"}}}, "functions": {}, "sortedMiddleware": ["/"]}