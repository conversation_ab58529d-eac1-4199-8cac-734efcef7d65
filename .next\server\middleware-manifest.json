{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "oUZ3UfuXv2lEoP4mrKf3WzsbPUhYH20/y7r2/pok7Fs=", "__NEXT_PREVIEW_MODE_ID": "62b7fff844670fc3ccab729bd3247bb4", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2d60cda76487fd2a30f09912e8aeb9baf748e5fe43c43ff21dddacfe6f5df7ff", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "36bbdfe42e486c1491c829d2c05c8208873da5dd17083f510b0b80826c50fcbd"}}}, "functions": {}, "sortedMiddleware": ["/"]}