{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "oUZ3UfuXv2lEoP4mrKf3WzsbPUhYH20/y7r2/pok7Fs=", "__NEXT_PREVIEW_MODE_ID": "5cc3224b43c6424f5d269320ad652097", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d382c2281b46e1616582db117b5b975b6d487fc862cbfe1341562799852e4829", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a044bb39c3b3b77923fdc3dfa36e7a8163a730dcd9ab1f4d54edbab4d72cd722"}}}, "functions": {}, "sortedMiddleware": ["/"]}