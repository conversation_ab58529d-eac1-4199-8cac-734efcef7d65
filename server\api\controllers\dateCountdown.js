const DateCountdown = require("../models/dateCountdown");
const { ForbiddenError } = require("@casl/ability");
const { defineAbilityFor } = require("../permissions/abilities");

// L<PERSON><PERSON> danh sách cấu hình đếm ngược
exports.getDateCountdowns = async (req, res) => {
  try {
    // Skip permission check if no user (for debugging)
    if (req.user) {
      const ability = defineAbilityFor(req.user);
      ForbiddenError.from(ability).throwUnlessCan("read", "DateCountdown");
    }

    const { targetModel = 'CourtCase' } = req.query;

    const countdowns = await DateCountdown.find({
      targetModel
    })
    .populate('createdBy', 'username email')
    .populate('updatedBy', 'username email')
    .sort({ fieldName: 1 });

    res.json({
      success: true,
      countdowns
    });
  } catch (error) {
    console.error("Error getting date countdowns:", error);
    console.error("Error stack:", error.stack);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi lấy cấu hình đếm ngược.",
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

// Tạo hoặc cập nhật cấu hình đếm ngược
exports.upsertDateCountdown = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    ForbiddenError.from(ability).throwUnlessCan("update", "CustomField");

    const {
      targetModel = 'CourtCase',
      fieldName,
      isBuiltIn = false,
      countdownConfig
    } = req.body;

    if (!fieldName) {
      return res.status(400).json({
        success: false,
        message: "Tên trường là bắt buộc."
      });
    }

    // Validate countdownConfig
    if (countdownConfig.warningDays && countdownConfig.dangerDays) {
      if (countdownConfig.dangerDays >= countdownConfig.warningDays) {
        return res.status(400).json({
          success: false,
          message: "Số ngày nguy hiểm phải nhỏ hơn số ngày cảnh báo."
        });
      }
    }

    let countdown = await DateCountdown.findOne({
      targetModel,
      fieldName,
      createdBy: req.user.id
    });

    if (countdown) {
      // Cập nhật
      countdown.countdownConfig = { ...countdown.countdownConfig, ...countdownConfig };
      countdown.updatedBy = req.user.id;
    } else {
      // Tạo mới
      countdown = new DateCountdown({
        targetModel,
        fieldName,
        isBuiltIn,
        countdownConfig,
        createdBy: req.user.id
      });
    }

    await countdown.save();
    await countdown.populate('createdBy', 'username email');
    await countdown.populate('updatedBy', 'username email');

    res.json({
      success: true,
      message: countdown.isNew ? "Tạo cấu hình đếm ngược thành công." : "Cập nhật cấu hình đếm ngược thành công.",
      countdown
    });
  } catch (error) {
    console.error("Error upserting date countdown:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi lưu cấu hình đếm ngược.",
      error: error.message,
    });
  }
};

// Xóa cấu hình đếm ngược
exports.deleteDateCountdown = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    ForbiddenError.from(ability).throwUnlessCan("delete", "CustomField");

    const { id } = req.params;

    const countdown = await DateCountdown.findOne({
      _id: id,
      createdBy: req.user.id
    });

    if (!countdown) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy cấu hình đếm ngược."
      });
    }

    await DateCountdown.findByIdAndDelete(id);

    res.json({
      success: true,
      message: "Xóa cấu hình đếm ngược thành công."
    });
  } catch (error) {
    console.error("Error deleting date countdown:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi xóa cấu hình đếm ngược.",
      error: error.message,
    });
  }
};

// Lấy thống kê đếm ngược cho một model
exports.getCountdownStats = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    ForbiddenError.from(ability).throwUnlessCan("read", "CustomField");
    ForbiddenError.from(ability).throwUnlessCan("read", "CourtCase");

    const { targetModel = 'CourtCase' } = req.query;

    // Lấy tất cả cấu hình đếm ngược
    const countdowns = await DateCountdown.find({
      targetModel,
      createdBy: req.user.id,
      'countdownConfig.enabled': true
    });

    if (countdowns.length === 0) {
      return res.json({
        success: true,
        stats: {}
      });
    }

    // Import model tương ứng
    let Model;
    if (targetModel === 'CourtCase') {
      // Model = require('../../models/CourtCase'); // Temporarily disabled
      Model = null; // Mock for now
    } else {
      return res.status(400).json({
        success: false,
        message: "Model không được hỗ trợ."
      });
    }

    const stats = {};

    for (const countdown of countdowns) {
      const fieldName = countdown.fieldName;
      
      // Lấy tất cả records có giá trị ngày
      const records = await Model.find({
        [fieldName]: { $exists: true, $ne: null }
      }).select(fieldName);

      const fieldStats = {
        total: records.length,
        expired: 0,
        danger: 0,
        warning: 0,
        safe: 0,
        upcoming: []
      };

      records.forEach(record => {
        const dateValue = record[fieldName];
        const daysRemaining = DateCountdown.calculateDaysRemaining(dateValue);
        const status = DateCountdown.getWarningStatus(
          daysRemaining, 
          countdown.countdownConfig.warningDays, 
          countdown.countdownConfig.dangerDays
        );

        fieldStats[status]++;

        // Thêm vào danh sách sắp hết hạn (trong vòng 30 ngày)
        if (daysRemaining !== null && daysRemaining >= 0 && daysRemaining <= 30) {
          fieldStats.upcoming.push({
            recordId: record._id,
            daysRemaining,
            status,
            date: dateValue
          });
        }
      });

      // Sắp xếp upcoming theo số ngày còn lại
      fieldStats.upcoming.sort((a, b) => a.daysRemaining - b.daysRemaining);

      stats[fieldName] = {
        fieldName,
        label: countdown.fieldName,
        config: countdown.countdownConfig,
        ...fieldStats
      };
    }

    res.json({
      success: true,
      stats
    });
  } catch (error) {
    console.error("Error getting countdown stats:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi lấy thống kê đếm ngược.",
      error: error.message,
    });
  }
};

// Lấy danh sách records sắp hết hạn
exports.getUpcomingDeadlines = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    ForbiddenError.from(ability).throwUnlessCan("read", "CourtCase");

    const { targetModel = 'CourtCase', days = 30 } = req.query;

    // Lấy tất cả cấu hình đếm ngược
    const countdowns = await DateCountdown.find({
      targetModel,
      createdBy: req.user.id,
      'countdownConfig.enabled': true
    });

    if (countdowns.length === 0) {
      return res.json({
        success: true,
        deadlines: []
      });
    }

    // Import model tương ứng
    let Model;
    if (targetModel === 'CourtCase') {
      // Model = require('../../models/CourtCase'); // Temporarily disabled
      Model = null; // Mock for now
    } else {
      return res.status(400).json({
        success: false,
        message: "Model không được hỗ trợ."
      });
    }

    const deadlines = [];

    for (const countdown of countdowns) {
      const fieldName = countdown.fieldName;
      
      // Lấy tất cả records có giá trị ngày
      const records = await Model.find({
        [fieldName]: { $exists: true, $ne: null }
      });

      records.forEach(record => {
        const dateValue = record[fieldName];
        const daysRemaining = DateCountdown.calculateDaysRemaining(dateValue);
        
        if (daysRemaining !== null && daysRemaining >= 0 && daysRemaining <= parseInt(days)) {
          const status = DateCountdown.getWarningStatus(
            daysRemaining, 
            countdown.countdownConfig.warningDays, 
            countdown.countdownConfig.dangerDays
          );

          deadlines.push({
            recordId: record._id,
            record: record,
            fieldName,
            fieldLabel: countdown.fieldName,
            date: dateValue,
            daysRemaining,
            status,
            countdownText: DateCountdown.formatCountdown(daysRemaining, status),
            colors: DateCountdown.getStatusColor(status)
          });
        }
      });
    }

    // Sắp xếp theo số ngày còn lại
    deadlines.sort((a, b) => a.daysRemaining - b.daysRemaining);

    res.json({
      success: true,
      deadlines
    });
  } catch (error) {
    console.error("Error getting upcoming deadlines:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi lấy danh sách sắp hết hạn.",
      error: error.message,
    });
  }
};
