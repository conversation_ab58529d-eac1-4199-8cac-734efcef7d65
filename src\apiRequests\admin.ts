import http from "@/lib/http";
import envConfig from "@/config";

export interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
}

export interface DashboardStatsResponse {
  success: boolean;
  stats: DashboardStats;
}

export interface RecentActivity {
  id: string;
  type: 'post_created' | 'post_updated' | 'post_pending' | 'post_pending_updated' | 'user_registered' | 'page_created' | 'page_updated';
  action: string;
  title: string;
  slug?: string;
  user: string;
  userId: string;
  time: string;
  status?: string;
  role?: string;
  isActive?: boolean;
}

export interface RecentActivitiesResponse {
  success: boolean;
  activities: RecentActivity[];
}

export interface PendingPost {
  _id: string;
  title: string;
  slug: string;
  short: string;
  createdAt: string;
  updatedAt: string;
  user: {
    _id: string;
    username: string;
    email: string;
  };
  categories: Array<{
    _id: string;
    name: string;
    slug: string;
  }>;
  isActive: boolean;
  isFeature: boolean;
  views: number;
}

export interface PendingPostsResponse {
  success: boolean;
  posts: PendingPost[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalPosts: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

// User Dashboard Types
export interface UserDashboardData {
  userInfo: {
    username: string;
    email: string;
    rule: string;
    permissions: string[];
    department?: string;
    departmentRole?: string;
  };
  permissions: {
    available: string[];
    categories: {
      user_management: string[];
      court_case: string[];
      file_management: string[];
      department: string[];
      system: string[];
      custom_fields: string[];
    };
  };
  departments: Array<{
    _id: string;
    name: string;
    description: string;
    memberCount: number | null;
    isActive: boolean;
    createdAt: string;
    isOwnDepartment?: boolean;
  }>;
  systemStats: {
    totalUsers?: number;
    totalDepartments?: number;
    totalCourtCases?: number;
    activeUsers?: number;
    activeDepartments?: number;
    departmentMembers?: number;
    activeDepartmentMembers?: number;
    recentCourtCases?: number;
  };
  dashboardType: 'admin' | 'department_manager' | 'user';
}

export interface UserDashboardResponse {
  success: boolean;
  data: UserDashboardData;
}

export interface PermissionDescriptions {
  [key: string]: string;
}

export interface PermissionDescriptionsResponse {
  success: boolean;
  data: PermissionDescriptions;
}

const adminApiRequest = {
  getDashboardStats: (sessionToken: string) =>
    http.get<DashboardStatsResponse>("/api/administrator/dashboard-stats", {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  getRecentActivities: (sessionToken: string, limit?: number) =>
    http.get<RecentActivitiesResponse>(`/api/administrator/recent-activities${limit ? `?limit=${limit}` : ''}`, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  toggleUserPrivate: (userId: string, sessionToken: string) =>
    http.put<{ success: boolean; user: any; message?: string }>("/api/administrator/update-private", {
      id: userId
    }, {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  // User Dashboard APIs
  getUserDashboardStats: (sessionToken: string) =>
    http.get<UserDashboardResponse>("/api/administrator/user-dashboard-stats", {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

  getPermissionDescriptions: (sessionToken: string) =>
    http.get<PermissionDescriptionsResponse>("/api/administrator/permission-descriptions", {
      headers: {
        Authorization: `Bearer ${sessionToken}`,
      },
    }),

};

export default adminApiRequest;
