(()=>{var a={};a.id=8152,a.ids=[8152],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3368:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21456:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(65424);let e={getDashboardStats:a=>d.Ay.get("/api/administrator/dashboard-stats",{headers:{Authorization:`Bearer ${a}`}}),getRecentActivities:(a,b)=>d.Ay.get(`/api/administrator/recent-activities${b?`?limit=${b}`:""}`,{headers:{Authorization:`Bearer ${a}`}}),toggleUserPrivate:(a,b)=>d.Ay.put("/api/administrator/update-private",{id:a},{headers:{Authorization:`Bearer ${b}`}}),getUserDashboardStats:a=>d.Ay.get("/api/administrator/user-dashboard-stats",{headers:{Authorization:`Bearer ${a}`}}),getPermissionDescriptions:a=>d.Ay.get("/api/administrator/permission-descriptions",{headers:{Authorization:`Bearer ${a}`}})}},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31210:(a,b,c)=>{"use strict";c.d(b,{CN:()=>i,Lz:()=>j,Wu:()=>h,ZB:()=>g,Zp:()=>e,aR:()=>f});var d=c(21124);c(38301);let e=({children:a,className:b="",padding:c="md",shadow:e="sm",hover:f=!1,onClick:g})=>(0,d.jsx)("div",{className:`
        bg-white rounded-xl border border-gray-100
        ${{none:"",sm:"p-4",md:"p-6",lg:"p-8"}[c]}
        ${{none:"",sm:"shadow-sm",md:"shadow-md",lg:"shadow-lg"}[e]}
        ${f?"hover:shadow-lg transition-shadow duration-200":""}
        ${b}
      `,onClick:g,children:a}),f=({children:a,className:b=""})=>(0,d.jsx)("div",{className:`border-b border-gray-100 pb-4 mb-6 ${b}`,children:a}),g=({children:a,className:b="",size:c="md"})=>(0,d.jsx)("h2",{className:`font-bold text-gray-900 ${{sm:"text-lg",md:"text-xl",lg:"text-2xl"}[c]} ${b}`,children:a}),h=({children:a,className:b=""})=>(0,d.jsx)("div",{className:b,children:a}),i=({title:a,value:b,icon:c,trend:f,color:g="blue",onClick:h,clickable:i=!1})=>{let j=(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-1",children:a}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"number"==typeof b?b.toLocaleString():b}),f&&(0,d.jsxs)("p",{className:`text-sm mt-2 flex items-center ${f.isPositive?"text-green-600":"text-red-600"}`,children:[(0,d.jsx)("span",{className:"mr-1",children:f.isPositive?"↗":"↘"}),f.value]})]}),c&&(0,d.jsx)("div",{className:`p-3 rounded-lg ${{blue:"bg-blue-500",green:"bg-green-500",purple:"bg-purple-500",orange:"bg-orange-500",red:"bg-red-500"}[g]}`,children:(0,d.jsx)("div",{className:"text-white",children:c})})]});return i&&h?(0,d.jsx)(e,{hover:!0,className:`relative overflow-hidden cursor-pointer transform hover:scale-105 transition-all duration-200 ${i?"hover:shadow-lg":""}`,onClick:h,children:j}):(0,d.jsx)(e,{hover:!0,className:"relative overflow-hidden",children:j})},j=({title:a,description:b,icon:c,onClick:f,href:g,color:h="blue"})=>{let i=({children:a})=>(0,d.jsx)(e,{hover:!0,className:"cursor-pointer transform hover:scale-105 transition-transform duration-200",children:a}),j=(0,d.jsxs)("div",{className:"flex items-start space-x-4",children:[c&&(0,d.jsx)("div",{className:`p-3 rounded-lg ${{blue:"bg-blue-500",green:"bg-green-500",purple:"bg-purple-500",orange:"bg-orange-500",red:"bg-red-500"}[h]} flex-shrink-0`,children:(0,d.jsx)("div",{className:"text-white",children:c})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-900 mb-1",children:a}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:b})]})]});return g?(0,d.jsx)("a",{href:g,children:(0,d.jsx)(i,{children:j})}):(0,d.jsx)("div",{onClick:f,children:(0,d.jsx)(i,{children:j})})}},32956:(a,b,c)=>{Promise.resolve().then(c.bind(c,93517))},33128:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(49754),e=c(9117),f=c(46595),g=c(32324),h=c(39326),i=c(38928),j=c(20175),k=c(12),l=c(54290),m=c(12696),n=c(82802),o=c(77533),p=c(45229),q=c(32822),r=c(261),s=c(26453),t=c(52474),u=c(26713),v=c(51356),w=c(62685),x=c(36225),y=c(63446),z=c(2762),A=c(45742),B=c(86439),C=c(81170),D=c.n(C),E=c(62506),F=c(91203),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H={children:["",{children:["(private)",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,93517)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,87473)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,51472)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,5682)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,81170,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.bind(c,59732)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,I=["C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(private)/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/(private)/dashboard/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},55511:a=>{"use strict";a.exports=require("crypto")},59742:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>B});var d=c(21124),e=c(38301),f=c.n(e),g=c(31210),h=c(21456),i=c(80974),j=c(80755),k=c(23339);let l=(0,k.A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),m=(0,k.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),n=(0,k.A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);var o=c(94684);let p=(0,k.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);var q=c(3368);let r=()=>{let[a,b]=(0,e.useState)(null),[c,f]=(0,e.useState)({}),[i,k]=(0,e.useState)(!0),[r,s]=(0,e.useState)(null),[t,u]=(0,e.useState)("");(0,e.useEffect)(()=>{u(localStorage.getItem("sessionToken")||"")},[]),(0,e.useEffect)(()=>{t&&(async()=>{try{k(!0);let[a,c]=await Promise.all([h.A.getUserDashboardStats(t),h.A.getPermissionDescriptions(t)]);a.payload.success&&b(a.payload.data),c.payload.success&&f(c.payload.data)}catch(a){console.error("Error fetching dashboard data:",a),s(a instanceof Error?a.message:"An error occurred")}finally{k(!1)}})()},[t]);let v=a=>{switch(a){case"admin":return"Quản trị vi\xean";case"department_manager":return"Quản l\xfd ph\xf2ng ban";case"department_member":return"Th\xe0nh vi\xean ph\xf2ng ban";case"user":return"Người d\xf9ng";default:return a}};if(i)return(0,d.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,d.jsx)("div",{className:"text-lg",children:"Đang tải..."})});if(r)return(0,d.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,d.jsxs)("div",{className:"text-lg text-red-600",children:["Lỗi: ",r]})});if(!a)return(0,d.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,d.jsx)("div",{className:"text-lg",children:"Kh\xf4ng c\xf3 dữ liệu"})});let{userInfo:w,permissions:x,departments:y,systemStats:z,dashboardType:A}=a;return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold",children:"Th\xf4ng tin t\xe0i khoản"}),(0,d.jsxs)("p",{className:"text-muted-foreground",children:["Ch\xe0o mừng ",w.username," - ",v(w.rule)]})]}),(0,d.jsx)(j.Ex,{variant:"admin"===A?"default":"secondary",children:v(w.rule)})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsx)(g.aR,{children:(0,d.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(l,{className:"h-5 w-5"}),"Th\xf4ng tin t\xe0i khoản"]})}),(0,d.jsx)(g.Wu,{className:"space-y-4",children:(0,d.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:"T\xean đăng nhập"}),(0,d.jsx)("p",{className:"text-lg font-semibold",children:w.username})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:"Email"}),(0,d.jsx)("p",{className:"text-lg",children:w.email})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:"Vai tr\xf2"}),(0,d.jsx)("p",{className:"text-lg",children:(0,d.jsx)(j.Ex,{variant:"outline",children:v(w.rule)})})]}),w.department&&(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"text-sm font-medium text-muted-foreground",children:"Ph\xf2ng ban"}),(0,d.jsx)("p",{className:"text-lg",children:w.department})]})]})})]}),(0,d.jsxs)("div",{className:"grid gap-6 md:grid-cols-1 lg:grid-cols-2",children:[(0,d.jsxs)(g.Zp,{children:[(0,d.jsx)(g.aR,{children:(0,d.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(p,{className:"h-5 w-5"}),"Quyền v\xe0 chức năng của bạn"]})}),(0,d.jsxs)(g.Wu,{className:"space-y-4",children:[Object.entries(x.categories).map(([a,b])=>0===b.length?null:(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 font-medium",children:[(a=>{switch(a){case"user_management":return(0,d.jsx)(l,{className:"h-4 w-4"});case"court_case":case"file_management":return(0,d.jsx)(m,{className:"h-4 w-4"});case"department":return(0,d.jsx)(n,{className:"h-4 w-4"});case"system":return(0,d.jsx)(o.A,{className:"h-4 w-4"});case"custom_fields":return(0,d.jsx)(p,{className:"h-4 w-4"});default:return(0,d.jsx)(q.A,{className:"h-4 w-4"})}})(a),(a=>{switch(a){case"user_management":return"Quản l\xfd người d\xf9ng";case"court_case":return"Quản l\xfd vụ \xe1n";case"file_management":return"Quản l\xfd file";case"department":return"Quản l\xfd ph\xf2ng ban";case"system":return"Hệ thống";case"custom_fields":return"Trường t\xf9y chỉnh";default:return a}})(a)]}),(0,d.jsx)("div",{className:"grid gap-1 pl-6",children:b.map(a=>{let b=c[a]||a;return(0,d.jsx)("div",{className:"flex items-center",children:(0,d.jsx)("span",{className:"text-sm",children:b})},a)})}),(0,d.jsx)("hr",{className:"my-2"})]},a)),0===x.available.length&&(0,d.jsx)("p",{className:"text-muted-foreground text-center py-4",children:"Bạn chưa được cấp quyền n\xe0o"})]})]}),y.length>0&&(0,d.jsxs)(g.Zp,{children:[(0,d.jsx)(g.aR,{children:(0,d.jsxs)(g.ZB,{className:"flex items-center gap-2",children:[(0,d.jsx)(n,{className:"h-5 w-5"}),"Ph\xf2ng ban của bạn"]})}),(0,d.jsx)(g.Wu,{className:"space-y-4",children:y.map(a=>(0,d.jsxs)("div",{className:"border rounded-lg p-4 space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("h3",{className:"font-medium",children:a.name}),(0,d.jsxs)("div",{className:"flex gap-2",children:[a.isOwnDepartment&&(0,d.jsx)(j.Ex,{variant:"default",className:"text-xs",children:"Ph\xf2ng ban của bạn"}),(0,d.jsx)(j.Ex,{variant:a.isActive?"default":"secondary",className:"text-xs",children:a.isActive?"Hoạt động":"Kh\xf4ng hoạt động"})]})]}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:a.description}),null!==a.memberCount&&(0,d.jsxs)("p",{className:"text-sm",children:[(0,d.jsx)("strong",{children:"Số th\xe0nh vi\xean:"})," ",a.memberCount]}),(0,d.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Tạo ng\xe0y: ",new Date(a.createdAt).toLocaleDateString("vi-VN")]})]},a._id))})]})]})]})};var s=c(89869),t=c(80729),u=c(13756),v=c(4361),w=c(13909),x=c(7372),y=c.n(x);function z(){return(z=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var A=(0,e.forwardRef)(function(a,b){var c=a.color,d=a.size,e=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return f().createElement("svg",z({ref:b,xmlns:"http://www.w3.org/2000/svg",width:e,height:e,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),f().createElement("polyline",{points:"22 12 18 12 15 21 9 3 6 12 2 12"}))});A.propTypes={color:y().string,size:y().oneOfType([y().string,y().number])},A.displayName="Activity";let B=()=>{let{user:a}=(0,s.U)(),[b,c]=(0,e.useState)({totalUsers:0,activeUsers:0}),[f,j]=(0,e.useState)(!0),[k,l]=(0,e.useState)(""),[m,n]=(0,e.useState)(!1),[o,p]=(0,e.useState)([]),[q,x]=(0,e.useState)(!0);(0,e.useEffect)(()=>{l(localStorage.getItem("sessionToken")||"")},[]),(0,e.useEffect)(()=>{if(!k||m||!a||"admin"!==a.rule)return;let b=setTimeout(async()=>{try{j(!0),console.log("Fetching dashboard stats...");let a=await h.A.getDashboardStats(k);console.log("API Response:",a),a.payload.success?(console.log("Dashboard stats received:",a.payload.stats),c(a.payload.stats),n(!0),i.oR.success("Đ\xe3 tải thống k\xea th\xe0nh c\xf4ng")):i.oR.error("Kh\xf4ng thể tải thống k\xea dashboard")}catch(a){console.error("Error fetching dashboard stats:",a),c({totalUsers:156,activeUsers:89}),n(!0),a?.status===429?i.oR.warning("Qu\xe1 nhiều y\xeau cầu, sử dụng dữ liệu cache"):i.oR.warning("Lỗi API, sử dụng dữ liệu mẫu")}finally{j(!1)}},1e3);return()=>clearTimeout(b)},[k,a]),(0,e.useEffect)(()=>{if(!k||!a||"admin"!==a.rule)return;let b=setTimeout(async()=>{try{x(!0);let a=await h.A.getRecentActivities(k,8);a.payload.success?p(a.payload.activities):i.oR.error("Kh\xf4ng thể tải hoạt động gần đ\xe2y")}catch(a){console.error("Error fetching recent activities:",a),a?.status===429?i.oR.warning("Qu\xe1 nhiều y\xeau cầu, vui l\xf2ng thử lại sau"):i.oR.error("Lỗi khi tải hoạt động gần đ\xe2y")}finally{x(!1)}},1500);return()=>clearTimeout(b)},[k,a]);let y=[{title:"Quản l\xfd th\xe0nh vi\xean",description:"Xem v\xe0 quản l\xfd t\xe0i khoản người d\xf9ng",href:"/dashboard/user",icon:(0,d.jsx)(t.A,{size:20}),color:"green"},{title:"Quản l\xfd vụ việc",description:"Quản l\xfd vụ việc t\xf2a \xe1n v\xe0 thụ l\xfd",href:"/dashboard/court-cases",icon:(0,d.jsx)(u.A,{size:20}),color:"purple"},{title:"C\xe0i đặt hệ thống",description:"Cấu h\xecnh v\xe0 t\xf9y chỉnh website",href:"/dashboard/setting",icon:(0,d.jsx)(v.A,{size:20}),color:"orange"},{title:"Quản l\xfd file",description:"Quản l\xfd t\xe0i liệu v\xe0 h\xecnh ảnh",href:"/dashboard/files",icon:(0,d.jsx)(u.A,{size:20}),color:"blue"}];return a&&"admin"!==a.rule?(0,d.jsx)(r,{}):(0,d.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,d.jsxs)("div",{className:"space-y-8",children:[(0,d.jsx)("div",{className:"bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl text-white p-8",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h1",{className:"text-3xl font-bold mb-2 flex items-center",children:[(0,d.jsx)(w.A,{className:"mr-3",size:36}),"Bảng điều khiển quản trị"]}),(0,d.jsx)("p",{className:"text-purple-100 text-lg",children:"Quản l\xfd to\xe0n bộ hệ thống v\xe0 nội dung website"})]}),(0,d.jsx)("div",{className:"hidden md:block",children:(0,d.jsx)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-4",children:(0,d.jsx)(A,{size:48,className:"text-white"})})})]})}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6",children:f?Array.from({length:6}).map((a,b)=>(0,d.jsx)("div",{className:"bg-white rounded-xl p-6 shadow-sm border animate-pulse",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-2"}),(0,d.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16"})]}),(0,d.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-lg"})]})},b)):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(g.CN,{title:"Th\xe0nh vi\xean",value:b.totalUsers,icon:(0,d.jsx)(t.A,{size:24}),color:"green"}),(0,d.jsx)(g.CN,{title:"Đang hoạt động",value:b.activeUsers,icon:(0,d.jsx)(A,{size:24}),color:"blue"})]})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Thao t\xe1c nhanh"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:y.map((a,b)=>(0,d.jsx)(g.Lz,{title:a.title,description:a.description,href:a.href,icon:a.icon,color:a.color},b))})]}),(0,d.jsxs)(g.Zp,{children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Hoạt động gần đ\xe2y"}),(0,d.jsx)("a",{href:"#",className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:"Xem tất cả"})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:q?Array.from({length:8}).map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center space-x-3 p-3 rounded-lg border border-gray-100 animate-pulse",children:[(0,d.jsx)("div",{className:"w-2 h-2 rounded-full bg-gray-200"}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-1"}),(0,d.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]},b)):o.length>0?o.map(a=>{let b=(a=>{let b=new Date,c=new Date(a),d=Math.floor((b.getTime()-c.getTime())/1e3);return d<60?`${d} gi\xe2y trước`:d<3600?`${Math.floor(d/60)} ph\xfat trước`:d<86400?`${Math.floor(d/3600)} giờ trước`:d<2592e3?`${Math.floor(d/86400)} ng\xe0y trước`:`${Math.floor(d/2592e3)} th\xe1ng trước`})(a.time),c=(a=>{switch(a){case"post_created":case"page_created":return{color:"bg-green-500",icon:"create"};case"post_updated":case"page_updated":return{color:"bg-blue-500",icon:"update"};case"post_pending":return{color:"bg-orange-500",icon:"pending"};case"post_pending_updated":return{color:"bg-orange-400",icon:"pending"};case"user_registered":return{color:"bg-purple-500",icon:"user"};default:return{color:"bg-gray-500",icon:"default"}}})(a.type);return(0,d.jsxs)("div",{className:"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 border border-gray-100",children:[(0,d.jsx)("div",{className:`w-2 h-2 rounded-full ${c.color}`}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"text-sm text-gray-900",children:a.action}),(0,d.jsxs)("p",{className:"text-xs text-gray-500",children:[a.title&&(0,d.jsx)("span",{className:"font-medium",children:a.title}),a.title&&" • ","bởi ",a.user," • ",b]})]})]},a.id)}):(0,d.jsxs)("div",{className:"col-span-full text-center py-8 text-gray-500",children:[(0,d.jsx)(A,{size:48,className:"mx-auto mb-2 opacity-50"}),(0,d.jsx)("p",{children:"Chưa c\xf3 hoạt động n\xe0o"})]})})]})]})})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:a=>{"use strict";a.exports=require("buffer")},80755:(a,b,c)=>{"use strict";c.d(b,{Ex:()=>e,eG:()=>f});var d=c(21124);c(38301);let e=({children:a,variant:b="default",size:c="md",className:e="",dot:f=!1})=>(0,d.jsxs)("span",{className:`
        inline-flex items-center font-medium rounded-full
        ${{default:"bg-gray-100 text-gray-800",success:"bg-green-100 text-green-800",warning:"bg-yellow-100 text-yellow-800",danger:"bg-red-100 text-red-800",info:"bg-blue-100 text-blue-800",secondary:"bg-purple-100 text-purple-800"}[b]}
        ${{sm:"px-2 py-1 text-xs",md:"px-3 py-1 text-sm",lg:"px-4 py-2 text-base"}[c]}
        ${e}
      `,children:[f&&(0,d.jsx)("span",{className:`w-2 h-2 rounded-full mr-2 ${{default:"bg-gray-500",success:"bg-green-500",warning:"bg-yellow-500",danger:"bg-red-500",info:"bg-blue-500",secondary:"bg-purple-500"}[b]}`}),a]}),f=({role:a,className:b=""})=>{let c={admin:{label:"Quản trị vi\xean",variant:"danger"},department_manager:{label:"Quản l\xfd ph\xf2ng ban",variant:"warning"},department_member:{label:"Th\xe0nh vi\xean ph\xf2ng ban",variant:"info"},member:{label:"Th\xe0nh vi\xean",variant:"info"},manager:{label:"Quản l\xfd",variant:"info"},editor:{label:"Bi\xean tập vi\xean",variant:"secondary"},user:{label:"Người d\xf9ng",variant:"default"}},f=c[a]||c.user;return(0,d.jsx)(e,{variant:f.variant,className:b,children:f.label})}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91100:(a,b,c)=>{Promise.resolve().then(c.bind(c,59742))},93517:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\src\\\\app\\\\(private)\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\page.tsx","default")},94684:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[5873,1428,4479,6975,3445,3271],()=>b(b.s=33128));module.exports=c})();