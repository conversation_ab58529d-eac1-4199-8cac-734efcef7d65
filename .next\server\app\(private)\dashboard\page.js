(()=>{var a={};a.id=8152,a.ids=[8152],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32956:(a,b,c)=>{Promise.resolve().then(c.bind(c,93517))},33128:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(49754),e=c(9117),f=c(46595),g=c(32324),h=c(39326),i=c(38928),j=c(20175),k=c(12),l=c(54290),m=c(12696),n=c(82802),o=c(77533),p=c(45229),q=c(32822),r=c(261),s=c(26453),t=c(52474),u=c(26713),v=c(51356),w=c(62685),x=c(36225),y=c(63446),z=c(2762),A=c(45742),B=c(86439),C=c(81170),D=c.n(C),E=c(62506),F=c(91203),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H={children:["",{children:["(private)",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,93517)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,11841)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,51472)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,5682)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,81170,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.bind(c,59732)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,I=["C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(private)/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/(private)/dashboard/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},33873:a=>{"use strict";a.exports=require("path")},34027:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>r});var d=c(21124),e=c(38301),f=c.n(e),g=c(2537),h=c(80729),i=c(7372),j=c.n(i);function k(){return(k=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var l=(0,e.forwardRef)(function(a,b){var c=a.color,d=a.size,e=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return f().createElement("svg",k({ref:b,xmlns:"http://www.w3.org/2000/svg",width:e,height:e,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),f().createElement("path",{d:"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"}),f().createElement("polyline",{points:"14 2 14 8 20 8"}),f().createElement("line",{x1:"16",y1:"13",x2:"8",y2:"13"}),f().createElement("line",{x1:"16",y1:"17",x2:"8",y2:"17"}),f().createElement("polyline",{points:"10 9 9 9 8 9"}))});l.propTypes={color:j().string,size:j().oneOfType([j().string,j().number])},l.displayName="FileText";var m=c(4361);function n(){return(n=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var o=(0,e.forwardRef)(function(a,b){var c=a.color,d=a.size,e=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return f().createElement("svg",n({ref:b,xmlns:"http://www.w3.org/2000/svg",width:e,height:e,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),f().createElement("path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"}))});function p(){return(p=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}o.propTypes={color:j().string,size:j().oneOfType([j().string,j().number])},o.displayName="Shield";var q=(0,e.forwardRef)(function(a,b){var c=a.color,d=a.size,e=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return f().createElement("svg",p({ref:b,xmlns:"http://www.w3.org/2000/svg",width:e,height:e,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),f().createElement("polyline",{points:"22 12 18 12 15 21 9 3 6 12 2 12"}))});q.propTypes={color:j().string,size:j().oneOfType([j().string,j().number])},q.displayName="Activity",!function(){var a=Error("Cannot find module '@/components/ui/Card'");throw a.code="MODULE_NOT_FOUND",a}(),function(){var a=Error("Cannot find module '@/apiRequests/admin'");throw a.code="MODULE_NOT_FOUND",a}(),function(){var a=Error("Cannot find module '@/components/Dashboard/RoleDashboard'");throw a.code="MODULE_NOT_FOUND",a}(),function(){var a=Error("Cannot find module '@/app/app-provider'");throw a.code="MODULE_NOT_FOUND",a}();let r=()=>{let{user:a}=Object(function(){var a=Error("Cannot find module '@/app/app-provider'");throw a.code="MODULE_NOT_FOUND",a}())(),[b,c]=(0,e.useState)({totalUsers:0,activeUsers:0}),[f,i]=(0,e.useState)(!0),[j,k]=(0,e.useState)(""),[n,p]=(0,e.useState)(!1),[r,s]=(0,e.useState)([]),[t,u]=(0,e.useState)(!0);(0,e.useEffect)(()=>{k(localStorage.getItem("sessionToken")||"")},[]),(0,e.useEffect)(()=>{if(!j||n||!a||"admin"!==a.rule)return;let b=setTimeout(async()=>{try{i(!0),console.log("Fetching dashboard stats...");let a=await Object(function(){var a=Error("Cannot find module '@/apiRequests/admin'");throw a.code="MODULE_NOT_FOUND",a}())(j);console.log("API Response:",a),a.payload.success?(console.log("Dashboard stats received:",a.payload.stats),c(a.payload.stats),p(!0),g.oR.success("Đ\xe3 tải thống k\xea th\xe0nh c\xf4ng")):g.oR.error("Kh\xf4ng thể tải thống k\xea dashboard")}catch(a){console.error("Error fetching dashboard stats:",a),c({totalUsers:156,activeUsers:89}),p(!0),a?.status===429?g.oR.warning("Qu\xe1 nhiều y\xeau cầu, sử dụng dữ liệu cache"):g.oR.warning("Lỗi API, sử dụng dữ liệu mẫu")}finally{i(!1)}},1e3);return()=>clearTimeout(b)},[j,a]),(0,e.useEffect)(()=>{if(!j||!a||"admin"!==a.rule)return;let b=setTimeout(async()=>{try{u(!0);let a=await Object(function(){var a=Error("Cannot find module '@/apiRequests/admin'");throw a.code="MODULE_NOT_FOUND",a}())(j,8);a.payload.success?s(a.payload.activities):g.oR.error("Kh\xf4ng thể tải hoạt động gần đ\xe2y")}catch(a){console.error("Error fetching recent activities:",a),a?.status===429?g.oR.warning("Qu\xe1 nhiều y\xeau cầu, vui l\xf2ng thử lại sau"):g.oR.error("Lỗi khi tải hoạt động gần đ\xe2y")}finally{u(!1)}},1500);return()=>clearTimeout(b)},[j,a]);let v=[{title:"Quản l\xfd th\xe0nh vi\xean",description:"Xem v\xe0 quản l\xfd t\xe0i khoản người d\xf9ng",href:"/dashboard/user",icon:(0,d.jsx)(h.A,{size:20}),color:"green"},{title:"Quản l\xfd vụ việc",description:"Quản l\xfd vụ việc t\xf2a \xe1n v\xe0 thụ l\xfd",href:"/dashboard/court-cases",icon:(0,d.jsx)(l,{size:20}),color:"purple"},{title:"C\xe0i đặt hệ thống",description:"Cấu h\xecnh v\xe0 t\xf9y chỉnh website",href:"/dashboard/setting",icon:(0,d.jsx)(m.A,{size:20}),color:"orange"},{title:"Quản l\xfd file",description:"Quản l\xfd t\xe0i liệu v\xe0 h\xecnh ảnh",href:"/dashboard/files",icon:(0,d.jsx)(l,{size:20}),color:"blue"}];return a&&"admin"!==a.rule?(0,d.jsx)(Object(function(){var a=Error("Cannot find module '@/components/Dashboard/RoleDashboard'");throw a.code="MODULE_NOT_FOUND",a}()),{}):(0,d.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,d.jsxs)("div",{className:"space-y-8",children:[(0,d.jsx)("div",{className:"bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl text-white p-8",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h1",{className:"text-3xl font-bold mb-2 flex items-center",children:[(0,d.jsx)(o,{className:"mr-3",size:36}),"Bảng điều khiển quản trị"]}),(0,d.jsx)("p",{className:"text-purple-100 text-lg",children:"Quản l\xfd to\xe0n bộ hệ thống v\xe0 nội dung website"})]}),(0,d.jsx)("div",{className:"hidden md:block",children:(0,d.jsx)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-4",children:(0,d.jsx)(q,{size:48,className:"text-white"})})})]})}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6",children:f?Array.from({length:6}).map((a,b)=>(0,d.jsx)("div",{className:"bg-white rounded-xl p-6 shadow-sm border animate-pulse",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-2"}),(0,d.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16"})]}),(0,d.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-lg"})]})},b)):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(Object(function(){var a=Error("Cannot find module '@/components/ui/Card'");throw a.code="MODULE_NOT_FOUND",a}()),{title:"Th\xe0nh vi\xean",value:b.totalUsers,icon:(0,d.jsx)(h.A,{size:24}),color:"green"}),(0,d.jsx)(Object(function(){var a=Error("Cannot find module '@/components/ui/Card'");throw a.code="MODULE_NOT_FOUND",a}()),{title:"Đang hoạt động",value:b.activeUsers,icon:(0,d.jsx)(q,{size:24}),color:"blue"})]})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Thao t\xe1c nhanh"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:v.map((a,b)=>(0,d.jsx)(Object(function(){var a=Error("Cannot find module '@/components/ui/Card'");throw a.code="MODULE_NOT_FOUND",a}()),{title:a.title,description:a.description,href:a.href,icon:a.icon,color:a.color},b))})]}),(0,d.jsxs)(Object(function(){var a=Error("Cannot find module '@/components/ui/Card'");throw a.code="MODULE_NOT_FOUND",a}()),{children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Hoạt động gần đ\xe2y"}),(0,d.jsx)("a",{href:"#",className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:"Xem tất cả"})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:t?Array.from({length:8}).map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center space-x-3 p-3 rounded-lg border border-gray-100 animate-pulse",children:[(0,d.jsx)("div",{className:"w-2 h-2 rounded-full bg-gray-200"}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-1"}),(0,d.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]},b)):r.length>0?r.map(a=>{let b=(a=>{let b=new Date,c=new Date(a),d=Math.floor((b.getTime()-c.getTime())/1e3);return d<60?`${d} gi\xe2y trước`:d<3600?`${Math.floor(d/60)} ph\xfat trước`:d<86400?`${Math.floor(d/3600)} giờ trước`:d<2592e3?`${Math.floor(d/86400)} ng\xe0y trước`:`${Math.floor(d/2592e3)} th\xe1ng trước`})(a.time),c=(a=>{switch(a){case"post_created":case"page_created":return{color:"bg-green-500",icon:"create"};case"post_updated":case"page_updated":return{color:"bg-blue-500",icon:"update"};case"post_pending":return{color:"bg-orange-500",icon:"pending"};case"post_pending_updated":return{color:"bg-orange-400",icon:"pending"};case"user_registered":return{color:"bg-purple-500",icon:"user"};default:return{color:"bg-gray-500",icon:"default"}}})(a.type);return(0,d.jsxs)("div",{className:"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 border border-gray-100",children:[(0,d.jsx)("div",{className:`w-2 h-2 rounded-full ${c.color}`}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"text-sm text-gray-900",children:a.action}),(0,d.jsxs)("p",{className:"text-xs text-gray-500",children:[a.title&&(0,d.jsx)("span",{className:"font-medium",children:a.title}),a.title&&" • ","bởi ",a.user," • ",b]})]})]},a.id)}):(0,d.jsxs)("div",{className:"col-span-full text-center py-8 text-gray-500",children:[(0,d.jsx)(q,{size:48,className:"mx-auto mb-2 opacity-50"}),(0,d.jsx)("p",{children:"Chưa c\xf3 hoạt động n\xe0o"})]})})]})]})})}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91100:(a,b,c)=>{Promise.resolve().then(c.bind(c,34027))},93517:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\src\\\\app\\\\(private)\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\page.tsx","default")}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[5873,1428,5607,6040],()=>b(b.s=33128));module.exports=c})();