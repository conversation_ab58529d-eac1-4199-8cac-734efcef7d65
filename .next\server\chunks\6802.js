"use strict";exports.id=6802,exports.ids=[6802],exports.modules={14230:(a,b,c)=>{Object.defineProperty(b,"U",{enumerable:!0,get:function(){return n}});let d=c(28706),e=c(33675),f=c(29294),g=c(63033),h=c(26906),i=c(11938),j=c(82831),k=c(30787),l=c(31716),m=c(49290);function n(){let a="cookies",b=f.workAsyncStorage.getStore(),c=g.workUnitAsyncStorage.getStore();if(b){if(c&&"after"===c.phase&&!(0,l.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${b.route} used "cookies" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "cookies" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E88",enumerable:!1,configurable:!0});if(b.forceStatic)return p(d.RequestCookiesAdapter.seal(new e.RequestCookies(new Headers({}))));if(b.dynamicShouldError)throw Object.defineProperty(new i.StaticGenBailoutError(`Route ${b.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`cookies\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E549",enumerable:!1,configurable:!0});if(c)switch(c.type){case"cache":let f=Object.defineProperty(Error(`Route ${b.route} used "cookies" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E398",enumerable:!1,configurable:!0});throw Error.captureStackTrace(f,n),b.invalidDynamicUsageError??=f,f;case"unstable-cache":throw Object.defineProperty(Error(`Route ${b.route} used "cookies" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "cookies" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E157",enumerable:!1,configurable:!0});case"prerender":var k=b,q=c;let g=o.get(q);if(g)return g;let r=(0,j.makeHangingPromise)(q.renderSignal,k.route,"`cookies()`");return o.set(q,r),r;case"prerender-client":let s="`cookies`";throw Object.defineProperty(new m.InvariantError(`${s} must not be used within a client component. Next.js should be preventing ${s} from being included in client components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0});case"prerender-ppr":return(0,h.postponeWithTracking)(b.route,a,c.dynamicTracking);case"prerender-legacy":return(0,h.throwToInterruptStaticGeneration)(a,b,c);case"prerender-runtime":return(0,h.delayUntilRuntimeStage)(c,function(a){let b=o.get(a);if(b)return b;let c=Promise.resolve(a);return o.set(a,c),c}(c.cookies));case"private-cache":return p(c.cookies);case"request":return(0,h.trackDynamicDataInDynamicRender)(c),p((0,d.areCookiesMutableInCurrentPhase)(c)?c.userspaceMutableCookies:c.cookies)}}(0,g.throwForMissingRequestStore)(a)}c(63036);let o=new WeakMap;function p(a){let b=o.get(a);if(b)return b;let c=Promise.resolve(a);return o.set(a,c),Object.defineProperties(c,{[Symbol.iterator]:{value:a[Symbol.iterator]?a[Symbol.iterator].bind(a):q.bind(a)},size:{get:()=>a.size},get:{value:a.get.bind(a)},getAll:{value:a.getAll.bind(a)},has:{value:a.has.bind(a)},set:{value:a.set.bind(a)},delete:{value:a.delete.bind(a)},clear:{value:"function"==typeof a.clear?a.clear.bind(a):r.bind(a,c)},toString:{value:a.toString.bind(a)}}),c}function q(){return this.getAll().map(a=>[a.name,a]).values()}function r(a){for(let a of this.getAll())this.delete(a.name);return a}(0,k.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})})},28706:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{MutableRequestCookiesAdapter:function(){return l},ReadonlyRequestCookiesError:function(){return g},RequestCookiesAdapter:function(){return h},appendMutableCookies:function(){return k},areCookiesMutableInCurrentPhase:function(){return n},createCookiesWithMutableAccessCheck:function(){return m},getModifiedCookieValues:function(){return j},responseCookiesToRequestCookies:function(){return p}});let d=c(33675),e=c(63036),f=c(29294);class g extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new g}}class h{static seal(a){return new Proxy(a,{get(a,b,c){switch(b){case"clear":case"delete":case"set":return g.callable;default:return e.ReflectAdapter.get(a,b,c)}}})}}let i=Symbol.for("next.mutated.cookies");function j(a){let b=a[i];return b&&Array.isArray(b)&&0!==b.length?b:[]}function k(a,b){let c=j(b);if(0===c.length)return!1;let e=new d.ResponseCookies(a),f=e.getAll();for(let a of c)e.set(a);for(let a of f)e.set(a);return!0}class l{static wrap(a,b){let c=new d.ResponseCookies(new Headers);for(let b of a.getAll())c.set(b);let g=[],h=new Set,j=()=>{let a=f.workAsyncStorage.getStore();if(a&&(a.pathWasRevalidated=!0),g=c.getAll().filter(a=>h.has(a.name)),b){let a=[];for(let b of g){let c=new d.ResponseCookies(new Headers);c.set(b),a.push(c.toString())}b(a)}},k=new Proxy(c,{get(a,b,c){switch(b){case i:return g;case"delete":return function(...b){h.add("string"==typeof b[0]?b[0]:b[0].name);try{return a.delete(...b),k}finally{j()}};case"set":return function(...b){h.add("string"==typeof b[0]?b[0]:b[0].name);try{return a.set(...b),k}finally{j()}};default:return e.ReflectAdapter.get(a,b,c)}}});return k}}function m(a){let b=new Proxy(a.mutableCookies,{get(c,d,f){switch(d){case"delete":return function(...d){return o(a,"cookies().delete"),c.delete(...d),b};case"set":return function(...d){return o(a,"cookies().set"),c.set(...d),b};default:return e.ReflectAdapter.get(c,d,f)}}});return b}function n(a){return"action"===a.phase}function o(a,b){if(!n(a))throw new g}function p(a){let b=new d.RequestCookies(new Headers);for(let c of a.getAll())b.set(c);return b}},34603:(a,b,c)=>{let d=c(67675),e=c(29294),f=c(63033),g=c(26906),h=c(11938),i=c(82831),j=c(30787),k=c(31716),l=c(49290);c(63036);new WeakMap;(0,j.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})})},35916:(a,b,c)=>{let d=c(63033),e=c(29294),f=c(26906),g=c(30787),h=c(11938),i=c(69168),j=c(49290);c(63036);new WeakMap;(0,g.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})})},86802:(a,b,c)=>{c.d(b,{UL:()=>d.U});var d=c(14230);c(34603),c(35916)}};