(()=>{var a={};a.id=4736,a.ids=[4736],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1590:(a,b,c)=>{"use strict";c.d(b,{lV:()=>l,MJ:()=>s,zB:()=>n,eI:()=>q,lR:()=>r,C5:()=>t});var d=c(21124),e=c(38301),f=c(70469),g=c(31980),h=c(44943),i=c(71498);let j=(0,c(26691).F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mb-2"),k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(i.b,{ref:c,className:(0,h.cn)(j(),a),...b}));k.displayName=i.b.displayName;let l=g.Op,m=e.createContext({}),n=({...a})=>(0,d.jsx)(m.Provider,{value:{name:a.name},children:(0,d.jsx)(g.xI,{...a})}),o=()=>{let a=e.useContext(m),b=e.useContext(p),{getFieldState:c,formState:d}=(0,g.xW)(),f=c(a.name,d);if(!a)throw Error("useFormField should be used within <FormField>");let{id:h}=b;return{id:h,name:a.name,formItemId:`${h}-form-item`,formDescriptionId:`${h}-form-item-description`,formMessageId:`${h}-form-item-message`,...f}},p=e.createContext({}),q=e.forwardRef(({className:a,...b},c)=>{let f=e.useId();return(0,d.jsx)(p.Provider,{value:{id:f},children:(0,d.jsx)("div",{ref:c,className:(0,h.cn)("mb-4",a),...b})})});q.displayName="FormItem";let r=e.forwardRef(({className:a,...b},c)=>{let{error:e,formItemId:f}=o();return(0,d.jsx)(k,{ref:c,className:(0,h.cn)(e&&"text-destructive",a),htmlFor:f,...b})});r.displayName="FormLabel";let s=e.forwardRef(({...a},b)=>{let{error:c,formItemId:e,formDescriptionId:g,formMessageId:h}=o();return(0,d.jsx)(f.DX,{ref:b,id:e,"aria-describedby":c?`${g} ${h}`:`${g}`,"aria-invalid":!!c,...a})});s.displayName="FormControl",e.forwardRef(({className:a,...b},c)=>{let{formDescriptionId:e}=o();return(0,d.jsx)("p",{ref:c,id:e,className:(0,h.cn)("text-[0.8rem] text-muted-foreground",a),...b})}).displayName="FormDescription";let t=e.forwardRef(({className:a,children:b,...c},e)=>{let{error:f,formMessageId:g}=o(),i=f?String(f?.message):b;return i?(0,d.jsx)("p",{ref:e,id:g,className:(0,h.cn)("text-[0.8rem] font-medium text-red-600",a),...c,children:i}):null});t.displayName="FormMessage"},3146:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\src\\\\app\\\\(private)\\\\dashboard\\\\profile-form.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\profile-form.tsx","default")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},23496:(a,b,c)=>{Promise.resolve().then(c.bind(c,79224))},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29411:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(38301),e=c.n(d),f=c(7372),g=c.n(f);function h(){return(h=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var i=(0,d.forwardRef)(function(a,b){var c=a.color,d=a.size,f=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return e().createElement("svg",h({ref:b,xmlns:"http://www.w3.org/2000/svg",width:f,height:f,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),e().createElement("path",{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"}),e().createElement("line",{x1:"1",y1:"1",x2:"23",y2:"23"}))});i.propTypes={color:g().string,size:g().oneOfType([g().string,g().number])},i.displayName="EyeOff";let j=i},33873:a=>{"use strict";a.exports=require("path")},35646:(a,b,c)=>{"use strict";c.d(b,{j$:()=>f,Ay:()=>j});var d=c(47960);c(48318);var e=c(82161);class f extends Error{constructor({status:a,payload:b}){super("Http Error"),this.status=a,this.payload=b}}class g extends f{constructor({status:a,payload:b}){super({status:a,payload:b}),this.status=a,this.payload=b}}let h=null,i=async(a,b,c)=>{let i;c?.body instanceof FormData?i=c.body:c?.body&&(i=JSON.stringify(c.body));let j=i instanceof FormData?{}:{"Content-Type":"application/json"},k=c?.baseUrl===void 0?d.A.NEXT_PUBLIC_API_ENDPOINT:c.baseUrl,l=b.startsWith("/")?`${k}${b}`:`${k}/${b}`,m=await fetch(l,{...c,headers:{...j,...c?.headers},body:i,method:a}),n=null,o=m.headers.get("content-type");if(o&&o.includes("application/json"))try{n=await m.json()}catch(a){console.error("Failed to parse JSON response:",a),n=null}else n=await m.text();let p={status:m.status,payload:n};if(!m.ok)if(404===m.status||403===m.status)throw new g(p);else if(401===m.status){if(1){let a="";{let b=c?.headers?.Authorization;b?.startsWith("Bearer ")&&(a=b.split("Bearer ")[1])}(0,e.redirect)(`/logout?sessionToken=${a}`)}else if(!h){h=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...j}});try{let a=async a=>{if("https://flscott.com"!==a.origin)return};window.addEventListener("message",a),await h}catch(a){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),h=null,location.href="/login"}}}else throw new f(p);return p},j={get:(a,b)=>i("GET",a,b),post:(a,b,c)=>i("POST",a,{...c,body:b}),put:(a,b,c)=>i("PUT",a,{...c,body:b}),patch:(a,b,c)=>i("PATCH",a,{...c,body:b}),delete:(a,b)=>i("DELETE",a,{...b})}},36815:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>i});var d=c(75338),e=c(86802),f=c(3146),g=c(35646);let h={meAuth:a=>g.Ay.get("/api/auth/user/profile",{headers:{Authorization:`Bearer ${a}`}})};async function i(){let a=(0,e.UL)().get("sessionToken"),b=await h.meAuth(a?.value??"");return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("h1",{className:"text-2xl",children:"Th\xf4ng tin t\xe0i khoản"}),(0,d.jsx)(f.default,{profile:b.payload.user})]})}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},47960:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(45711);let e=d.Ik({NEXT_PUBLIC_API_ENDPOINT:d.Yj().url(),NEXT_PUBLIC_URL:d.Yj().url(),CRYPTOJS_SECRECT:d.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"https://flscott.com",NEXT_PUBLIC_URL:"https://flscott.com",CRYPTOJS_SECRECT:process.env.CRYPTOJS_SECRECT});if(!e.success)throw console.error("Invalid environment variables:",e.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let f=e.data},51019:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(38301),e=c.n(d),f=c(7372),g=c.n(f);function h(){return(h=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var i=(0,d.forwardRef)(function(a,b){var c=a.color,d=a.size,f=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return e().createElement("svg",h({ref:b,xmlns:"http://www.w3.org/2000/svg",width:f,height:f,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),e().createElement("line",{x1:"12",y1:"2",x2:"12",y2:"6"}),e().createElement("line",{x1:"12",y1:"18",x2:"12",y2:"22"}),e().createElement("line",{x1:"4.93",y1:"4.93",x2:"7.76",y2:"7.76"}),e().createElement("line",{x1:"16.24",y1:"16.24",x2:"19.07",y2:"19.07"}),e().createElement("line",{x1:"2",y1:"12",x2:"6",y2:"12"}),e().createElement("line",{x1:"18",y1:"12",x2:"22",y2:"12"}),e().createElement("line",{x1:"4.93",y1:"19.07",x2:"7.76",y2:"16.24"}),e().createElement("line",{x1:"16.24",y1:"7.76",x2:"19.07",y2:"4.93"}))});i.propTypes={color:g().string,size:g().oneOfType([g().string,g().number])},i.displayName="Loader";let j=i},55511:a=>{"use strict";a.exports=require("crypto")},58015:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(38301),e=c.n(d),f=c(7372),g=c.n(f);function h(){return(h=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var i=(0,d.forwardRef)(function(a,b){var c=a.color,d=a.size,f=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return e().createElement("svg",h({ref:b,xmlns:"http://www.w3.org/2000/svg",width:f,height:f,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),e().createElement("path",{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"}),e().createElement("circle",{cx:"12",cy:"12",r:"3"}))});i.propTypes={color:g().string,size:g().oneOfType([g().string,g().number])},i.displayName="Eye";let j=i},58066:(a,b,c)=>{"use strict";c.d(b,{Ay:()=>j});var d={};c.r(d),c.d(d,{BRAND:()=>h.qt,DIRTY:()=>f.jm,EMPTY_PATH:()=>f.I3,INVALID:()=>f.uY,NEVER:()=>h.tm,OK:()=>f.OK,ParseStatus:()=>f.MY,Schema:()=>h.Sj,ZodAny:()=>h.Ml,ZodArray:()=>h.n,ZodBigInt:()=>h.Lr,ZodBoolean:()=>h.WF,ZodBranded:()=>h.eN,ZodCatch:()=>h.hw,ZodDate:()=>h.aP,ZodDefault:()=>h.Xi,ZodDiscriminatedUnion:()=>h.jv,ZodEffects:()=>h.k1,ZodEnum:()=>h.Vb,ZodError:()=>i.G,ZodFirstPartyTypeKind:()=>h.kY,ZodFunction:()=>h.CZ,ZodIntersection:()=>h.Jv,ZodIssueCode:()=>i.eq,ZodLazy:()=>h.Ih,ZodLiteral:()=>h.DN,ZodMap:()=>h.Ut,ZodNaN:()=>h.Tq,ZodNativeEnum:()=>h.WM,ZodNever:()=>h.iS,ZodNull:()=>h.PQ,ZodNullable:()=>h.l1,ZodNumber:()=>h.rS,ZodObject:()=>h.bv,ZodOptional:()=>h.Ii,ZodParsedType:()=>g.Zp,ZodPipeline:()=>h._c,ZodPromise:()=>h.$i,ZodReadonly:()=>h.EV,ZodRecord:()=>h.b8,ZodSchema:()=>h.lK,ZodSet:()=>h.Kz,ZodString:()=>h.ND,ZodSymbol:()=>h.K5,ZodTransformer:()=>h.BG,ZodTuple:()=>h.y0,ZodType:()=>h.aR,ZodUndefined:()=>h._Z,ZodUnion:()=>h.fZ,ZodUnknown:()=>h._,ZodVoid:()=>h.a0,addIssueToContext:()=>f.zn,any:()=>h.bz,array:()=>h.YO,bigint:()=>h.o,boolean:()=>h.zM,coerce:()=>h.au,custom:()=>h.Ie,date:()=>h.p6,datetimeRegex:()=>h.fm,defaultErrorMap:()=>e.su,discriminatedUnion:()=>h.gM,effect:()=>h.QZ,enum:()=>h.k5,function:()=>h.fH,getErrorMap:()=>e.$W,getParsedType:()=>g.CR,instanceof:()=>h.Nl,intersection:()=>h.E$,isAborted:()=>f.G4,isAsync:()=>f.xP,isDirty:()=>f.DM,isValid:()=>f.fn,late:()=>h.fn,lazy:()=>h.RZ,literal:()=>h.eu,makeIssue:()=>f.y7,map:()=>h.Tj,nan:()=>h.oi,nativeEnum:()=>h.fc,never:()=>h.Zm,null:()=>h.ch,nullable:()=>h.me,number:()=>h.ai,object:()=>h.Ik,objectUtil:()=>g.o6,oboolean:()=>h.yN,onumber:()=>h.p7,optional:()=>h.lq,ostring:()=>h.Di,pipeline:()=>h.Tk,preprocess:()=>h.vk,promise:()=>h.iv,quotelessJson:()=>i.WI,record:()=>h.g1,set:()=>h.hZ,setErrorMap:()=>e.pJ,strictObject:()=>h.re,string:()=>h.Yj,symbol:()=>h.HR,transformer:()=>h.Gu,tuple:()=>h.PV,undefined:()=>h.Vx,union:()=>h.KC,unknown:()=>h.L5,util:()=>g.ZS,void:()=>h.rI});var e=c(41867),f=c(51399),g=c(82826),h=c(65923),i=c(12377);let j=d},59944:(a,b,c)=>{Promise.resolve().then(c.bind(c,3146))},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71498:(a,b,c)=>{"use strict";c.d(b,{b:()=>i});var d=c(38301);c(23312);var e=c(70469),f=c(21124),g=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,b)=>{let c=(0,e.TL)(`Primitive.${b}`),g=d.forwardRef((a,d)=>{let{asChild:e,...g}=a;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,f.jsx)(e?c:b,{...g,ref:d})});return g.displayName=`Primitive.${b}`,{...a,[b]:g}},{}),h=d.forwardRef((a,b)=>(0,f.jsx)(g.label,{...a,ref:b,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));h.displayName="Label";var i=h},79224:(a,b,c)=>{"use strict";c.d(b,{default:()=>w});var d=c(21124),e=c(55550),f=c(31980),g=c(1590),h=c(93758),i=c(42378),j=c(38301),k=c(58066);k.Ay.object({user:k.Ay.object({_id:k.Ay.number(),username:k.Ay.string(),email:k.Ay.string(),rule:k.Ay.string(),permissions:k.Ay.array(k.Ay.string()).optional()}),message:k.Ay.string()}).strict(),k.Ay.object({user:k.Ay.object({_id:k.Ay.number(),username:k.Ay.string(),email:k.Ay.string(),rule:k.Ay.string(),isMail:k.Ay.boolean(),isAuthApp:k.Ay.boolean(),permissions:k.Ay.array(k.Ay.string()).optional(),department:k.Ay.object({_id:k.Ay.string(),name:k.Ay.string(),code:k.Ay.string()}).optional(),departmentRole:k.Ay.string().optional()}),message:k.Ay.string()}).strict();let l=k.Ay.object({username:k.Ay.string().trim().min(2).max(256)}),m=k.Ay.object({password:k.Ay.string().min(6).max(100),newPassword:k.Ay.string().min(6).max(100),confirmPassword:k.Ay.string().min(6).max(100)}).superRefine(({confirmPassword:a,newPassword:b},c)=>{a!==b&&c.addIssue({code:"custom",message:"Confirm password incorrect",path:["confirmPassword"]})}),n=k.Ay.object({isMail:k.Ay.boolean()}),o=k.Ay.object({isAuthApp:k.Ay.boolean()}),p=k.Ay.object({token:k.Ay.string()});var q=c(65424);let r={updateMe:(a,b)=>q.Ay.put("/api/auth/user/profile",a,{headers:{Authorization:`Bearer ${b}`}}),updateMePassword:(a,b)=>q.Ay.put("/api/auth/change-pass",a,{headers:{Authorization:`Bearer ${b}`}}),updateMeAuth:(a,b)=>q.Ay.put("/api/auth/active-mail",a,{headers:{Authorization:`Bearer ${b}`}}),updateMeAuthApp:(a,b)=>q.Ay.put("/api/auth/active-authapp",a,{headers:{Authorization:`Bearer ${b}`}}),verifyToken:(a,b)=>q.Ay.put("/api/auth/verify-authapp",a,{headers:{Authorization:`Bearer ${b}`}})};var s=c(51019),t=c(80974),u=c(89869),v=c(24515);let w=({profile:a})=>{console.log(a);let{setUser:b}=(0,u.U)(),[c,k]=(0,j.useState)(!1);(0,i.useRouter)();let[q,w]=(0,j.useState)(""),[x,y]=(0,j.useState)(""),[z,A]=(0,j.useState)(""),B=(0,j.useRef)(null),C=(0,f.mN)({resolver:(0,e.u)(l),defaultValues:{username:a.username}}),D=(0,f.mN)({resolver:(0,e.u)(m),defaultValues:{newPassword:"",password:"",confirmPassword:""}}),E=(0,f.mN)({resolver:(0,e.u)(n),defaultValues:{isMail:a.isMail}}),F=(0,f.mN)({resolver:(0,e.u)(o),defaultValues:{isAuthApp:a.isAuthApp}}),G=(0,f.mN)({resolver:(0,e.u)(p),defaultValues:{token:""}});async function H(a){if(!c){k(!0);try{let c=localStorage.getItem("sessionToken")||"",d=await r.updateMe(a,c);d&&(b(d.payload.user),t.oR.success("Profile update successful!"))}catch(a){console.log(a),t.oR.error("An error occurred during update your profile. Please try again.")}finally{k(!1)}}}async function I(a){if(!c){k(!0);try{let b=localStorage.getItem("sessionToken")||"";await r.updateMePassword(a,b)&&t.oR.success("Profile update successful!")}catch(a){t.oR.error("An error occurred during update your profile. Please try again.")}finally{k(!1)}}}async function J(a){if(!c){k(!0);try{let b=localStorage.getItem("sessionToken")||"";await r.updateMeAuth(a,b)&&t.oR.success("Profile update successful!")}catch(a){t.oR.error("An error occurred during update your profile. Please try again.")}finally{k(!1)}}}async function K(a){if(!c){k(!0);try{let b=localStorage.getItem("sessionToken")||"",c=await r.updateMeAuthApp(a,b);c&&(w(c.payload.qrCode),y(c.payload.secret),B.current&&B.current.showModal())}catch(a){t.oR.error("An error occurred during update your profile. Please try again.")}finally{k(!1)}}}async function L(b){if(!c){k(!0);try{let c=localStorage.getItem("sessionToken")||"";await r.verifyToken(b,c)&&(a.isAuthApp=!0,B.current&&B.current.close(),t.oR.success("Profile update successful!"))}catch(a){A("Invalid 2FA code")}finally{k(!1)}}}return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(g.lV,{...C,children:(0,d.jsxs)("form",{onSubmit:C.handleSubmit(H),className:"space-y-2 max-w-[600px] flex-shrink-0 w-full mx-auto",noValidate:!0,children:[(0,d.jsx)(g.lR,{children:"Email"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)(h.p,{placeholder:"shadcn",type:"email",value:a.email,readOnly:!0})}),(0,d.jsx)(g.C5,{}),(0,d.jsx)(g.zB,{control:C.control,name:"username",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Nick name"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)(h.p,{placeholder:"Nick name",type:"text",...a})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsxs)("button",{disabled:!!c,type:"submit",className:"btn btn-primary mt-8 bg-blue-700 w-40 text-white mx-auto flex items-center",children:[c?(0,d.jsx)(s.A,{className:"animate-spin"}):"","X\xe1c nhận"]})]})}),(0,d.jsx)(g.lV,{...D,children:(0,d.jsxs)("form",{onSubmit:D.handleSubmit(I),className:"space-y-2 max-w-[600px] flex-shrink-0 w-full mx-auto",noValidate:!0,children:[(0,d.jsx)(g.zB,{control:D.control,name:"password",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Mật khẩu cũ"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)(h.p,{placeholder:"password",type:"password",...a})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:D.control,name:"newPassword",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Mật khẩu mới"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)(h.p,{placeholder:"password",type:"password",...a})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:D.control,name:"confirmPassword",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"X\xe1c nhận mật khẩu"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)(h.p,{placeholder:"X\xe1c nhận mật khẩu",type:"password",...a})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsxs)("button",{disabled:!!c,type:"submit",className:"btn btn-primary mt-8 bg-blue-700 w-40 text-white mx-auto flex items-center",children:[c?(0,d.jsx)(s.A,{className:"animate-spin"}):"","X\xe1c nhận"]})]})}),(0,d.jsxs)(g.lV,{...E,children:[(0,d.jsx)("h3",{className:"text-2xl",children:"Bảo mật 2 lớp bằng Email"}),(0,d.jsxs)("form",{onSubmit:E.handleSubmit(J),className:"space-y-2 max-w-[600px] flex-shrink-0 w-full mx-auto my-4",noValidate:!0,children:[(0,d.jsx)("span",{className:"text-sm mb-4",children:'"K\xedch hoạt t\xednh năng bảo mật bằng Email: Một m\xe3 x\xe1c thực sẽ được gửi đến email của bạn khi đăng nhập."'}),(0,d.jsx)(g.zB,{control:E.control,name:"isMail",render:({field:a})=>(0,d.jsxs)(g.eI,{className:"flex items-center gap-2 justify-between py-2",children:[(0,d.jsx)("span",{children:"K\xedch hoạt bảo mật Qua Mail"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)("input",{type:"checkbox",checked:a.value??!1,onChange:b=>a.onChange(b.target.checked),className:"checkbox"})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsxs)("button",{disabled:!!c,type:"submit",className:"btn btn-primary mt-8 bg-blue-700 w-40 text-white mx-auto flex items-center",children:[c?(0,d.jsx)(s.A,{className:"animate-spin"}):"","X\xe1c nhận"]})]})]}),(0,d.jsx)("h3",{className:"text-2xl",children:"Bảo mật 2 lớp bằng App "}),(0,d.jsx)("dialog",{id:"my_modal_1",className:"modal ",ref:B,children:(0,d.jsxs)("div",{className:"modal-box relative ",children:[(0,d.jsx)("h3",{className:"text-lg font-bold",children:"Set Up Two-Factor Authentication"}),(0,d.jsx)("p",{className:"py-4",children:"Qu\xe9t m\xe3 QR b\xean dưới bằng ứng dụng x\xe1c thực của bạn (v\xed dụ: Google Authenticator, Authy).."}),q?(0,d.jsx)("div",{className:"flex justify-center",children:(0,d.jsx)(v.default,{src:q,alt:"QR Code",width:200,height:200,priority:!0})}):(0,d.jsx)("p",{children:"Loading QR code..."}),(0,d.jsx)(g.lV,{...G,children:(0,d.jsxs)("form",{onSubmit:G.handleSubmit(L),className:"space-y-2 max-w-[600px] flex-shrink-0 w-full mx-auto",noValidate:!0,children:[(0,d.jsx)(g.zB,{control:G.control,name:"token",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Nhập M\xe3 2FA Code"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)(h.p,{placeholder:"2FA Cod",type:"text",...a})}),(0,d.jsx)(g.C5,{})]})}),z&&(0,d.jsx)("p",{className:"text-red-500 mt-2",children:z}),(0,d.jsxs)("button",{disabled:!!c,type:"submit",className:"btn btn-primary mt-8 bg-blue-700 w-40 text-white mx-auto flex items-center",children:[c?(0,d.jsx)(s.A,{className:"animate-spin"}):"","X\xe1c nhận"]})]})}),(0,d.jsx)("form",{method:"dialog",children:(0,d.jsx)("button",{className:"btn btn-sm btn-circle btn-ghost absolute right-2 top-2",children:"✕"})})]})}),(0,d.jsx)(g.lV,{...F,children:(0,d.jsxs)("form",{onSubmit:F.handleSubmit(K),className:"space-y-2 max-w-[600px] flex-shrink-0 w-full mx-auto my-4",noValidate:!0,children:[(0,d.jsx)("span",{className:"text-sm mb-4",children:'"Khi bật 2FA, mỗi lần đăng nhập, bạn sẽ được y\xeau cầu sử dụng kh\xf3a bảo mật, nhập m\xe3 x\xe1c minh hoặc x\xe1c nhận đăng nhập từ thiết bị di động, t\xf9y theo phương thức bạn đ\xe3 chọn."'}),(0,d.jsx)(g.zB,{control:F.control,name:"isAuthApp",render:({field:a})=>(0,d.jsxs)(g.eI,{className:"flex items-center gap-2 justify-between py-2",children:[(0,d.jsx)("span",{children:"K\xedch hoạt bảo mật Qua ứng dụng"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)("input",{type:"checkbox",checked:a.value??!1,onChange:b=>a.onChange(b.target.checked),className:"checkbox"})}),(0,d.jsx)(g.C5,{})]})}),a.isAuthApp?null:(0,d.jsxs)("button",{disabled:!!c,type:"submit",className:"btn btn-primary mt-8 bg-blue-700 w-40 text-white mx-auto flex items-center",children:[c?(0,d.jsx)(s.A,{className:"animate-spin"}):"","X\xe1c nhận"]})]})})]})}},79428:a=>{"use strict";a.exports=require("buffer")},81300:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(49754),e=c(9117),f=c(46595),g=c(32324),h=c(39326),i=c(38928),j=c(20175),k=c(12),l=c(54290),m=c(12696),n=c(82802),o=c(77533),p=c(45229),q=c(32822),r=c(261),s=c(26453),t=c(52474),u=c(26713),v=c(51356),w=c(62685),x=c(36225),y=c(63446),z=c(2762),A=c(45742),B=c(86439),C=c(81170),D=c.n(C),E=c(62506),F=c(91203),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H={children:["",{children:["(private)",{children:["dashboard",{children:["account",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,36815)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\account\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,87473)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,51472)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,5682)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,81170,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.bind(c,59732)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,I=["C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\account\\page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(private)/dashboard/account/page",pathname:"/dashboard/account",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/(private)/dashboard/account/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},93758:(a,b,c)=>{"use strict";c.d(b,{p:()=>i});var d=c(21124),e=c(44943),f=c(29411),g=c(58015),h=c(38301);let i=c.n(h)().forwardRef(({className:a,type:b,...c},i)=>{let[j,k]=(0,h.useState)(!1);return(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)("div",{className:"relative w-full",children:[(0,d.jsx)("input",{type:"password"===b&&j?"text":b,autoComplete:"password"===b?"new-password":"",className:(0,e.cn)("input input-bordered w-full rounded-md",a),ref:i,...c}),"password"===b&&(j?(0,d.jsx)(f.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer",onClick:()=>k(!j)}):(0,d.jsx)(g.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer",onClick:()=>k(!j)}))]})})});i.displayName="Input"}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[5873,1428,4479,6975,1058,5711,1503,3445,3271],()=>b(b.s=81300));module.exports=c})();