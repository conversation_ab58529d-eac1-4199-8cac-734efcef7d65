const Department = require("../models/department");
const User = require("../models/user");
const { customAlphabet } = require("nanoid");
const bcrypt = require("bcryptjs");
const { ForbiddenError } = require("@casl/ability");
const { defineAbilityFor } = require("../permissions/abilities");
const { MASTER_PERMISSIONS } = require("../../../shared/permissions/masterPermissions.js");
const { syncUserPermissions } = require("../../utils/permissionSyncWrapper");

// Tạo phòng ban mới
exports.createDepartment = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    // Chỉ admin mới có thể tạo phòng ban
    if (req.user.rule !== 'admin') {
      return res.status(403).json({
        success: false,
        message: "Chỉ quản trị viên mới có thể tạo phòng ban mới.",
      });
    }

    const { name, description, defaultPermissions, managerId } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: "Tên phòng ban là bắt buộc.",
      });
    }

    // Kiểm tra tên phòng ban đã tồn tại
    const existingDept = await Department.findOne({ name });

    if (existingDept) {
      return res.status(400).json({
        success: false,
        message: "Tên phòng ban đã tồn tại.",
      });
    }

    // Kiểm tra manager nếu có
    let manager = null;
    if (managerId) {
      manager = await User.findById(managerId);
      if (!manager) {
        return res.status(400).json({
          success: false,
          message: "Không tìm thấy người quản lý được chỉ định.",
        });
      }
    }

    const department = new Department({
      name,
      description,
      defaultPermissions: defaultPermissions || [],
      manager: managerId || null,
      createdBy: req.user._id,
    });

    ForbiddenError.from(ability).throwUnlessCan("create", department);
    await department.save();

    // Cập nhật role cho manager nếu có
    if (manager) {
      manager.department = department._id;
      manager.rule = 'department_manager';
      manager.departmentRole = 'manager';
      await manager.save();
    }

    await department.populate('manager', 'username email');
    await department.populate('createdBy', 'username email');

    res.status(201).json({
      success: true,
      message: "Tạo phòng ban thành công.",
      department,
    });
  } catch (error) {
    console.error("Error creating department:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi tạo phòng ban.",
      error: error.message,
    });
  }
};

// Lấy danh sách tất cả phòng ban
exports.getAllDepartments = async (req, res) => {

  try {
    const { page = 1, perPage = 20, search = "" } = req.body;

    let filter = { isActive: true };

    // Nếu không phải admin, chỉ xem được phòng ban của mình
    if (req.user.rule !== 'admin') {
      if (req.user.department) {
        filter._id = req.user.department;
      } else {
        return res.json({
          success: true,
          departments: [],
          total: 0,
        });
      }
    }

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const skip = (page - 1) * perPage;

    const [departments, total] = await Promise.all([
      Department.find(filter)
        .populate('manager', 'username email')
        .populate('createdBy', 'username email')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(perPage),
      Department.countDocuments(filter)
    ]);

    // Calculate real-time member count for each department
    const departmentsWithMemberCount = await Promise.all(
      departments.map(async (dept) => {
        const memberCount = await User.countDocuments({ department: dept._id });
        return {
          ...dept.toObject(),
          memberCount
        };
      })
    );

    res.json({
      success: true,
      departments: departmentsWithMemberCount,
      total,
      page,
      perPage,
    });
  } catch (error) {
    console.error("Error getting departments:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi lấy danh sách phòng ban.",
      error: error.message,
    });
  }
};

// Lấy thông tin chi tiết phòng ban
exports.getDepartmentById = async (req, res) => {

  try {
    const { id } = req.params;

    const department = await Department.findById(id)
      .populate('manager', 'username email rule departmentRole')
      .populate('createdBy', 'username email')
      .populate({
        path: 'members',
        select: 'username email rule departmentRole createdAt',
        match: { isActive: { $ne: false } }
      });

    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy phòng ban.",
      });
    }

    // Kiểm tra quyền truy cập
    // Admin có thể xem tất cả phòng ban
    // Department manager và member chỉ có thể xem phòng ban của mình
    const userDepartmentId = req.user.department ? 
      (req.user.department._id ? req.user.department._id.toString() : req.user.department.toString()) 
      : null;
    
    const canViewDepartment = req.user.rule === 'admin' ||
      (userDepartmentId && userDepartmentId === id);

    if (!canViewDepartment) {
      return res.status(403).json({
        success: false,
        message: "Bạn không có quyền xem thông tin phòng ban này.",
      });
    }

    res.json({
      success: true,
      department,
    });
  } catch (error) {
    console.error("Error getting department:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi lấy thông tin phòng ban.",
      error: error.message,
    });
  }
};

// Cập nhật thông tin phòng ban
exports.updateDepartment = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    const { id } = req.params;
    const { name, description, defaultPermissions, managerId, syncMemberPermissions } = req.body;

    // Chỉ admin mới có thể cập nhật phòng ban
    if (req.user.rule !== 'admin') {
      return res.status(403).json({
        success: false,
        message: "Chỉ quản trị viên mới có thể cập nhật thông tin phòng ban.",
      });
    }

    const department = await Department.findById(id);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy phòng ban.",
      });
    }

    // Kiểm tra tên phòng ban trùng lặp (nếu thay đổi)
    if (name && name !== department.name) {
      const existingDept = await Department.findOne({ name, _id: { $ne: id } });
      if (existingDept) {
        return res.status(400).json({
          success: false,
          message: "Tên phòng ban đã tồn tại.",
        });
      }
    }

    // Cập nhật manager nếu có thay đổi
    if (managerId !== undefined) {
      // Xóa role manager cũ
      if (department.manager) {
        const oldManager = await User.findById(department.manager);
        if (oldManager) {
          oldManager.rule = 'department_member';
          oldManager.departmentRole = 'member';
          await oldManager.save();
        }
      }

      // Gán manager mới
      if (managerId) {
        const newManager = await User.findById(managerId);
        if (!newManager) {
          return res.status(400).json({
            success: false,
            message: "Không tìm thấy người quản lý được chỉ định.",
          });
        }
        newManager.department = department._id;
        newManager.rule = 'department_manager';
        newManager.departmentRole = 'manager';
        await newManager.save();
      }
    }

    // Chuẩn bị dữ liệu cập nhật
    const updateData = {};
    if (name) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (defaultPermissions) updateData.defaultPermissions = defaultPermissions;
    if (managerId !== undefined) updateData.manager = managerId || null;

    ForbiddenError.from(ability).throwUnlessCan("update", department);

    // Sử dụng findByIdAndUpdate để tránh validation issues
    const updatedDepartment = await Department.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: false } // Tắt validators để tránh lỗi createdBy
    );

    // Đồng bộ permissions cho tất cả thành viên nếu được yêu cầu
    let syncedMembersCount = 0;
    if (defaultPermissions && defaultPermissions.length > 0 && syncMemberPermissions === true) {
      console.log(`[INFO] Syncing permissions for all members in department ${id}`);

      // Cập nhật permissions cho tất cả thành viên trong phòng ban
      const updateResult = await User.updateMany(
        {
          department: id,
          rule: { $in: ['department_member', 'department_manager'] } // Chỉ cập nhật cho thành viên phòng ban
        },
        {
          $set: { permissions: defaultPermissions }
        }
      );

      syncedMembersCount = updateResult.modifiedCount;
      console.log(`[INFO] Updated permissions for ${syncedMembersCount} members`);
    }

    await updatedDepartment.populate('manager', 'username email');
    await updatedDepartment.populate('createdBy', 'username email');

    res.json({
      success: true,
      message: syncedMembersCount > 0
        ? `Cập nhật phòng ban thành công và đồng bộ quyền cho ${syncedMembersCount} thành viên.`
        : "Cập nhật phòng ban thành công.",
      department: updatedDepartment,
      syncedMembersCount,
    });
  } catch (error) {
    console.error("Error updating department:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi cập nhật phòng ban.",
      error: error.message,
    });
  }
};

// Xóa phòng ban
exports.deleteDepartment = async (req, res) => {
  try {
    const { id } = req.params;

    console.log('=== DELETE DEPARTMENT DEBUG ===');
    console.log('ID:', id, 'Length:', id ? id.length : 'undefined');
    console.log('User:', req.user ? { id: req.user._id, rule: req.user.rule, email: req.user.email } : 'undefined');
    console.log('============================');

    // Chỉ admin mới có thể xóa phòng ban
    if (req.user.rule !== 'admin') {
      console.log('Permission denied: user rule is', req.user.rule);
      return res.status(403).json({
        success: false,
        message: "Chỉ quản trị viên mới có thể xóa phòng ban.",
      });
    }

    // Validate ObjectId
    if (!id || !id.match(/^[0-9a-fA-F]{24}$/)) {
      console.log('Invalid ID format:', id);
      return res.status(400).json({
        success: false,
        message: `ID phòng ban không hợp lệ. Yêu cầu 24 ký tự hex, nhận được ${id ? id.length : 0} ký tự. ID: ${id}`,
      });
    }

    const department = await Department.findById(id);
    if (!department) {
      console.log('Department not found:', id);
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy phòng ban.",
      });
    }

    console.log('Department found:', { name: department.name });

    // Kiểm tra xem phòng ban có thành viên không
    const memberCount = await User.countDocuments({ department: id });
    console.log('Member count for department:', memberCount);

    // Xử lý thành viên trước khi xóa phòng ban
    if (memberCount > 0) {
      console.log('Department has members, removing department reference from users');

      // Xóa department reference từ tất cả thành viên
      await User.updateMany(
        { department: id },
        {
          $unset: {
            department: 1,
            departmentRole: 1
          },
          rule: 'user' // Đặt lại rule về user thường
        }
      );

      console.log(`Removed department reference from ${memberCount} users`);
    }

    // Soft delete using findByIdAndUpdate to avoid validation issues
    await Department.findByIdAndUpdate(
      id,
      { isActive: false },
      { runValidators: false }
    );

    const message = memberCount > 0
      ? `Xóa phòng ban thành công. ${memberCount} thành viên đã được chuyển về trạng thái không thuộc phòng ban nào.`
      : "Xóa phòng ban thành công.";

    res.json({
      success: true,
      message,
      affectedMembers: memberCount
    });
  } catch (error) {
    console.error("Error deleting department:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi xóa phòng ban.",
      error: error.message,
    });
  }
};

// Thêm thành viên vào phòng ban
exports.addMemberToDepartment = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    const { id: departmentId } = req.params;
    const { userId, username, email, password, phonenumber, permissions, departmentRole } = req.body;

    // Kiểm tra quyền
    let department = await Department.findById(departmentId);
    if (!department) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy phòng ban.",
      });
    }

    // Fix dept_ prefix in defaultPermissions if exists
    if (department.defaultPermissions && department.defaultPermissions.some(p => p.startsWith('dept_'))) {
      const fixedPermissions = department.defaultPermissions.map(perm => {
        if (perm.startsWith('dept_')) {
          return perm.replace('dept_', '');
        }
        return perm;
      });

      await Department.findByIdAndUpdate(
        departmentId,
        { defaultPermissions: fixedPermissions },
        { validateBeforeSave: false }
      );

      // Refetch department with updated permissions
      department = await Department.findById(departmentId);
    }

    // Kiểm tra quyền thêm thành viên
    const userDepartmentId = req.user.department ? 
      (req.user.department._id ? req.user.department._id.toString() : req.user.department.toString()) 
      : null;
    
    const canAddMember = req.user.rule === 'admin' ||
      (req.user.rule === 'department_manager' &&
       userDepartmentId &&
       userDepartmentId === departmentId);

    if (!canAddMember) {
      return res.status(403).json({
        success: false,
        message: "Bạn không có quyền thêm thành viên vào phòng ban này.",
      });
    }

    let user;
    let finalPassword; // Định nghĩa biến ở scope chung

    if (userId) {
      // Trường hợp thêm user hiện có vào phòng ban
      user = await User.findById(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: "Không tìm thấy người dùng.",
        });
      }

      // Kiểm tra user đã có phòng ban chưa
      if (user.department) {
        return res.status(400).json({
          success: false,
          message: "Người dùng đã thuộc phòng ban khác.",
        });
      }

      // Cập nhật user với thông tin phòng ban
      user.department = departmentId;
      user.departmentRole = departmentRole || 'member';
      user.rule = 'department_member';

      // Use provided permissions or fallback to department defaults
      let finalPermissions = permissions || department.defaultPermissions;

      // Fix dept_ prefix if exists (for backward compatibility)
      finalPermissions = finalPermissions.map(perm => {
        if (perm.startsWith('dept_')) {
          return perm.replace('dept_', '');
        }
        return perm;
      });

      user.permissions = finalPermissions;
      await user.save();

    } else {
      // Trường hợp tạo user mới

      // Kiểm tra email đã tồn tại
      const existingUser = await User.findOne({ email });
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: "Địa chỉ email này đã được sử dụng.",
        });
      }

      // Kiểm tra số điện thoại nếu có
      if (phonenumber) {
        const existingPhone = await User.findOne({ phonenumber });
        if (existingPhone) {
          return res.status(400).json({
            success: false,
            message: "Số điện thoại đã được sử dụng.",
          });
        }
      }

      // Tạo mật khẩu tự động nếu không có
      const generateId = customAlphabet("1234567890abcdef", 8);
      finalPassword = password || generateId();
      const hashedPassword = await bcrypt.hash(finalPassword, 10);

      // Use provided permissions or fallback to department defaults
      let finalPermissions = permissions || department.defaultPermissions;

      // Fix dept_ prefix if exists (for backward compatibility)
      finalPermissions = finalPermissions.map(perm => {
        if (perm.startsWith('dept_')) {
          return perm.replace('dept_', '');
        }
        return perm;
      });

      // Tạo user mới
      user = new User({
        username,
        email,
        password: hashedPassword,
        phonenumber,
        code: generateId(),
        department: departmentId,
        rule: 'department_member',
        departmentRole: departmentRole || 'member',
        permissions: finalPermissions,
      });

      // Debug: Log the user and ability details
      console.log('Current user:', {
        id: req.user._id,
        rule: req.user.rule,
        department: req.user.department,
        departmentType: typeof req.user.department
      });
      console.log('New user to create:', {
        department: user.department,
        departmentType: typeof user.department
      });

      ForbiddenError.from(ability).throwUnlessCan("create", user);
      await user.save();
    }

    // Cập nhật số lượng thành viên
    await department.updateMemberCount();

    res.status(201).json({
      success: true,
      message: "Thêm thành viên vào phòng ban thành công.",
      user: {
        _id: user._id,
        username: user.username,
        email: user.email,
        phonenumber: user.phonenumber,
        rule: user.rule,
        departmentRole: user.departmentRole,
        permissions: user.permissions,
      },
      generatedPassword: userId ? null : (password ? null : (typeof finalPassword !== 'undefined' ? finalPassword : null)), // Chỉ trả về mật khẩu nếu được tạo tự động cho user mới
    });
  } catch (error) {
    console.error("Error adding member to department:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi thêm thành viên.",
      error: error.message,
    });
  }
};

// Lấy danh sách thành viên phòng ban
exports.getDepartmentMembers = async (req, res) => {

  try {
    const { id: departmentId } = req.params;
    const { page = 1, perPage = 20, search = "" } = req.body;

    // Kiểm tra quyền xem thành viên
    // Admin có thể xem thành viên của tất cả phòng ban
    // Department manager và member chỉ có thể xem thành viên phòng ban của mình
    const userDepartmentId = req.user.department ? 
      (req.user.department._id ? req.user.department._id.toString() : req.user.department.toString()) 
      : null;
    
    const canViewMembers = req.user.rule === 'admin' ||
      (userDepartmentId && userDepartmentId === departmentId);

    if (!canViewMembers) {
      return res.status(403).json({
        success: false,
        message: "Bạn không có quyền xem danh sách thành viên phòng ban này.",
      });
    }

    let filter = { department: departmentId };

    if (search) {
      filter.$or = [
        { username: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { phonenumber: { $regex: search, $options: 'i' } }
      ];
    }

    const skip = (page - 1) * perPage;

    const [members, total] = await Promise.all([
      User.find(filter)
        .select('username email phonenumber rule departmentRole permissions createdAt')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(perPage),
      User.countDocuments(filter)
    ]);

    res.json({
      success: true,
      members,
      total,
      page,
      perPage,
    });
  } catch (error) {
    console.error("Error getting department members:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi lấy danh sách thành viên.",
      error: error.message,
    });
  }
};

// Cập nhật quyền thành viên phòng ban
exports.updateMemberPermissions = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    const { id: departmentId, memberId } = req.params;
    const { permissions, departmentRole } = req.body;

    // Kiểm tra quyền cập nhật
    const userDepartmentId = req.user.department ?
      (req.user.department._id ? req.user.department._id.toString() : req.user.department.toString())
      : null;

    const canUpdateMember = req.user.rule === 'admin' ||
      (req.user.rule === 'department_manager' &&
       userDepartmentId &&
       userDepartmentId === departmentId);

    if (!canUpdateMember) {
      return res.status(403).json({
        success: false,
        message: "Bạn không có quyền cập nhật thành viên trong phòng ban này.",
      });
    }

    const member = await User.findOne({ _id: memberId, department: departmentId });
    if (!member) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy thành viên trong phòng ban này.",
      });
    }

    // Cho phép department manager thay đổi quyền của chính mình
    // Chỉ cảnh báo nhưng không chặn
    if (req.user.rule === 'department_manager' && memberId === req.user._id.toString()) {
      console.log(`[WARNING] Department manager ${req.user.email} is updating their own permissions`);
    }

    // Cập nhật quyền
    if (permissions) member.permissions = permissions;
    
    // Cập nhật vai trò trong phòng ban
    if (departmentRole) {
      if (departmentRole === 'manager') {
        // Khi thăng cấp thành quản lý phòng ban
        
        // Kiểm tra xem đã có quản lý chưa
        const department = await Department.findById(departmentId);
        if (department && department.manager && department.manager.toString() !== memberId) {
          // Hạ cấp quản lý cũ nếu có
          const oldManager = await User.findById(department.manager);
          if (oldManager) {
            oldManager.rule = 'department_member';
            oldManager.departmentRole = 'member';
            await oldManager.save();
          }
        }
        
        // Thăng cấp thành viên mới
        member.rule = 'department_manager';
        member.departmentRole = 'manager';
        
        // Cập nhật department manager using updateOne to avoid validation issues
        if (department) {
          await Department.updateOne(
            { _id: departmentId },
            { manager: memberId }
          );
        }
      } else {
        // Khi hạ cấp từ quản lý về thành viên
        if (member.rule === 'department_manager') {
          member.rule = 'department_member';
          
          // Xóa manager khỏi department nếu đây là manager hiện tại
          const department = await Department.findById(departmentId);
          if (department && department.manager && department.manager.toString() === memberId) {
            await Department.updateOne(
              { _id: departmentId },
              { manager: null }
            );
          }
        }
        member.departmentRole = departmentRole;
      }
    }

    ForbiddenError.from(ability).throwUnlessCan("update", member);

    await member.save();

    // Sync permissions to all user sessions
    await syncUserPermissions(member._id.toString());

    res.json({
      success: true,
      message: "Cập nhật quyền thành viên thành công.",
      member: {
        _id: member._id,
        username: member.username,
        email: member.email,
        permissions: member.permissions,
        departmentRole: member.departmentRole,
      },
    });
  } catch (error) {
    console.error("Error updating member permissions:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi cập nhật quyền thành viên.",
      error: error.message,
    });
  }
};

// Xóa thành viên khỏi phòng ban
exports.removeMemberFromDepartment = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    const { id: departmentId, memberId } = req.params;

    // Kiểm tra quyền xóa
    const userDepartmentId = req.user.department ? 
      (req.user.department._id ? req.user.department._id.toString() : req.user.department.toString()) 
      : null;
    
    const canRemoveMember = req.user.rule === 'admin' ||
      (req.user.rule === 'department_manager' &&
       userDepartmentId &&
       userDepartmentId === departmentId);

    if (!canRemoveMember) {
      return res.status(403).json({
        success: false,
        message: "Bạn không có quyền xóa thành viên khỏi phòng ban này.",
      });
    }

    const member = await User.findOne({ _id: memberId, department: departmentId });
    if (!member) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy thành viên trong phòng ban này.",
      });
    }

    // Không cho phép department manager xóa chính mình
    if (req.user.rule === 'department_manager' && memberId === req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: "Bạn không thể xóa chính mình khỏi phòng ban.",
      });
    }

    // Xóa thành viên khỏi phòng ban
    member.department = null;
    member.rule = 'user';
    member.departmentRole = 'member';
    member.permissions = [];

    ForbiddenError.from(ability).throwUnlessCan("update", member);
    await member.save();

    // Cập nhật số lượng thành viên
    const department = await Department.findById(departmentId);
    if (department) {
      await department.updateMemberCount();
    }

    res.json({
      success: true,
      message: "Xóa thành viên khỏi phòng ban thành công.",
    });
  } catch (error) {
    console.error("Error removing member from department:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi xóa thành viên.",
      error: error.message,
    });
  }
};

// Cập nhật permission keys cho tất cả phòng ban (utility function)
exports.fixDepartmentPermissions = async (req, res) => {
  try {
    // Chỉ admin mới có thể chạy function này
    if (req.user.rule !== 'admin') {
      return res.status(403).json({
        success: false,
        message: "Chỉ quản trị viên mới có thể thực hiện thao tác này.",
      });
    }

    const departments = await Department.find({ isActive: true });
    let updatedCount = 0;

    for (const dept of departments) {
      if (dept.defaultPermissions && dept.defaultPermissions.length > 0) {
        // Map dept_ prefixed permissions to non-prefixed ones
        const fixedPermissions = dept.defaultPermissions.map(perm => {
          if (perm.startsWith('dept_')) {
            return perm.replace('dept_', '');
          }
          return perm;
        });

        dept.defaultPermissions = fixedPermissions;
        await dept.save();
        updatedCount++;
      }
    }

    res.json({
      success: true,
      message: `Đã cập nhật permission keys cho ${updatedCount} phòng ban.`,
      updatedCount
    });
  } catch (error) {
    console.error("Error fixing department permissions:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi cập nhật permission keys.",
      error: error.message,
    });
  }
};

// Cập nhật memberCount cho tất cả phòng ban (utility function)
exports.updateAllMemberCounts = async (req, res) => {
  try {
    // Chỉ admin mới có thể chạy function này
    if (req.user.rule !== 'admin') {
      return res.status(403).json({
        success: false,
        message: "Chỉ quản trị viên mới có thể thực hiện thao tác này.",
      });
    }

    const departments = await Department.find({ isActive: true });
    let updatedCount = 0;

    for (const dept of departments) {
      await dept.updateMemberCount();
      updatedCount++;
    }

    res.json({
      success: true,
      message: `Đã cập nhật memberCount cho ${updatedCount} phòng ban.`,
      updatedCount
    });
  } catch (error) {
    console.error("Error updating member counts:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi cập nhật memberCount.",
      error: error.message,
    });
  }
};

// Lấy danh sách quyền có sẵn
exports.getAvailablePermissions = async (_, res) => {
  try {
    // Sử dụng Master Permissions để đảm bảo đồng nhất
    const permissions = MASTER_PERMISSIONS;

    res.json({
      success: true,
      permissions,
    });
  } catch (error) {
    console.error("Error getting available permissions:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi lấy danh sách quyền.",
      error: error.message,
    });
  }
};

// Lấy danh sách preset quyền cho phòng ban
exports.getDepartmentPresets = async (_, res) => {
  try {
    const DEPARTMENT_PERMISSION_PRESETS = require('../config/departmentPresets');
    
    res.json({
      success: true,
      presets: DEPARTMENT_PERMISSION_PRESETS,
    });
  } catch (error) {
    console.error("Error getting department presets:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi lấy danh sách preset phòng ban.",
      error: error.message,
    });
  }
};