{"name": "blog-express", "version": "0.1.0", "private": true, "engines": {"node": ">=18.17.0", "npm": ">=8.0.0"}, "scripts": {"dev": "nodemon app.js", "build": "next build", "start": "node app.js", "lint": "next lint", "sync-files": "node server/scripts/sync-existing-files.js"}, "dependencies": {"@casl/ability": "^6.7.3", "@casl/mongoose": "^8.0.3", "@fingerprintjs/fingerprintjs": "^4.6.2", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@react-oauth/google": "^0.12.2", "@tanstack/react-table": "^8.21.3", "axios": "^1.11.0", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cookies-next": "^4.3.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "daisyui": "^4.12.10", "date-fns": "^2.30.0", "dotenv": "^16.5.0", "exceljs": "^4.4.0", "express": "^4.21.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^6.11.2", "helmet": "^8.1.0", "hpp": "^0.2.3", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lucide-react": "^0.516.0", "moment": "^2.29.1", "mongoose": "^8.18.0", "morgan": "^1.10.0", "multer": "^2.0.2", "nanoid": "^3.3.7", "next": "^15.5.0", "nextjs-toploader": "^1.6.12", "nodemailer": "^6.7.2", "papaparse": "^5.5.3", "passport": "^0.7.0", "passport-jwt": "^4.0.0", "pdfjs-dist": "^5.3.31", "prop-types": "^15.8.1", "qrcode": "^1.5.4", "react": "^19.1.1", "react-datepicker": "^8.7.0", "react-dom": "^19.1.1", "react-feather": "^2.0.10", "react-google-recaptcha": "^3.1.0", "react-google-recaptcha-v3": "^1.11.0", "react-hook-form": "^7.58.1", "react-pdf": "^9.2.1", "react-select": "^5.8.0", "react-toastify": "^11.0.5", "request-ip": "^3.3.0", "sass": "^1.89.2", "sharp": "^0.34.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "speakeasy": "^2.0.0", "tailwind-merge": "^2.6.0", "validator": "^13.15.15", "xss": "^1.0.15", "zod": "^3.25.67"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.14.202", "@types/node": "^24.3.0", "@types/react": "^19.1.11", "@types/react-datepicker": "^4.19.4", "@types/react-dom": "^19.1.7", "@types/react-google-recaptcha": "^2.1.9", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-config-next": "^15.5.0", "nodemon": "^3.1.10", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.9.2"}}