'use client';

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Users, Building, FileText, Shield, Settings, Eye } from 'lucide-react';
import adminApiRequest, { UserDashboardData, PermissionDescriptions } from '@/apiRequests/admin';

const RoleDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<UserDashboardData | null>(null);
  const [permissionDescriptions, setPermissionDescriptions] = useState<PermissionDescriptions>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sessionToken, setSessionToken] = useState<string>("");

  useEffect(() => {
    // Get session token on client side only
    const token = localStorage.getItem("sessionToken") || "";
    setSessionToken(token);
  }, []);

  useEffect(() => {
    if (!sessionToken) return; // Wait for session token

    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        const [dashboardResponse, descriptionsResponse] = await Promise.all([
          adminApiRequest.getUserDashboardStats(sessionToken),
          adminApiRequest.getPermissionDescriptions(sessionToken)
        ]);

        if (dashboardResponse.payload.success) {
          setDashboardData(dashboardResponse.payload.data);
        }

        if (descriptionsResponse.payload.success) {
          console.log('Permission descriptions loaded:', descriptionsResponse.payload.data);
          setPermissionDescriptions(descriptionsResponse.payload.data);
        }
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [sessionToken]);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'user_management':
        return <Users className="h-4 w-4" />;
      case 'court_case':
        return <FileText className="h-4 w-4" />;
      case 'file_management':
        return <FileText className="h-4 w-4" />;
      case 'department':
        return <Building className="h-4 w-4" />;
      case 'system':
        return <Settings className="h-4 w-4" />;
      case 'custom_fields':
        return <Eye className="h-4 w-4" />;
      default:
        return <Shield className="h-4 w-4" />;
    }
  };

  const getCategoryName = (category: string) => {
    switch (category) {
      case 'user_management':
        return 'Quản lý người dùng';
      case 'court_case':
        return 'Quản lý vụ án';
      case 'file_management':
        return 'Quản lý file';
      case 'department':
        return 'Quản lý phòng ban';
      case 'system':
        return 'Hệ thống';
      case 'custom_fields':
        return 'Trường tùy chỉnh';
      default:
        return category;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Đang tải thông tin dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-destructive mb-2">Lỗi khi tải dashboard</p>
          <p className="text-muted-foreground text-sm">{error}</p>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <p className="text-muted-foreground">Không có dữ liệu dashboard</p>
      </div>
    );
  }

  const { userInfo, permissions, departments } = dashboardData;

  return (
    <div className="space-y-6">
      {/* User Info Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Thông tin tài khoản
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Tên đăng nhập</label>
              <p className="text-sm font-medium">{userInfo.username}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Email</label>
              <p className="text-sm font-medium">{userInfo.email}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Vai trò</label>
              <p className="text-sm font-medium">{userInfo.rule}</p>
            </div>
            {userInfo.department && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Phòng ban</label>
                <p className="text-sm font-medium">{userInfo.department}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Permissions Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Quyền và chức năng của bạn
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {Object.entries(permissions.categories).map(([category, perms]) => {
            if (perms.length === 0) return null;

            return (
              <div key={category} className="space-y-2">
                <div className="flex items-center gap-2 font-medium">
                  {getCategoryIcon(category)}
                  {getCategoryName(category)}
                </div>
                <div className="grid gap-1 pl-6">
                  {perms.map((permission) => {
                    const displayName = permissionDescriptions[permission] || permission;
                    return (
                      <div key={permission} className="flex items-center">
                        <span className="text-sm">{displayName}</span>
                      </div>
                    );
                  })}
                </div>
                <hr className="my-2" />
              </div>
            );
          })}

          {permissions.available.length === 0 && (
            <p className="text-muted-foreground text-center py-4">
              Bạn chưa được cấp quyền nào
            </p>
          )}
        </CardContent>
      </Card>

      {/* Departments - Only show if user has department info */}
      {departments.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              Phòng ban
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {departments.map((dept) => (
                <div key={dept._id} className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">{dept.name}</h4>
                      {dept.description && (
                        <p className="text-sm text-muted-foreground">{dept.description}</p>
                      )}
                    </div>
                    <div className="text-right">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        dept.isActive 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {dept.isActive ? 'Hoạt động' : 'Không hoạt động'}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default RoleDashboard;
