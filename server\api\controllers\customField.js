const CustomField = require("../models/customField");
// const CourtCase = require("../../models/CourtCase"); // Temporarily disabled
const { ForbiddenError } = require("@casl/ability");
const { defineAbilityFor } = require("../permissions/abilities");

// Lấy danh sách custom fields
exports.getCustomFields = async (req, res) => {
  try {
    // Skip permission check if no user (for debugging)
    if (req.user) {
      const ability = defineAbilityFor(req.user);
      ForbiddenError.from(ability).throwUnlessCan("read", "CustomField");
    }

    const { targetModel = 'CourtCase' } = req.query;

    const fields = await CustomField.find({
      targetModel,
      isActive: true
    })
    .populate('createdBy', 'username email')
    .populate('updatedBy', 'username email')
    .sort({ 'config.sortOrder': 1, createdAt: 1 });

    res.json({
      success: true,
      fields
    });
  } catch (error) {
    console.error("Error getting custom fields:", error);
    console.error("Error stack:", error.stack);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi lấy danh sách trường tùy chỉnh.",
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

// Tạo custom field mới
exports.createCustomField = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    ForbiddenError.from(ability).throwUnlessCan("create", "CustomField");

    const {
      name,
      label,
      description,
      dataType,
      config,
      targetModel = 'CourtCase'
    } = req.body;

    // Validate name (chỉ cho phép chữ cái, số và underscore)
    const nameRegex = /^[a-zA-Z][a-zA-Z0-9_]*$/;
    if (!nameRegex.test(name)) {
      return res.status(400).json({
        success: false,
        message: "Tên trường chỉ được chứa chữ cái, số và dấu gạch dưới, bắt đầu bằng chữ cái."
      });
    }

    // Kiểm tra tên trường đã tồn tại
    const existingField = await CustomField.findOne({ name, targetModel });
    if (existingField) {
      return res.status(400).json({
        success: false,
        message: "Tên trường đã tồn tại."
      });
    }

    // Tạo field mới
    const customField = new CustomField({
      name,
      label,
      description,
      dataType,
      config: config || {},
      targetModel,
      createdBy: req.user.id
    });

    await customField.save();
    await customField.populate('createdBy', 'username email');

    res.status(201).json({
      success: true,
      message: "Tạo trường tùy chỉnh thành công.",
      field: customField
    });
  } catch (error) {
    console.error("Error creating custom field:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi tạo trường tùy chỉnh.",
      error: error.message,
    });
  }
};

// Cập nhật custom field
exports.updateCustomField = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    const { id } = req.params;
    const {
      label,
      description,
      config,
      isActive
    } = req.body;

    const customField = await CustomField.findById(id);
    if (!customField) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy trường tùy chỉnh."
      });
    }

    ForbiddenError.from(ability).throwUnlessCan("update", customField);

    // Cập nhật thông tin
    if (label !== undefined) customField.label = label;
    if (description !== undefined) customField.description = description;
    if (config !== undefined) customField.config = { ...customField.config, ...config };
    if (isActive !== undefined) customField.isActive = isActive;
    
    customField.updatedBy = req.user.id;

    await customField.save();
    await customField.populate('createdBy', 'username email');
    await customField.populate('updatedBy', 'username email');

    res.json({
      success: true,
      message: "Cập nhật trường tùy chỉnh thành công.",
      field: customField
    });
  } catch (error) {
    console.error("Error updating custom field:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi cập nhật trường tùy chỉnh.",
      error: error.message,
    });
  }
};

// Xóa custom field
exports.deleteCustomField = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    const { id } = req.params;

    const customField = await CustomField.findById(id);
    if (!customField) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy trường tùy chỉnh."
      });
    }

    ForbiddenError.from(ability).throwUnlessCan("delete", customField);

    // Soft delete - chỉ đánh dấu isActive = false
    customField.isActive = false;
    customField.updatedBy = req.user.id;
    await customField.save();

    res.json({
      success: true,
      message: "Xóa trường tùy chỉnh thành công."
    });
  } catch (error) {
    console.error("Error deleting custom field:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi xóa trường tùy chỉnh.",
      error: error.message,
    });
  }
};

// Cập nhật thứ tự hiển thị của các fields
exports.updateFieldsOrder = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    ForbiddenError.from(ability).throwUnlessCan("update", "CustomField");

    const { fieldOrders } = req.body; // Array of { id, sortOrder }

    if (!Array.isArray(fieldOrders)) {
      return res.status(400).json({
        success: false,
        message: "Dữ liệu thứ tự không hợp lệ."
      });
    }

    // Cập nhật thứ tự cho từng field
    const updatePromises = fieldOrders.map(({ id, sortOrder }) =>
      CustomField.findByIdAndUpdate(id, {
        'config.sortOrder': sortOrder,
        updatedBy: req.user.id
      })
    );

    await Promise.all(updatePromises);

    res.json({
      success: true,
      message: "Cập nhật thứ tự trường thành công."
    });
  } catch (error) {
    console.error("Error updating fields order:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi cập nhật thứ tự trường.",
      error: error.message,
    });
  }
};

// Lấy thống kê cho custom fields
exports.getCustomFieldStats = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    ForbiddenError.from(ability).throwUnlessCan("read", "CustomField");
    ForbiddenError.from(ability).throwUnlessCan("read", "CourtCase");

    const { targetModel = 'CourtCase' } = req.query;

    // Lấy danh sách custom fields có showInStats = true
    const fields = await CustomField.find({
      targetModel,
      isActive: true,
      'config.showInStats': true
    });

    const stats = {};

    for (const field of fields) {
      const fieldKey = `customFields.${field.name}`;
      
      if (field.dataType === 'select' || field.dataType === 'multiselect') {
        // Thống kê cho select fields
        const pipeline = [
          { $match: { [fieldKey]: { $exists: true, $ne: null } } },
          { $group: { _id: `$${fieldKey}`, count: { $sum: 1 } } },
          { $sort: { count: -1 } }
        ];

        if (field.dataType === 'multiselect') {
          pipeline.splice(1, 0, { $unwind: `$${fieldKey}` });
        }

        const result = await CourtCase.aggregate(pipeline);
        stats[field.name] = {
          label: field.label,
          type: 'distribution',
          data: result
        };
      } else if (field.dataType === 'number' || field.dataType === 'currency') {
        // Thống kê cho number fields
        const result = await CourtCase.aggregate([
          { $match: { [fieldKey]: { $exists: true, $ne: null } } },
          {
            $group: {
              _id: null,
              total: { $sum: `$${fieldKey}` },
              avg: { $avg: `$${fieldKey}` },
              min: { $min: `$${fieldKey}` },
              max: { $max: `$${fieldKey}` },
              count: { $sum: 1 }
            }
          }
        ]);

        stats[field.name] = {
          label: field.label,
          type: 'numeric',
          data: result[0] || { total: 0, avg: 0, min: 0, max: 0, count: 0 }
        };
      } else if (field.dataType === 'boolean') {
        // Thống kê cho boolean fields
        const result = await CourtCase.aggregate([
          { $match: { [fieldKey]: { $exists: true, $ne: null } } },
          { $group: { _id: `$${fieldKey}`, count: { $sum: 1 } } }
        ]);

        stats[field.name] = {
          label: field.label,
          type: 'boolean',
          data: result
        };
      }
    }

    res.json({
      success: true,
      stats
    });
  } catch (error) {
    console.error("Error getting custom field stats:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi hệ thống khi lấy thống kê trường tùy chỉnh.",
      error: error.message,
    });
  }
};
