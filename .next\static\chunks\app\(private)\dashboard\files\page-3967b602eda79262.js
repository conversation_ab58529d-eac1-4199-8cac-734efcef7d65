(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6156],{25656:(e,s,t)=>{"use strict";t.d(s,{Ay:()=>d});var l=t(79902),r=t(64269),a=t(20063);class i extends Error{constructor({status:e,payload:s}){super("Http Error"),this.status=e,this.payload=s}}class n extends i{constructor({status:e,payload:s}){super({status:e,payload:s}),this.status=e,this.payload=s}}let c=null,o=async(e,s,t)=>{let o;(null==t?void 0:t.body)instanceof FormData?o=t.body:(null==t?void 0:t.body)&&(o=JSON.stringify(t.body));let d=o instanceof FormData?{}:{"Content-Type":"application/json"};{let e=localStorage.getItem("sessionToken");e&&(d.Authorization="Bearer ".concat(e))}let x=(null==t?void 0:t.baseUrl)===void 0?l.A.NEXT_PUBLIC_API_ENDPOINT:t.baseUrl,u=s.startsWith("/")?"".concat(x).concat(s):"".concat(x,"/").concat(s),m=await fetch(u,{...t,headers:{...d,...null==t?void 0:t.headers},body:o,method:e}),h=null,g=m.headers.get("content-type");if(g&&g.includes("application/json"))try{h=await m.json()}catch(e){console.error("Failed to parse JSON response:",e),h=null}else h=await m.text();let p={status:m.status,payload:h};if(!m.ok)if(404===m.status||403===m.status)throw new n(p);else if(401===m.status){if(0){let e="";e=localStorage.getItem("sessionToken")||"",(0,a.redirect)("/logout?sessionToken=".concat(e))}else if(!c){c=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...d}});try{let e=async e=>{if(e.origin!=="".concat("https://flscott.com"))return};window.addEventListener("message",e),await c}catch(e){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),c=null,location.href="/login"}}}else throw new i(p);if(["api/auth/verify-app-code","api/auth/verify-code","api/auth/login","auth"].some(e=>e===(0,r.Fd)(s))){let{token:e}=h;localStorage.setItem("sessionToken",e)}else"auth/logout"===(0,r.Fd)(s)&&(localStorage.removeItem("user"),localStorage.removeItem("sessionToken"));return p},d={get:(e,s)=>o("GET",e,s),post:(e,s,t)=>o("POST",e,{...t,body:s}),put:(e,s,t)=>o("PUT",e,{...t,body:s}),patch:(e,s,t)=>o("PATCH",e,{...t,body:s}),delete:(e,s)=>o("DELETE",e,{...s})}},25679:(e,s,t)=>{Promise.resolve().then(t.bind(t,29066))},29066:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u});var l=t(95155),r=t(12115),a=t(74744),i=t(25656);let n={getFiles:(e,s)=>i.Ay.post("/api/administrator/files",e,{headers:{Authorization:"Bearer ".concat(s)}}),deleteFile:(e,s)=>i.Ay.delete("/api/administrator/files/".concat(e),{headers:{Authorization:"Bearer ".concat(s)}}),bulkAction:(e,s)=>i.Ay.post("/api/administrator/files/bulk-action",e,{headers:{Authorization:"Bearer ".concat(s)}}),getFileStats:e=>i.Ay.get("/api/administrator/files/stats",{headers:{Authorization:"Bearer ".concat(e)}}),getSyncStatus:e=>i.Ay.get("/api/administrator/files/sync-status",{headers:{Authorization:"Bearer ".concat(e)}}),syncExistingFiles:e=>i.Ay.post("/api/administrator/files/sync-existing",{},{headers:{Authorization:"Bearer ".concat(e)}})};var c=t(97849);let o=e=>{let{files:s,onFileSelect:t,onFileDelete:i,onBulkAction:n,loading:o=!1}=e,[d,x]=(0,r.useState)([]),[u,m]=(0,r.useState)(!1);(0,r.useEffect)(()=>{m(d.length===s.length&&s.length>0)},[d,s]);let h=e=>{if(0===d.length)return void a.oR.warning("Please select files first");confirm("delete"===e?"Are you sure you want to delete ".concat(d.length," files?"):"Are you sure you want to ".concat(e," ").concat(d.length," files?"))&&(n(d,e),x([]),m(!1))};return o?(0,l.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,l.jsx)("span",{className:"ml-2",children:"Loading files..."})]}):0===s.length?(0,l.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,l.jsx)("div",{className:"text-4xl mb-2",children:"\uD83D\uDCC1"}),(0,l.jsx)("p",{children:"No files found"})]}):(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[d.length>0&&(0,l.jsxs)("div",{className:"bg-blue-50 border-b px-4 py-3 flex items-center justify-between",children:[(0,l.jsxs)("span",{className:"text-sm text-blue-700",children:[d.length," files selected"]}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsx)("button",{onClick:()=>h("activate"),className:"px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700",children:"Activate"}),(0,l.jsx)("button",{onClick:()=>h("deactivate"),className:"px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700",children:"Deactivate"}),(0,l.jsx)("button",{onClick:()=>h("delete"),className:"px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700",children:"Delete"})]})]}),(0,l.jsx)("div",{className:"overflow-x-auto",children:(0,l.jsxs)("table",{className:"w-full",children:[(0,l.jsx)("thead",{className:"bg-gray-50 border-b",children:(0,l.jsxs)("tr",{children:[(0,l.jsx)("th",{className:"px-4 py-3 text-left",children:(0,l.jsx)("input",{type:"checkbox",checked:u,onChange:()=>{u?x([]):x(s.map(e=>e._id)),m(!u)},className:"rounded border-gray-300"})}),(0,l.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"File"}),(0,l.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,l.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Size"}),(0,l.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Uploaded By"}),(0,l.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,l.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,l.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,l.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(e=>(0,l.jsxs)("tr",{className:"hover:bg-gray-50 ".concat(d.includes(e._id)?"bg-blue-50":""),children:[(0,l.jsx)("td",{className:"px-4 py-3",children:(0,l.jsx)("input",{type:"checkbox",checked:d.includes(e._id),onChange:()=>(e=>{x(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])})(e._id),className:"rounded border-gray-300"})}),(0,l.jsx)("td",{className:"px-4 py-3",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("span",{className:"text-2xl mr-3",children:((e,s)=>{switch(e){case"image":return"\uD83D\uDDBC️";case"video":return"\uD83C\uDFAC";case"document":if(s.includes("pdf"))return"\uD83D\uDCC4";if(s.includes("word"))return"\uD83D\uDCDD";if(s.includes("excel"))return"\uD83D\uDCCA";return"\uD83D\uDCC4";default:return"\uD83D\uDCC1"}})(e.type,e.mimetype)}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-sm font-medium text-gray-900 truncate max-w-xs",children:e.originalName}),(0,l.jsx)("div",{className:"text-xs text-gray-500 truncate max-w-xs",children:e.filename})]})]})}),(0,l.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:(0,l.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800",children:e.type})}),(0,l.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:(0,c.z3)(e.size)}),(0,l.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.uploadedBy.username}),(0,l.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:(0,c.Yq)(e.uploadedAt)}),(0,l.jsx)("td",{className:"px-4 py-3",children:(0,l.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.isActive?"Active":"Inactive"})}),(0,l.jsx)("td",{className:"px-4 py-3 text-sm font-medium",children:(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsx)("button",{onClick:()=>t(e),className:"text-blue-600 hover:text-blue-900",children:"View"}),(0,l.jsx)("button",{onClick:()=>window.open(e.url,"_blank"),className:"text-green-600 hover:text-green-900",children:"Download"}),(0,l.jsx)("button",{onClick:()=>i(e._id),className:"text-red-600 hover:text-red-900",children:"Delete"})]})})]},e._id))})]})})]})},d=e=>{let{onSyncComplete:s}=e,[t,i]=(0,r.useState)(null),[o,d]=(0,r.useState)(!1),[x,u]=(0,r.useState)(!1),m=async()=>{try{let e=localStorage.getItem("sessionToken")||"",s=await n.getSyncStatus(e);s.payload.success&&i(s.payload.status)}catch(e){console.error("Error fetching sync status:",e)}},h=async()=>{if(!t||0===t.missingInDatabase)return void a.oR.info("Kh\xf4ng c\xf3 file n\xe0o cần đồng bộ");if(confirm("Bạn c\xf3 chắc muốn đồng bộ ".concat(t.missingInDatabase," files? Qu\xe1 tr\xecnh n\xe0y c\xf3 thể mất một \xedt thời gian.")))try{d(!0);let e=localStorage.getItem("sessionToken")||"";(await n.syncExistingFiles(e)).payload.success?(a.oR.success("Đ\xe3 bắt đầu đồng bộ file. Vui l\xf2ng kiểm tra lại sau v\xe0i ph\xfat."),setTimeout(()=>{m(),null==s||s()},3e3)):a.oR.error("Kh\xf4ng thể bắt đầu đồng bộ file")}catch(e){console.error("Error syncing files:",e),a.oR.error("Đ\xe3 xảy ra lỗi khi đồng bộ file")}finally{d(!1)}};if((0,r.useEffect)(()=>{m();let e=setInterval(m,3e4);return()=>clearInterval(e)},[]),!t)return(0,l.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,l.jsxs)("div",{className:"animate-pulse",children:[(0,l.jsx)("div",{className:"h-4 bg-gray-300 rounded w-1/4 mb-2"}),(0,l.jsx)("div",{className:"h-6 bg-gray-300 rounded w-1/2"})]})});let g=0===t.missingInDatabase;return(0,l.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"text-2xl mr-3",children:o?"\uD83D\uDD04":g?"✅":"⚠️"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"File Sync Status"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Trạng th\xe1i đồng bộ giữa thư mục vật l\xfd v\xe0 database"})]})]}),(0,l.jsx)("button",{onClick:()=>u(!x),className:"text-blue-600 hover:text-blue-800 text-sm",children:x?"Ẩn chi tiết":"Xem chi tiết"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",children:[(0,l.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:t.totalPhysicalFiles}),(0,l.jsx)("div",{className:"text-xs text-gray-600",children:"Files vật l\xfd"})]}),(0,l.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-green-600",children:t.totalDatabaseFiles}),(0,l.jsx)("div",{className:"text-xs text-gray-600",children:"Files trong DB"})]}),(0,l.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded",children:[(0,l.jsx)("div",{className:"text-2xl font-bold ".concat(t.missingInDatabase>0?"text-orange-600":"text-green-600"),children:t.missingInDatabase}),(0,l.jsx)("div",{className:"text-xs text-gray-600",children:"Chưa đồng bộ"})]}),(0,l.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded",children:[(0,l.jsx)("div",{className:"text-2xl font-bold ".concat(g?"text-green-600":"text-orange-600"),children:g?"100%":Math.round(t.totalDatabaseFiles/t.totalPhysicalFiles*100)+"%"}),(0,l.jsx)("div",{className:"text-xs text-gray-600",children:"Tỉ lệ sync"})]})]}),(0,l.jsx)("div",{className:"p-3 rounded-md mb-4 ".concat(g?"bg-green-50 text-green-800":"bg-orange-50 text-orange-800"),children:o?(0,l.jsxs)("span",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"}),"Đang đồng bộ files..."]}):g?"✅ Tất cả files đ\xe3 được đồng bộ":"⚠️ C\xf3 ".concat(t.missingInDatabase," files chưa được đồng bộ v\xe0o database")}),!g&&!o&&(0,l.jsxs)("button",{onClick:h,disabled:o,className:"w-full px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 disabled:opacity-50",children:["\uD83D\uDD04 Đồng bộ ",t.missingInDatabase," files"]}),x&&(0,l.jsxs)("div",{className:"mt-4 border-t pt-4",children:[(0,l.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Files chưa đồng bộ (10 đầu ti\xean):"}),t.missingFiles&&t.missingFiles.length>0?(0,l.jsxs)("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:[t.missingFiles.map((e,s)=>(0,l.jsxs)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded text-sm",children:[(0,l.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,l.jsx)("div",{className:"truncate font-medium text-gray-900",children:e.filename}),(0,l.jsx)("div",{className:"text-gray-500 text-xs",children:e.relativePath})]}),(0,l.jsx)("div",{className:"ml-4 text-gray-600 text-xs",children:(0,c.z3)(e.size)})]},s)),t.missingInDatabase>t.missingFiles.length&&(0,l.jsxs)("div",{className:"text-center text-gray-500 text-sm py-2",children:["... v\xe0 ",t.missingInDatabase-t.missingFiles.length," files kh\xe1c"]})]}):(0,l.jsx)("div",{className:"text-gray-500 text-sm",children:"Kh\xf4ng c\xf3 files n\xe0o cần đồng bộ"})]}),(0,l.jsxs)("div",{className:"mt-4 flex justify-between items-center text-sm text-gray-500",children:[(0,l.jsxs)("span",{children:["Cập nhật lần cuối: ",new Date().toLocaleTimeString()]}),(0,l.jsx)("button",{onClick:m,className:"text-blue-600 hover:text-blue-800",children:"\uD83D\uDD04 L\xe0m mới"})]})]})};var x=t(48203);let u=()=>{let[e,s]=(0,r.useState)([]),[t,i]=(0,r.useState)(!0),[u,m]=(0,r.useState)(null),[h,g]=(0,r.useState)({page:1,perPage:20,type:"all",sortBy:"uploadedAt",sortOrder:"desc"}),[p,y]=(0,r.useState)(0),[f,b]=(0,r.useState)(null),[j,v]=(0,r.useState)(!1),N=async()=>{try{i(!0);let e=localStorage.getItem("sessionToken")||"",t=await n.getFiles(h,e);t.payload.success?(s(t.payload.files),y(t.payload.total||0)):a.oR.error("Failed to fetch files")}catch(e){console.error("Error fetching files:",e),a.oR.error("An error occurred while fetching files")}finally{i(!1)}},w=async()=>{try{let e=localStorage.getItem("sessionToken")||"",s=await n.getFileStats(e);s.payload.success&&m(s.payload.stats)}catch(e){console.error("Error fetching stats:",e)}},S=async e=>{if(confirm("Are you sure you want to delete this file?"))try{let s=localStorage.getItem("sessionToken")||"";(await n.deleteFile(e,s)).payload.success?(a.oR.success("File deleted successfully"),N(),w()):a.oR.error("Failed to delete file")}catch(e){console.error("Error deleting file:",e),a.oR.error("An error occurred while deleting file")}},k=async(e,s)=>{try{let t=localStorage.getItem("sessionToken")||"";(await n.bulkAction({fileIds:e,action:s},t)).payload.success?(a.oR.success("".concat(s," completed successfully")),N(),w()):a.oR.error("Failed to ".concat(s," files"))}catch(e){console.error("Error in bulk ".concat(s,":"),e),a.oR.error("An error occurred while performing ".concat(s))}},C=(e,s)=>{g(t=>({...t,[e]:s,page:1}))},D=e=>{g(s=>({...s,page:e}))};(0,r.useEffect)(()=>{N()},[h]),(0,r.useEffect)(()=>{w()},[]);let A=Math.ceil(p/h.perPage);return(0,l.jsx)(x.default,{requiredPermissions:["file_view","file_upload","file_delete"],requireAll:!1,children:(0,l.jsxs)("div",{className:"p-6",children:[(0,l.jsxs)("div",{className:"mb-6",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"File Management"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Manage uploaded files and media"})]}),u&&(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,l.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"text-2xl mr-3",children:"\uD83D\uDCC1"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Total Files"}),(0,l.jsx)("p",{className:"text-xl font-semibold",children:(0,c.ZV)(u.totalFiles)})]})]})}),(0,l.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"text-2xl mr-3",children:"\uD83D\uDCBE"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Total Size"}),(0,l.jsx)("p",{className:"text-xl font-semibold",children:(0,c.z3)(u.totalSize)})]})]})}),(0,l.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"text-2xl mr-3",children:"\uD83D\uDCC8"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Recent Uploads"}),(0,l.jsx)("p",{className:"text-xl font-semibold",children:(0,c.ZV)(u.recentUploads)})]})]})}),(0,l.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"text-2xl mr-3",children:"\uD83C\uDFAF"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"File Types"}),(0,l.jsx)("p",{className:"text-xl font-semibold",children:u.filesByType.length})]})]})})]}),(0,l.jsx)("div",{className:"mb-6",children:(0,l.jsx)(d,{onSyncComplete:()=>{N(),w()}})}),(0,l.jsx)("div",{className:"bg-white p-4 rounded-lg shadow mb-6",children:(0,l.jsxs)("div",{className:"flex flex-wrap gap-4 items-center",children:[(0,l.jsx)("div",{className:"flex-1 min-w-64",children:(0,l.jsx)("input",{type:"text",placeholder:"Search files...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",onChange:e=>{var s;return s=e.target.value,void g(e=>({...e,query:s,page:1}))}})}),(0,l.jsxs)("select",{value:h.type,onChange:e=>C("type",e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,l.jsx)("option",{value:"all",children:"All Types"}),(0,l.jsx)("option",{value:"image",children:"Images"}),(0,l.jsx)("option",{value:"video",children:"Videos"}),(0,l.jsx)("option",{value:"document",children:"Documents"}),(0,l.jsx)("option",{value:"other",children:"Other"})]}),(0,l.jsxs)("select",{value:"".concat(h.sortBy,"-").concat(h.sortOrder),onChange:e=>{let[s,t]=e.target.value.split("-");C("sortBy",s),C("sortOrder",t)},className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,l.jsx)("option",{value:"uploadedAt-desc",children:"Newest First"}),(0,l.jsx)("option",{value:"uploadedAt-asc",children:"Oldest First"}),(0,l.jsx)("option",{value:"filename-asc",children:"Name A-Z"}),(0,l.jsx)("option",{value:"filename-desc",children:"Name Z-A"}),(0,l.jsx)("option",{value:"size-desc",children:"Largest First"}),(0,l.jsx)("option",{value:"size-asc",children:"Smallest First"})]}),(0,l.jsx)("button",{onClick:()=>v(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500",children:"\uD83D\uDCE4 Upload File"})]})}),(0,l.jsx)(o,{files:e,onFileSelect:b,onFileDelete:S,onBulkAction:k,loading:t}),A>1&&(0,l.jsx)("div",{className:"mt-6 flex justify-center",children:(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsx)("button",{onClick:()=>D(h.page-1),disabled:h.page<=1,className:"px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50",children:"Previous"}),Array.from({length:Math.min(5,A)},(e,s)=>{let t=s+1;return(0,l.jsx)("button",{onClick:()=>D(t),className:"px-3 py-2 border rounded-md ".concat(h.page===t?"bg-blue-600 text-white border-blue-600":"border-gray-300 hover:bg-gray-50"),children:t},t)}),(0,l.jsx)("button",{onClick:()=>D(h.page+1),disabled:h.page>=A,className:"px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50",children:"Next"})]})}),f&&(0,l.jsx)("div",{className:"fixed inset-0 flex items-center justify-center z-50",style:{backgroundColor:"rgba(0, 0, 0, 0.5)"},children:(0,l.jsxs)("div",{className:"p-6 rounded-lg max-w-2xl w-full mx-4 max-h-96 overflow-y-auto",style:{backgroundColor:"#ffffff",color:"#111827"},children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold",style:{color:"#111827"},children:"File Details"}),(0,l.jsx)("button",{onClick:()=>b(null),className:"text-xl font-bold p-1 rounded hover:bg-gray-100",style:{color:"#6b7280"},children:"✕"})]}),(0,l.jsxs)("div",{className:"space-y-3",style:{color:"#111827"},children:[(0,l.jsxs)("div",{style:{color:"#111827"},children:[(0,l.jsx)("strong",{style:{color:"#111827"},children:"Name:"}),(0,l.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:f.originalName})]}),(0,l.jsxs)("div",{style:{color:"#111827"},children:[(0,l.jsx)("strong",{style:{color:"#111827"},children:"Type:"}),(0,l.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:f.type})]}),(0,l.jsxs)("div",{style:{color:"#111827"},children:[(0,l.jsx)("strong",{style:{color:"#111827"},children:"Size:"}),(0,l.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:(0,c.z3)(f.size)})]}),(0,l.jsxs)("div",{style:{color:"#111827"},children:[(0,l.jsx)("strong",{style:{color:"#111827"},children:"Uploaded by:"}),(0,l.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:f.uploadedBy.username})]}),(0,l.jsxs)("div",{style:{color:"#111827"},children:[(0,l.jsx)("strong",{style:{color:"#111827"},children:"Upload date:"}),(0,l.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:new Date(f.uploadedAt).toLocaleString()})]}),(0,l.jsxs)("div",{style:{color:"#111827"},children:[(0,l.jsx)("strong",{style:{color:"#111827"},children:"Status:"}),(0,l.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:f.isActive?"Active":"Inactive"})]}),f.description&&(0,l.jsxs)("div",{style:{color:"#111827"},children:[(0,l.jsx)("strong",{style:{color:"#111827"},children:"Description:"}),(0,l.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:f.description})]}),f.tags&&f.tags.length>0&&(0,l.jsxs)("div",{style:{color:"#111827"},children:[(0,l.jsx)("strong",{style:{color:"#111827"},children:"Tags:"}),(0,l.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:f.tags.join(", ")})]})]}),(0,l.jsxs)("div",{className:"mt-6 flex gap-2",children:[(0,l.jsx)("button",{onClick:()=>window.open(f.url,"_blank"),className:"px-4 py-2 rounded hover:opacity-90 transition-opacity",style:{backgroundColor:"#2563eb",color:"#ffffff"},children:"View/Download"}),(0,l.jsx)("button",{onClick:()=>{S(f._id),b(null)},className:"px-4 py-2 rounded hover:opacity-90 transition-opacity",style:{backgroundColor:"#dc2626",color:"#ffffff"},children:"Delete"})]})]})})]})})}},48203:(e,s,t)=>{"use strict";t.d(s,{default:()=>n});var l=t(95155),r=t(75444),a=t(20063),i=t(12115);function n(e){let{children:s,requiredPermission:t,requiredPermissions:n=[],requireAll:c=!1,fallbackPath:o="/dashboard"}=e,{hasPermission:d,hasAnyPermission:x,isAdmin:u,isDepartmentManager:m,isLoading:h}=(0,r.S)(),g=(0,a.useRouter)();if((0,i.useEffect)(()=>{if(!h&&!u)(t?"admin"===t&&!!m||d(t):!(n.length>0)||(c?n.every(e=>d(e)):x(n)))||g.replace(o)},[d,x,u,m,h,t,n,c,o,g]),h)return(0,l.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,l.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(u)return(0,l.jsx)(l.Fragment,{children:s});return(t?"admin"===t&&!!m||d(t):!(n.length>0)||(c?n.every(e=>d(e)):x(n)))?(0,l.jsx)(l.Fragment,{children:s}):(0,l.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Kh\xf4ng c\xf3 quyền truy cập"}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o trang n\xe0y."}),(0,l.jsx)("button",{onClick:()=>g.back(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Quay lại"})]})})}},64269:(e,s,t)=>{"use strict";t.d(s,{Fd:()=>i,cn:()=>a}),t(25656);var l=t(2821),r=t(75889);function a(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.QP)((0,l.$)(s))}t(138);let i=e=>e.startsWith("/")?e.slice(1):e},75444:(e,s,t)=>{"use strict";t.d(s,{S:()=>r});var l=t(97367);let r=()=>{let{user:e,isLoading:s}=(0,l.U)();return{hasPermission:t=>{var l;return!s&&!!e&&("admin"===e.rule||(null==(l=e.permissions)?void 0:l.includes(t))||!1)},hasAnyPermission:t=>!s&&!!e&&("admin"===e.rule||t.some(s=>{var t;return null==(t=e.permissions)?void 0:t.includes(s)})),getAllPermissions:()=>s||!e?[]:"admin"===e.rule?["user_view","user_add","user_edit","user_delete","user_import_csv","file_view","file_upload","file_delete","system_settings_view","system_settings_edit","analytics_view","permissions_manage"]:e.permissions||[],userPermissions:(null==e?void 0:e.permissions)||[],isAdmin:!s&&(null==e?void 0:e.rule)==="admin",isDepartmentManager:!s&&(null==e?void 0:e.rule)==="department_manager",isLoading:s}}},79902:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var l=t(77376),r=t(95704);let a=l.Ik({NEXT_PUBLIC_API_ENDPOINT:l.Yj().url(),NEXT_PUBLIC_URL:l.Yj().url(),CRYPTOJS_SECRECT:l.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"https://flscott.com",NEXT_PUBLIC_URL:"https://flscott.com",CRYPTOJS_SECRECT:r.env.CRYPTOJS_SECRECT});if(!a.success)throw console.error("Invalid environment variables:",a.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let i=a.data},97367:(e,s,t)=>{"use strict";t.d(s,{U:()=>i,default:()=>n});var l=t(95155),r=t(12115);let a=(0,r.createContext)({user:null,setUser:()=>{},isAuthenticated:!1,isLoading:!0}),i=()=>(0,r.useContext)(a),n=e=>{let{children:s}=e,[t,i]=(0,r.useState)(()=>null),[n,c]=(0,r.useState)(!0),o=(0,r.useCallback)(e=>{i(e),localStorage.setItem("user",JSON.stringify(e))},[i]);return(0,r.useEffect)(()=>{let e=localStorage.getItem("user");i(e?JSON.parse(e):null),c(!1)},[i]),(0,l.jsx)(a.Provider,{value:{user:t,setUser:o,isAuthenticated:!!t,isLoading:n},children:s})}},97849:(e,s,t)=>{"use strict";t.d(s,{Yq:()=>r,ZV:()=>a,z3:()=>l});let l=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(s<0?0:s))+" "+["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"][t]},r=e=>{if(!e)return"";let s=new Date(e);return isNaN(s.getTime())?"":s.toLocaleDateString("vi-VN",{year:"numeric",month:"2-digit",day:"2-digit"})},a=e=>e.toLocaleString()}},e=>{e.O(0,[9268,2739,4744,8441,1255,7358],()=>e(e.s=25679)),_N_E=e.O()}]);