(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3948],{15870:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});let s=(0,n(71847).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},30486:(e,t,n)=>{Promise.resolve().then(n.bind(n,95354))},95354:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>K});var s=n(95155),a=n(12115),l=n(71847);let r=(0,l.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),i=(0,l.A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);var c=n(20063),o=n(74744),d=n(15870);let h=(0,l.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var g=n(57828);let x=(0,l.A)("grip-vertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]]),u=(0,l.A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),m=(0,l.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),b=(0,l.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);var p=n(57052),y=n(84961),f=n(65229);let j=[{value:"text",label:"Văn bản",icon:"\uD83D\uDCDD"},{value:"number",label:"Số",icon:"\uD83D\uDD22"},{value:"date",label:"Ng\xe0y",icon:"\uD83D\uDCC5"},{value:"datetime",label:"Ng\xe0y giờ",icon:"\uD83D\uDD50"},{value:"boolean",label:"Đ\xfang/Sai",icon:"☑️"},{value:"select",label:"Lựa chọn đơn",icon:"\uD83D\uDCCB"},{value:"multiselect",label:"Lựa chọn nhiều",icon:"\uD83D\uDCCB"},{value:"currency",label:"Tiền tệ",icon:"\uD83D\uDCB0"},{value:"percentage",label:"Phần trăm",icon:"\uD83D\uDCCA"},{value:"email",label:"Email",icon:"\uD83D\uDCE7"},{value:"phone",label:"Số điện thoại",icon:"\uD83D\uDCDE"},{value:"url",label:"Đường dẫn",icon:"\uD83D\uDD17"},{value:"textarea",label:"Văn bản d\xe0i",icon:"\uD83D\uDCC4"}];function v(e){let{isOpen:t,onClose:n,onSubmit:l,targetModel:r}=e,[i,c]=(0,a.useState)({name:"",label:"",description:"",dataType:"text",config:{required:!1,showInList:!0,showInStats:!0,columnWidth:150,options:[]}}),[o,d]=(0,a.useState)({value:"",label:"",color:"#3B82F6"}),g="select"===i.dataType||"multiselect"===i.dataType;return t?(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Tạo trường t\xf9y chỉnh mới"}),(0,s.jsx)("button",{onClick:n,className:"p-2 text-gray-400 hover:text-gray-600 rounded-lg",children:(0,s.jsx)(f.A,{size:20})})]}),(0,s.jsxs)("form",{onSubmit:e=>(e.preventDefault(),i.name.trim()&&i.label.trim())?/^[a-zA-Z][a-zA-Z0-9_]*$/.test(i.name)?void l(i):void alert("T\xean trường chỉ được chứa chữ c\xe1i, số v\xe0 dấu gạch dưới, bắt đầu bằng chữ c\xe1i"):void alert("Vui l\xf2ng nhập t\xean v\xe0 nh\xe3n hiển thị"),className:"p-6 space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["T\xean trường ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{type:"text",value:i.name,onChange:e=>c(t=>({...t,name:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"vd: priority_level",required:!0}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Chỉ chữ c\xe1i, số v\xe0 dấu gạch dưới. Bắt đầu bằng chữ c\xe1i."})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Nh\xe3n hiển thị ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{type:"text",value:i.label,onChange:e=>c(t=>({...t,label:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"vd: Mức độ ưu ti\xean",required:!0})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"M\xf4 tả"}),(0,s.jsx)("textarea",{value:i.description,onChange:e=>c(t=>({...t,description:e.target.value})),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"M\xf4 tả về trường n\xe0y..."})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Kiểu dữ liệu ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:j.map(e=>(0,s.jsx)("button",{type:"button",onClick:()=>c(t=>({...t,dataType:e.value})),className:"p-3 border rounded-lg text-left transition-colors ".concat(i.dataType===e.value?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-300 hover:border-gray-400"),children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-lg",children:e.icon}),(0,s.jsx)("span",{className:"text-sm font-medium",children:e.label})]})},e.value))})]}),g&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["T\xf9y chọn ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsxs)("div",{className:"flex gap-2 mb-3",children:[(0,s.jsx)("input",{type:"text",value:o.value,onChange:e=>d(t=>({...t,value:e.target.value})),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Gi\xe1 trị (vd: high)"}),(0,s.jsx)("input",{type:"text",value:o.label,onChange:e=>d(t=>({...t,label:e.target.value})),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nh\xe3n hiển thị (vd: Cao)"}),(0,s.jsx)("input",{type:"color",value:o.color,onChange:e=>d(t=>({...t,color:e.target.value})),className:"w-12 h-10 border border-gray-300 rounded-lg"}),(0,s.jsx)("button",{type:"button",onClick:()=>o.value.trim()&&o.label.trim()?i.config.options.some(e=>e.value===o.value)?void alert("Gi\xe1 trị n\xe0y đ\xe3 tồn tại"):void(c(e=>({...e,config:{...e.config,options:[...e.config.options,{...o}]}})),d({value:"",label:"",color:"#3B82F6"})):void alert("Vui l\xf2ng nhập gi\xe1 trị v\xe0 nh\xe3n hiển thị"),className:"px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:(0,s.jsx)(h,{size:16})})]}),(0,s.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto",children:i.config.options.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-gray-50 rounded-lg",children:[(0,s.jsx)("div",{className:"w-4 h-4 rounded",style:{backgroundColor:e.color}}),(0,s.jsx)("span",{className:"text-sm font-medium",children:e.label}),(0,s.jsxs)("span",{className:"text-sm text-gray-500",children:["(",e.value,")"]}),(0,s.jsx)("button",{type:"button",onClick:()=>{c(e=>({...e,config:{...e.config,options:e.config.options.filter((e,n)=>n!==t)}}))},className:"ml-auto p-1 text-red-600 hover:bg-red-50 rounded",children:(0,s.jsx)(b,{size:14})})]},t))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"C\xe0i đặt"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("label",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:i.config.required,onChange:e=>c(t=>({...t,config:{...t.config,required:e.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsx)("span",{className:"text-sm",children:"Bắt buộc nhập"})]}),(0,s.jsxs)("label",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:i.config.showInList,onChange:e=>c(t=>({...t,config:{...t.config,showInList:e.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsx)("span",{className:"text-sm",children:"Hiển thị trong danh s\xe1ch"})]}),(0,s.jsxs)("label",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:i.config.showInStats,onChange:e=>c(t=>({...t,config:{...t.config,showInStats:e.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsx)("span",{className:"text-sm",children:"Hiển thị trong thống k\xea"})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-3 pt-4 border-t border-gray-200",children:[(0,s.jsx)("button",{type:"button",onClick:n,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,s.jsx)("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Tạo trường"})]})]})]})}):null}function N(e){var t;let{isOpen:n,onClose:l,onSubmit:r,field:i}=e,[c,o]=(0,a.useState)({label:"",description:"",config:{required:!1,showInList:!0,showInStats:!0,columnWidth:150,options:[]}}),[d,g]=(0,a.useState)({value:"",label:"",color:"#3B82F6"});(0,a.useEffect)(()=>{i&&o({label:i.label,description:i.description||"",config:{required:i.config.required||!1,showInList:!1!==i.config.showInList,showInStats:!1!==i.config.showInStats,columnWidth:i.config.columnWidth||150,options:i.config.options||[]}})},[i]);let x=(null==i?void 0:i.dataType)==="select"||(null==i?void 0:i.dataType)==="multiselect";return n&&i?(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Chỉnh sửa trường"}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,s.jsx)("span",{className:"text-lg",children:{text:"\uD83D\uDCDD",number:"\uD83D\uDD22",date:"\uD83D\uDCC5",datetime:"\uD83D\uDD50",boolean:"☑️",select:"\uD83D\uDCCB",multiselect:"\uD83D\uDCCB",currency:"\uD83D\uDCB0",percentage:"\uD83D\uDCCA",email:"\uD83D\uDCE7",phone:"\uD83D\uDCDE",url:"\uD83D\uDD17",textarea:"\uD83D\uDCC4",file:"\uD83D\uDCCE",json:"\uD83D\uDD27"}[i.dataType]||"❓"}),(0,s.jsxs)("span",{className:"text-sm text-gray-600",children:[i.name," (",{text:"Văn bản",number:"Số",date:"Ng\xe0y",datetime:"Ng\xe0y giờ",boolean:"Đ\xfang/Sai",select:"Lựa chọn đơn",multiselect:"Lựa chọn nhiều",currency:"Tiền tệ",percentage:"Phần trăm",email:"Email",phone:"Số điện thoại",url:"Đường dẫn",textarea:"Văn bản d\xe0i",file:"File đ\xednh k\xe8m",json:"Dữ liệu JSON"}[t=i.dataType]||t,")"]}),(0,s.jsx)("span",{className:"text-xs px-2 py-1 rounded ".concat(i.isBuiltIn?"bg-blue-100 text-blue-600":"bg-green-100 text-green-600"),children:i.isBuiltIn?"Mặc định":"Đ\xe3 th\xeam"})]})]}),(0,s.jsx)("button",{onClick:l,className:"p-2 text-gray-400 hover:text-gray-600 rounded-lg",children:(0,s.jsx)(f.A,{size:20})})]}),(0,s.jsxs)("form",{onSubmit:e=>{if(e.preventDefault(),!c.label.trim())return void alert("Vui l\xf2ng nhập nh\xe3n hiển thị");r(c)},className:"p-6 space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Nh\xe3n hiển thị ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{type:"text",value:c.label,onChange:e=>o(t=>({...t,label:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"vd: Mức độ ưu ti\xean",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"M\xf4 tả"}),(0,s.jsx)("textarea",{value:c.description,onChange:e=>o(t=>({...t,description:e.target.value})),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"M\xf4 tả về trường n\xe0y..."})]}),x&&!i.isBuiltIn&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"T\xf9y chọn"}),(0,s.jsxs)("div",{className:"flex gap-2 mb-3",children:[(0,s.jsx)("input",{type:"text",value:d.value,onChange:e=>g(t=>({...t,value:e.target.value})),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Gi\xe1 trị (vd: high)"}),(0,s.jsx)("input",{type:"text",value:d.label,onChange:e=>g(t=>({...t,label:e.target.value})),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nh\xe3n hiển thị (vd: Cao)"}),(0,s.jsx)("input",{type:"color",value:d.color,onChange:e=>g(t=>({...t,color:e.target.value})),className:"w-12 h-10 border border-gray-300 rounded-lg"}),(0,s.jsx)("button",{type:"button",onClick:()=>d.value.trim()&&d.label.trim()?c.config.options.some(e=>e.value===d.value)?void alert("Gi\xe1 trị n\xe0y đ\xe3 tồn tại"):void(o(e=>({...e,config:{...e.config,options:[...e.config.options,{...d}]}})),g({value:"",label:"",color:"#3B82F6"})):void alert("Vui l\xf2ng nhập gi\xe1 trị v\xe0 nh\xe3n hiển thị"),className:"px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:(0,s.jsx)(h,{size:16})})]}),(0,s.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto",children:c.config.options.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-gray-50 rounded-lg",children:[(0,s.jsx)("div",{className:"w-4 h-4 rounded",style:{backgroundColor:e.color}}),(0,s.jsx)("span",{className:"text-sm font-medium",children:e.label}),(0,s.jsxs)("span",{className:"text-sm text-gray-500",children:["(",e.value,")"]}),(0,s.jsx)("button",{type:"button",onClick:()=>{o(e=>({...e,config:{...e.config,options:e.config.options.filter((e,n)=>n!==t)}}))},className:"ml-auto p-1 text-red-600 hover:bg-red-50 rounded",children:(0,s.jsx)(b,{size:14})})]},t))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"C\xe0i đặt"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("label",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:c.config.required,onChange:e=>o(t=>({...t,config:{...t.config,required:e.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsx)("span",{className:"text-sm",children:"Bắt buộc nhập"})]}),(0,s.jsxs)("label",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:c.config.showInList,onChange:e=>o(t=>({...t,config:{...t.config,showInList:e.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsx)("span",{className:"text-sm",children:"Hiển thị trong danh s\xe1ch"})]}),(0,s.jsxs)("label",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:c.config.showInStats,onChange:e=>o(t=>({...t,config:{...t.config,showInStats:e.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsx)("span",{className:"text-sm",children:"Hiển thị trong thống k\xea"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Độ rộng cột (pixels)"}),(0,s.jsx)("input",{type:"number",value:c.config.columnWidth,onChange:e=>o(t=>({...t,config:{...t.config,columnWidth:parseInt(e.target.value)||150}})),className:"w-32 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",min:"50",max:"500"})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-3 pt-4 border-t border-gray-200",children:[(0,s.jsx)("button",{type:"button",onClick:l,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,s.jsx)("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Cập nhật"})]})]})]})}):null}var w=n(75444);let k=[{name:"stt",label:"STT",dataType:"number",required:!0,canEdit:!0,canDelete:!0},{name:"soVanThu",label:"Số văn thư",dataType:"text",required:!1,canEdit:!0,canDelete:!0},{name:"ngayNhanVanThu",label:"Ng\xe0y nhận văn thư",dataType:"date",required:!1,canEdit:!0,canDelete:!0},{name:"loaiAn",label:"Loại \xe1n",dataType:"text",required:!1,canEdit:!0,canDelete:!0},{name:"soThuLy",label:"Số thụ l\xfd",dataType:"text",required:!1,canEdit:!0,canDelete:!0},{name:"ngayThuLy",label:"Ng\xe0y thụ l\xfd",dataType:"date",required:!1,canEdit:!0,canDelete:!0},{name:"tand",label:"TAND",dataType:"text",required:!1,canEdit:!0,canDelete:!0},{name:"soBanAn",label:"Số bản \xe1n",dataType:"text",required:!1,canEdit:!0,canDelete:!0},{name:"ngayBanHanh",label:"Ng\xe0y ban h\xe0nh",dataType:"date",required:!1,canEdit:!0,canDelete:!0},{name:"biCaoNguoiKhieuKien",label:"Bị c\xe1o/Người khiếu kiện",dataType:"text",required:!1,canEdit:!0,canDelete:!0},{name:"toiDanhNoiDung",label:"Tội danh/Nội dung",dataType:"textarea",required:!1,canEdit:!0,canDelete:!0},{name:"quanHePhatLuat",label:"Quan hệ ph\xe1p luật",dataType:"text",required:!1,canEdit:!0,canDelete:!0},{name:"hinhThucXuLy",label:"H\xecnh thức xử l\xfd",dataType:"text",required:!1,canEdit:!0,canDelete:!0},{name:"thuTucApDung",label:"Thủ tục \xe1p dụng",dataType:"text",required:!1,canEdit:!0,canDelete:!0},{name:"thamPhanPhuTrach",label:"Thẩm ph\xe1n phụ tr\xe1ch",dataType:"text",required:!1,canEdit:!0,canDelete:!0},{name:"truongPhoPhongKTNV",label:"Trưởng/Ph\xf3 ph\xf2ng KTNV",dataType:"text",required:!1,canEdit:!0,canDelete:!0},{name:"ghiChu",label:"Ghi ch\xfa",dataType:"textarea",required:!1,canEdit:!0,canDelete:!0},{name:"ghiChuKetQua",label:"Ghi ch\xfa kết quả",dataType:"textarea",required:!1,canEdit:!0,canDelete:!0},{name:"trangThaiGiaiQuyet",label:"Trạng th\xe1i giải quyết",dataType:"select",required:!0,canEdit:!0,canDelete:!0,options:[{value:"Chưa giải quyết",label:"Chưa giải quyết",color:"#EF4444"},{value:"Đang giải quyết",label:"Đang giải quyết",color:"#F59E0B"},{value:"Đ\xe3 giải quyết",label:"Đ\xe3 giải quyết",color:"#10B981"}]}];function C(e){let{targetModel:t="CourtCase",onFieldsChange:n}=e,[l,r]=(0,a.useState)([]),[i,c]=(0,a.useState)([]),[f,j]=(0,a.useState)(null),[C,T]=(0,a.useState)(!0),[D,S]=(0,a.useState)(!1),[I,A]=(0,a.useState)(null),[L,E]=(0,a.useState)(null),[z,q]=(0,a.useState)(""),[M,H]=(0,a.useState)("all"),{hasPermission:_}=(0,w.S)();(0,a.useEffect)(()=>{B()},[t]);let B=async()=>{try{T(!0);let e=localStorage.getItem("sessionToken")||"",[s,a]=await Promise.all([p.A.getCustomFields(t,e),y.A.getFieldConfiguration(t,e)]);if(s.payload.success&&a.payload.success){r(s.payload.fields),j(a.payload.configuration);let e=a.payload.configuration.fieldConfigs,t=[];k.forEach(n=>{var s,a,l,r;let i=e.find(e=>e.fieldName===n.name);t.push({...n,_id:"default_".concat(n.name),isDefault:!0,config:{showInList:null==(s=null==i?void 0:i.showInList)||s,showInStats:null!=(a=null==i?void 0:i.showInStats)?a:"textarea"!==n.dataType,columnWidth:null!=(l=null==i?void 0:i.columnWidth)?l:150,sortOrder:null!=(r=null==i?void 0:i.sortOrder)?r:0,required:n.required,options:n.options||[]},createdBy:{username:"System",email:"system"}})}),s.payload.fields.forEach(n=>{var s,a,l,r;let i=e.find(e=>e.fieldName===n.name);t.push({...n,isDefault:!1,canEdit:!0,canDelete:!0,config:{...n.config,showInList:null!=(s=null==i?void 0:i.showInList)?s:n.config.showInList,showInStats:null!=(a=null==i?void 0:i.showInStats)?a:n.config.showInStats,columnWidth:null!=(l=null==i?void 0:i.columnWidth)?l:n.config.columnWidth,sortOrder:null!=(r=null==i?void 0:i.sortOrder)?r:n.config.sortOrder}})}),t.sort((e,t)=>e.config.sortOrder-t.config.sortOrder),c(t),null==n||n(s.payload.fields)}}catch(e){console.error("Error fetching fields and configuration:",e),o.oR.error("Lỗi khi tải danh s\xe1ch trường t\xf9y chỉnh")}finally{T(!1)}},R=async e=>{try{let n=localStorage.getItem("sessionToken")||"",s=await p.A.createCustomField({...e,targetModel:t},n);s.payload.success?(o.oR.success("Tạo trường t\xf9y chỉnh th\xe0nh c\xf4ng"),S(!1),B()):o.oR.error(s.payload.message||"Kh\xf4ng thể tạo trường t\xf9y chỉnh")}catch(e){console.error("Error creating custom field:",e),o.oR.error("Lỗi khi tạo trường t\xf9y chỉnh")}},V=async(e,t)=>{try{let n=localStorage.getItem("sessionToken")||"",s=await p.A.updateCustomField(e,t,n);s.payload.success?(o.oR.success("Cập nhật trường t\xf9y chỉnh th\xe0nh c\xf4ng"),A(null),B()):o.oR.error(s.payload.message||"Kh\xf4ng thể cập nhật trường t\xf9y chỉnh")}catch(e){console.error("Error updating custom field:",e),o.oR.error("Lỗi khi cập nhật trường t\xf9y chỉnh")}},F=async e=>{let t=e.isDefault?"trường cơ bản":"trường t\xf9y chỉnh";if(confirm("Bạn c\xf3 chắc chắn muốn x\xf3a ".concat(t,' "').concat(e.label,'"? Dữ liệu đ\xe3 nhập sẽ kh\xf4ng bị mất nhưng trường sẽ kh\xf4ng hiển thị nữa.')))try{if(e.isDefault){let n=i.filter(t=>t._id!==e._id);c(n),o.oR.success("X\xf3a ".concat(t," th\xe0nh c\xf4ng"))}else{let t=localStorage.getItem("sessionToken")||"",n=await p.A.deleteCustomField(e._id,t);n.payload.success?(o.oR.success("X\xf3a trường t\xf9y chỉnh th\xe0nh c\xf4ng"),B()):o.oR.error(n.payload.message||"Kh\xf4ng thể x\xf3a trường t\xf9y chỉnh")}}catch(e){console.error("Error deleting field:",e),o.oR.error("Lỗi khi x\xf3a ".concat(t))}},O=async e=>{try{let n=localStorage.getItem("sessionToken")||"";if(!e.isDefault)return void await V(e._id,{config:{...e.config,showInList:!e.config.showInList}});await y.A.updateFieldVisibility({targetModel:t,fieldName:e.name,showInList:!e.config.showInList},n),await B(),o.oR.success("".concat(e.config.showInList?"Ẩn":"Hiển thị",' cột "').concat(e.label,'" th\xe0nh c\xf4ng'))}catch(e){console.error("Error toggling field visibility:",e),o.oR.error("Lỗi khi cập nhật hiển thị trường")}},K=e=>{e.preventDefault(),e.dataTransfer.dropEffect="move"},W=async(e,n)=>{if(e.preventDefault(),!L||L===n)return void E(null);let s=i.findIndex(e=>e._id===L),a=i.findIndex(e=>e._id===n);if(-1===s||-1===a)return void E(null);let l=[...i],[r]=l.splice(s,1);l.splice(a,0,r);let c=l.map((e,t)=>({fieldName:e.name,sortOrder:t}));try{let e=localStorage.getItem("sessionToken")||"";await y.A.updateFieldOrder({targetModel:t,fieldOrders:c},e);let n=l.filter(e=>!e.isDefault).map((e,t)=>({id:e._id,sortOrder:t}));n.length>0&&await p.A.updateFieldsOrder(n,e),await B(),o.oR.success("Cập nhật thứ tự trường th\xe0nh c\xf4ng")}catch(e){console.error("Error updating field order:",e),o.oR.error("Lỗi khi cập nhật thứ tự trường")}E(null)},G=i.filter(e=>{let t=""===z||e.name.toLowerCase().includes(z.toLowerCase())||e.label.toLowerCase().includes(z.toLowerCase()),n="all"===M||"builtin"===M&&e.isDefault||"custom"===M&&!e.isDefault||"visible"===M&&e.config.showInList||"hidden"===M&&!e.config.showInList;return t&&n});return C?(0,s.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 flex items-center gap-2",children:[(0,s.jsx)(d.A,{size:20}),"Quản l\xfd trường t\xf9y chỉnh"]}),(0,s.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:["Tạo v\xe0 quản l\xfd c\xe1c trường dữ liệu t\xf9y chỉnh cho ","CourtCase"===t?"vụ việc t\xf2a \xe1n":t]})]}),_("custom_fields_create")&&(0,s.jsxs)("button",{onClick:()=>S(!0),className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,s.jsx)(h,{size:16}),"Th\xeam trường mới"]})]}),i.length>0&&(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,s.jsx)(d.A,{size:20,className:"text-blue-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-900",children:i.length}),(0,s.jsx)("div",{className:"text-sm text-blue-700",children:"Tổng số trường"})]})]})}),(0,s.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,s.jsx)(h,{size:20,className:"text-green-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-900",children:i.filter(e=>!e.isDefault).length}),(0,s.jsx)("div",{className:"text-sm text-green-700",children:"Trường đ\xe3 th\xeam"})]})]})}),(0,s.jsx)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,s.jsx)(g.A,{size:20,className:"text-purple-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-purple-900",children:i.filter(e=>e.config.showInList).length}),(0,s.jsx)("div",{className:"text-sm text-purple-700",children:"Đang hiển thị"})]})]})}),(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,s.jsx)("span",{className:"text-blue-600 text-lg",children:"\uD83D\uDD12"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-900",children:i.filter(e=>e.isDefault).length}),(0,s.jsx)("div",{className:"text-sm text-blue-700",children:"Trường cơ bản"})]})]})})]}),i.length>0&&(0,s.jsxs)("div",{className:"flex flex-wrap gap-2 mb-6",children:[(0,s.jsx)("button",{onClick:()=>{c(i.map(e=>({...e,config:{...e.config,showInList:!0}}))),o.oR.success("Hiển thị tất cả trường th\xe0nh c\xf4ng")},className:"px-3 py-1 text-sm bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors",children:"\uD83D\uDC41️ Hiển thị tất cả"}),(0,s.jsx)("button",{onClick:()=>{c(i.map(e=>({...e,config:{...e.config,showInList:!1}}))),o.oR.success("Ẩn tất cả trường th\xe0nh c\xf4ng")},className:"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:"\uD83D\uDE48 Ẩn tất cả"}),(0,s.jsx)("button",{onClick:()=>{c([...i.filter(e=>e.isDefault).map(e=>({...e,config:{...e.config,showInList:!0}})),...i.filter(e=>!e.isDefault).map(e=>({...e,config:{...e.config,showInList:!1}}))]),o.oR.success("Chỉ hiển thị trường cơ bản")},className:"px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors",children:"\uD83D\uDCCB Chỉ cơ bản"}),(0,s.jsx)("button",{onClick:()=>{c([...i.filter(e=>e.isDefault).map(e=>({...e,config:{...e.config,showInList:!1}})),...i.filter(e=>!e.isDefault).map(e=>({...e,config:{...e.config,showInList:!0}}))]),o.oR.success("Chỉ hiển thị trường đ\xe3 th\xeam")},className:"px-3 py-1 text-sm bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors",children:"➕ Chỉ đ\xe3 th\xeam"})]}),i.length>0&&(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mb-6",children:[(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("input",{type:"text",placeholder:"T\xecm kiếm trường theo t\xean hoặc nh\xe3n...",value:z,onChange:e=>q(e.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})}),(0,s.jsxs)("select",{value:M,onChange:e=>H(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"all",children:"Tất cả trường"}),(0,s.jsx)("option",{value:"builtin",children:"Chỉ trường cơ bản"}),(0,s.jsx)("option",{value:"custom",children:"Chỉ trường đ\xe3 th\xeam"}),(0,s.jsx)("option",{value:"visible",children:"Chỉ trường hiển thị"}),(0,s.jsx)("option",{value:"hidden",children:"Chỉ trường ẩn"})]})]}),0===i.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(d.A,{size:48,className:"mx-auto text-gray-400 mb-4"}),(0,s.jsx)("p",{className:"text-gray-500 mb-4",children:"Đang tải danh s\xe1ch trường..."})]}):0===G.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(d.A,{size:48,className:"mx-auto text-gray-400 mb-4"}),(0,s.jsx)("p",{className:"text-gray-500 mb-4",children:"Kh\xf4ng t\xecm thấy trường n\xe0o ph\xf9 hợp"}),(0,s.jsx)("button",{onClick:()=>{q(""),H("all")},className:"text-blue-600 hover:text-blue-700 font-medium",children:"X\xf3a bộ lọc"})]}):(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3 mb-4",children:(0,s.jsxs)("div",{className:"flex items-start gap-2",children:[(0,s.jsx)("span",{className:"text-green-500 mt-0.5",children:"ℹ️"}),(0,s.jsxs)("div",{className:"text-sm text-green-700",children:[(0,s.jsx)("strong",{children:"Hướng dẫn:"})," Tất cả c\xe1c trường đều c\xf3 thể k\xe9o thả để sắp xếp thứ tự. Trường cơ bản (m\xe0u xanh dương) v\xe0 trường t\xf9y chỉnh (m\xe0u xanh l\xe1) đều c\xf3 thể di chuyển."]})]})}),G.map(e=>{var t;return(0,s.jsxs)("div",{draggable:!0,onDragStart:t=>{E(e._id),t.dataTransfer.effectAllowed="move"},onDragOver:K,onDrop:t=>W(t,e._id),className:"flex items-center gap-4 p-4 border rounded-lg hover:bg-gray-50 transition-colors cursor-move ".concat(L===e._id?"opacity-50":""," ").concat(!e.config.showInList?"bg-gray-50 border-gray-200":"border-gray-300"," ").concat(e.isDefault?"border-l-4 border-l-blue-500":"border-l-4 border-l-green-500"),children:[(0,s.jsx)(x,{size:16,className:"text-gray-400",title:"K\xe9o thả để sắp xếp thứ tự"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-lg",children:(e=>{switch(e){case"text":return"\uD83D\uDCDD";case"number":return"\uD83D\uDD22";case"date":return"\uD83D\uDCC5";case"datetime":return"\uD83D\uDD50";case"boolean":return"☑️";case"select":case"multiselect":return"\uD83D\uDCCB";case"currency":return"\uD83D\uDCB0";case"percentage":return"\uD83D\uDCCA";case"email":return"\uD83D\uDCE7";case"phone":return"\uD83D\uDCDE";case"url":return"\uD83D\uDD17";case"textarea":return"\uD83D\uDCC4";case"file":return"\uD83D\uDCCE";case"json":return"\uD83D\uDD27";default:return"❓"}})(e.dataType)}),(0,s.jsx)("span",{className:"font-medium text-gray-900",children:e.label}),(0,s.jsx)("span",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded",children:{text:"Văn bản",number:"Số",date:"Ng\xe0y",datetime:"Ng\xe0y giờ",boolean:"Đ\xfang/Sai",select:"Lựa chọn đơn",multiselect:"Lựa chọn nhiều",currency:"Tiền tệ",percentage:"Phần trăm",email:"Email",phone:"Số điện thoại",url:"Đường dẫn",textarea:"Văn bản d\xe0i",file:"File đ\xednh k\xe8m",json:"Dữ liệu JSON"}[t=e.dataType]||t}),e.config.required&&(0,s.jsx)("span",{className:"text-xs bg-red-100 text-red-600 px-2 py-1 rounded",children:"Bắt buộc"}),(0,s.jsx)("span",{className:"text-xs px-2 py-1 rounded ".concat(e.isDefault?"bg-blue-100 text-blue-600":"bg-green-100 text-green-600"),children:e.isDefault?"Cơ bản":"Đ\xe3 th\xeam"})]}),e.description&&(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.description})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("button",{onClick:()=>O(e),className:"p-2 rounded-lg transition-colors ".concat(e.config.showInList?"text-green-600 hover:bg-green-50":"text-gray-400 hover:bg-gray-100"),title:e.config.showInList?"Ẩn khỏi danh s\xe1ch":"Hiển thị trong danh s\xe1ch",children:e.config.showInList?(0,s.jsx)(g.A,{size:16}):(0,s.jsx)(u,{size:16})}),_("custom_fields_edit")&&(0,s.jsx)("button",{onClick:()=>A(e),className:"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"Chỉnh sửa",children:(0,s.jsx)(m,{size:16})}),_("custom_fields_delete")&&(0,s.jsx)("button",{onClick:()=>F(e),className:"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors",title:"X\xf3a",children:(0,s.jsx)(b,{size:16})})]})]},e._id)}),0===i.filter(e=>!e.isDefault).length&&(0,s.jsxs)("div",{className:"text-center py-6 border-2 border-dashed border-gray-300 rounded-lg mt-4",children:[(0,s.jsx)("p",{className:"text-gray-500 mb-2",children:"Chưa c\xf3 trường n\xe0o được th\xeam"}),_("custom_fields_create")&&(0,s.jsx)("button",{onClick:()=>S(!0),className:"text-blue-600 hover:text-blue-700 font-medium",children:"Tạo trường đầu ti\xean"})]})]}),(0,s.jsx)(v,{isOpen:D,onClose:()=>S(!1),onSubmit:R,targetModel:t}),I&&(0,s.jsx)(N,{isOpen:!0,onClose:()=>A(null),onSubmit:e=>{I.isDefault?(c(i.map(t=>t._id===I._id?{...t,label:e.label,description:e.description,config:{...t.config,...e.config}}:t)),A(null),o.oR.success("Cập nhật trường cơ bản th\xe0nh c\xf4ng")):V(I._id,e)},field:I})]})}let T=(0,l.A)("chart-pie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]]),D=(0,l.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]),S=(0,l.A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);function I(e){let{targetModel:t="CourtCase",fields:n}=e,[l,r]=(0,a.useState)({}),[c,d]=(0,a.useState)(!0);(0,a.useEffect)(()=>{h()},[t]);let h=async()=>{try{d(!0);let e=localStorage.getItem("sessionToken")||"",n=await p.A.getCustomFieldStats(t,e);n.payload.success&&r(n.payload.stats)}catch(e){console.error("Error fetching custom field stats:",e),o.oR.error("Lỗi khi tải thống k\xea trường t\xf9y chỉnh")}finally{d(!1)}},g=e=>new Intl.NumberFormat("vi-VN").format(e),x=e=>new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(e);if(c)return(0,s.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})});let u=Object.entries(l);return 0===u.length?(0,s.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(i,{size:48,className:"mx-auto text-gray-400 mb-4"}),(0,s.jsx)("p",{className:"text-gray-500 mb-4",children:"Chưa c\xf3 thống k\xea cho trường t\xf9y chỉnh"}),(0,s.jsx)("p",{className:"text-sm text-gray-400",children:"Th\xeam dữ liệu v\xe0o c\xe1c trường t\xf9y chỉnh để xem thống k\xea"})]})}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,s.jsx)(i,{size:24,className:"text-blue-600"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Thống k\xea trường t\xf9y chỉnh"})]}),(0,s.jsx)("p",{className:"text-gray-600",children:"Thống k\xea tự động được t\xednh to\xe1n dựa tr\xean dữ liệu trong c\xe1c trường t\xf9y chỉnh"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:u.map(e=>{let[t,a]=e,l=n.find(e=>e.name===t);return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,s.jsxs)("div",{className:"p-2 bg-blue-100 rounded-lg",children:["distribution"===a.type&&(0,s.jsx)(T,{size:20,className:"text-blue-600"}),"numeric"===a.type&&(0,s.jsx)(D,{size:20,className:"text-blue-600"}),"boolean"===a.type&&(0,s.jsx)(S,{size:20,className:"text-blue-600"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-900",children:a.label}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:(null==l?void 0:l.dataType)==="currency"?"Tiền tệ":(null==l?void 0:l.dataType)==="percentage"?"Phần trăm":(null==l?void 0:l.dataType)==="select"?"Lựa chọn đơn":(null==l?void 0:l.dataType)==="multiselect"?"Lựa chọn nhiều":(null==l?void 0:l.dataType)==="boolean"?"Đ\xfang/Sai":(null==l?void 0:l.dataType)==="number"?"Số":"Kh\xe1c"})]})]}),"distribution"===a.type&&Array.isArray(a.data)&&(0,s.jsx)("div",{className:"space-y-3",children:a.data.map((e,t)=>{let n=a.data.reduce((e,t)=>e+t.count,0),l=n>0?e.count/n*100:0;return(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-3 h-3 rounded-full bg-blue-500"}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e._id||"Kh\xf4ng x\xe1c định"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:g(e.count)}),(0,s.jsxs)("span",{className:"text-xs text-gray-500",children:["(",l.toFixed(1),"%)"]})]})]},t)})}),"numeric"===a.type&&a.data&&(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,s.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:(null==l?void 0:l.dataType)==="currency"?x(a.data.total||0):g(a.data.total||0)}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Tổng"})]}),(0,s.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,s.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:(null==l?void 0:l.dataType)==="currency"?x(a.data.avg||0):g(a.data.avg||0)}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Trung b\xecnh"})]}),(0,s.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,s.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:(null==l?void 0:l.dataType)==="currency"?x(a.data.min||0):g(a.data.min||0)}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Nhỏ nhất"})]}),(0,s.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,s.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:(null==l?void 0:l.dataType)==="currency"?x(a.data.max||0):g(a.data.max||0)}),(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Lớn nhất"})]})]}),"boolean"===a.type&&Array.isArray(a.data)&&(0,s.jsx)("div",{className:"space-y-3",children:a.data.map((e,t)=>{let n=a.data.reduce((e,t)=>e+t.count,0),l=n>0?e.count/n*100:0;return(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(e._id?"bg-green-500":"bg-red-500")}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e._id?"C\xf3":"Kh\xf4ng"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:g(e.count)}),(0,s.jsxs)("span",{className:"text-xs text-gray-500",children:["(",l.toFixed(1),"%)"]})]})]},t)})})]},t)})}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("button",{onClick:h,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"L\xe0m mới thống k\xea"})})]})}var A=n(26983);let L=(0,l.A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);var E=n(52056),z=n(52726);let q=(0,l.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]);function M(e){var t,n,l;let{isOpen:r,onClose:i,onSubmit:c,availableFields:o}=e,[d,h]=(0,a.useState)(""),[g,x]=(0,a.useState)({enabled:!0,warningDays:30,dangerDays:7,showInList:!0,showCountdownBadge:!0,showColorWarning:!0,hideExpired:!1,emailNotification:{enabled:!1,daysBefore:7}}),u=e=>{if(e.preventDefault(),!d)return void alert("Vui l\xf2ng chọn trường ng\xe0y");if(g.warningDays&&g.dangerDays&&g.dangerDays>=g.warningDays)return void alert("Số ng\xe0y nguy hiểm phải nhỏ hơn số ng\xe0y cảnh b\xe1o");let t=o.find(e=>e.name===d);t&&c(t.name,t.isDefault,g)};return r?(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,s.jsx)(A.A,{size:20,className:"text-blue-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Tạo cấu h\xecnh đếm ngược"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Thiết lập cảnh b\xe1o hết hạn cho trường ng\xe0y (GMT+7)"}),(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded p-2 mt-2",children:(0,s.jsxs)("p",{className:"text-xs text-blue-700",children:["\uD83D\uDCA1 ",(0,s.jsx)("strong",{children:"Lưu \xfd:"})," Ng\xe0y được chọn l\xe0 ng\xe0y bắt đầu đếm ngược, hệ thống sẽ cộng số ng\xe0y cảnh b\xe1o để t\xednh ng\xe0y hết hạn"]})})]})]}),(0,s.jsx)("button",{onClick:i,className:"text-gray-500 hover:text-gray-700 transition-colors p-2 hover:bg-gray-100 rounded-lg",children:(0,s.jsx)(f.A,{size:20})})]}),(0,s.jsxs)("form",{onSubmit:u,className:"p-6 space-y-6 overflow-y-auto max-h-[calc(90vh-140px)]",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Chọn trường ng\xe0y ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsxs)("select",{value:d,onChange:e=>h(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0,children:[(0,s.jsx)("option",{value:"",children:"-- Chọn trường ng\xe0y --"}),o.map(e=>(0,s.jsxs)("option",{value:e.name,children:[e.label," ",e.isDefault?"(Cơ bản)":"(T\xf9y chỉnh)"]},e.name))]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("h4",{className:"text-md font-medium text-gray-900 flex items-center gap-2",children:[(0,s.jsx)(E.A,{size:16}),"Cấu h\xecnh cảnh b\xe1o"]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số ng\xe0y cảnh b\xe1o"}),(0,s.jsx)("input",{type:"number",min:"1",max:"365",value:g.warningDays||30,onChange:e=>x(t=>({...t,warningDays:parseInt(e.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Hiển thị cảnh b\xe1o v\xe0ng khi c\xf2n X ng\xe0y"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số ng\xe0y nguy hiểm"}),(0,s.jsx)("input",{type:"number",min:"1",max:"365",value:g.dangerDays||7,onChange:e=>x(t=>({...t,dangerDays:parseInt(e.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Hiển thị cảnh b\xe1o đỏ khi c\xf2n X ng\xe0y"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h4",{className:"text-md font-medium text-gray-900",children:"Cấu h\xecnh hiển thị"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("label",{className:"flex items-center gap-3",children:[(0,s.jsx)("input",{type:"checkbox",checked:g.enabled,onChange:e=>x(t=>({...t,enabled:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium",children:"Bật đếm ngược"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"K\xedch hoạt t\xednh năng đếm ngược cho trường n\xe0y"})]})]}),(0,s.jsxs)("label",{className:"flex items-center gap-3",children:[(0,s.jsx)("input",{type:"checkbox",checked:g.showCountdownBadge,onChange:e=>x(t=>({...t,showCountdownBadge:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium",children:"Hiển thị badge đếm ngược"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Hiển thị đếm ngược chi tiết (ng\xe0y, giờ, ph\xfat, gi\xe2y) theo GMT+7"})]})]}),(0,s.jsxs)("label",{className:"flex items-center gap-3",children:[(0,s.jsx)("input",{type:"checkbox",checked:g.showColorWarning,onChange:e=>x(t=>({...t,showColorWarning:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium",children:"Hiển thị m\xe0u cảnh b\xe1o"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Thay đổi m\xe0u sắc theo trạng th\xe1i (xanh/v\xe0ng/đỏ)"})]})]}),(0,s.jsxs)("label",{className:"flex items-center gap-3",children:[(0,s.jsx)("input",{type:"checkbox",checked:g.hideExpired,onChange:e=>x(t=>({...t,hideExpired:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium",children:"Ẩn nếu qu\xe1 hạn"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Tự động ẩn badge khi đ\xe3 qu\xe1 hạn"})]})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("h4",{className:"text-md font-medium text-gray-900 flex items-center gap-2",children:[(0,s.jsx)(q,{size:16}),"Th\xf4ng b\xe1o email (T\xf9y chọn)"]}),(0,s.jsxs)("label",{className:"flex items-center gap-3",children:[(0,s.jsx)("input",{type:"checkbox",checked:null==(t=g.emailNotification)?void 0:t.enabled,onChange:e=>x(t=>({...t,emailNotification:{...t.emailNotification,enabled:e.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium",children:"Gửi email cảnh b\xe1o"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Tự động gửi email th\xf4ng b\xe1o trước hạn"})]})]}),(null==(n=g.emailNotification)?void 0:n.enabled)&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Gửi email trước (ng\xe0y)"}),(0,s.jsx)("input",{type:"number",min:"1",max:"365",value:(null==(l=g.emailNotification)?void 0:l.daysBefore)||7,onChange:e=>x(t=>({...t,emailNotification:{...t.emailNotification,daysBefore:parseInt(e.target.value)}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[(0,s.jsx)("h5",{className:"text-sm font-medium text-gray-900 mb-2",children:"Xem trước:"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Ng\xe0y:"}),(0,s.jsx)("span",{className:"text-sm",children:"25/12/2024"})]}),g.showCountdownBadge&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Badge:"}),(0,s.jsx)("span",{className:"inline-flex items-center gap-1 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 border border-yellow-200 rounded-full",children:"⏰ C\xf2n 15 ng\xe0y"})]})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-4 p-6 border-t border-gray-200",children:[(0,s.jsx)("button",{type:"button",onClick:()=>{h(""),x({enabled:!0,warningDays:30,dangerDays:7,showInList:!0,showCountdownBadge:!0,showColorWarning:!0,hideExpired:!1,emailNotification:{enabled:!1,daysBefore:7}}),i()},className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,s.jsx)("button",{type:"submit",form:"countdown-form",onClick:u,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Tạo cấu h\xecnh"})]})]})}):null}function H(e){var t,n,l;let{isOpen:r,onClose:i,onSubmit:c,countdown:o,getFieldLabel:d}=e,[h,g]=(0,a.useState)({});(0,a.useEffect)(()=>{o&&g(o.countdownConfig)},[o]);let x=e=>{if(e.preventDefault(),o){if(h.warningDays&&h.dangerDays&&h.dangerDays>=h.warningDays)return void alert("Số ng\xe0y nguy hiểm phải nhỏ hơn số ng\xe0y cảnh b\xe1o");c(o._id,h)}};return r&&o?(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,s.jsx)(A.A,{size:20,className:"text-blue-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Chỉnh sửa cấu h\xecnh đếm ngược"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:d(o.fieldName)})]})]}),(0,s.jsx)("button",{onClick:i,className:"text-gray-500 hover:text-gray-700 transition-colors p-2 hover:bg-gray-100 rounded-lg",children:(0,s.jsx)(f.A,{size:20})})]}),(0,s.jsxs)("form",{onSubmit:x,className:"p-6 space-y-6 overflow-y-auto max-h-[calc(90vh-140px)]",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("h4",{className:"text-md font-medium text-gray-900 flex items-center gap-2",children:[(0,s.jsx)(E.A,{size:16}),"Cấu h\xecnh cảnh b\xe1o"]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số ng\xe0y cảnh b\xe1o"}),(0,s.jsx)("input",{type:"number",min:"1",max:"365",value:h.warningDays||30,onChange:e=>g(t=>({...t,warningDays:parseInt(e.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Hiển thị cảnh b\xe1o v\xe0ng khi c\xf2n X ng\xe0y"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số ng\xe0y nguy hiểm"}),(0,s.jsx)("input",{type:"number",min:"1",max:"365",value:h.dangerDays||7,onChange:e=>g(t=>({...t,dangerDays:parseInt(e.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Hiển thị cảnh b\xe1o đỏ khi c\xf2n X ng\xe0y"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h4",{className:"text-md font-medium text-gray-900",children:"Cấu h\xecnh hiển thị"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("label",{className:"flex items-center gap-3",children:[(0,s.jsx)("input",{type:"checkbox",checked:h.enabled,onChange:e=>g(t=>({...t,enabled:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium",children:"Bật đếm ngược"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"K\xedch hoạt t\xednh năng đếm ngược cho trường n\xe0y"})]})]}),(0,s.jsxs)("label",{className:"flex items-center gap-3",children:[(0,s.jsx)("input",{type:"checkbox",checked:h.showCountdownBadge,onChange:e=>g(t=>({...t,showCountdownBadge:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium",children:"Hiển thị badge đếm ngược"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:'Hiển thị badge "C\xf2n X ng\xe0y" trong danh s\xe1ch'})]})]}),(0,s.jsxs)("label",{className:"flex items-center gap-3",children:[(0,s.jsx)("input",{type:"checkbox",checked:h.showColorWarning,onChange:e=>g(t=>({...t,showColorWarning:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium",children:"Hiển thị m\xe0u cảnh b\xe1o"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Thay đổi m\xe0u sắc theo trạng th\xe1i (xanh/v\xe0ng/đỏ)"})]})]}),(0,s.jsxs)("label",{className:"flex items-center gap-3",children:[(0,s.jsx)("input",{type:"checkbox",checked:h.hideExpired,onChange:e=>g(t=>({...t,hideExpired:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium",children:"Ẩn nếu qu\xe1 hạn"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Tự động ẩn badge khi đ\xe3 qu\xe1 hạn"})]})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("h4",{className:"text-md font-medium text-gray-900 flex items-center gap-2",children:[(0,s.jsx)(q,{size:16}),"Th\xf4ng b\xe1o email (T\xf9y chọn)"]}),(0,s.jsxs)("label",{className:"flex items-center gap-3",children:[(0,s.jsx)("input",{type:"checkbox",checked:null==(t=h.emailNotification)?void 0:t.enabled,onChange:e=>g(t=>({...t,emailNotification:{...t.emailNotification,enabled:e.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium",children:"Gửi email cảnh b\xe1o"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Tự động gửi email th\xf4ng b\xe1o trước hạn"})]})]}),(null==(n=h.emailNotification)?void 0:n.enabled)&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Gửi email trước (ng\xe0y)"}),(0,s.jsx)("input",{type:"number",min:"1",max:"365",value:(null==(l=h.emailNotification)?void 0:l.daysBefore)||7,onChange:e=>g(t=>({...t,emailNotification:{...t.emailNotification,daysBefore:parseInt(e.target.value)}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[(0,s.jsx)("h5",{className:"text-sm font-medium text-gray-900 mb-2",children:"Xem trước:"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Ng\xe0y:"}),(0,s.jsx)("span",{className:"text-sm",children:"25/12/2024"})]}),h.showCountdownBadge&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Badge:"}),(0,s.jsx)("span",{className:"inline-flex items-center gap-1 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 border border-yellow-200 rounded-full",children:"⏰ C\xf2n 15 ng\xe0y"})]})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-4 p-6 border-t border-gray-200",children:[(0,s.jsx)("button",{type:"button",onClick:i,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,s.jsx)("button",{type:"submit",onClick:x,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Cập nhật"})]})]})}):null}var _=n(78874),B=n(42529);function R(e){let{date:t,warningDays:n=30,dangerDays:l=7,showIcon:r=!0,showDetailed:i=!1,size:c="md",className:o=""}=e,[d,h]=(0,a.useState)(null),[g,x]=(0,a.useState)("none");(0,a.useEffect)(()=>{if(!t){h(null),x("none");return}let e=()=>{let e=(()=>{let e=new Date(t),s=new Date(e);s.setDate(s.getDate()+n);let a=new Date,l=new Date(a.getTime()+6e4*a.getTimezoneOffset()+252e5);e.setHours(0,0,0,0),s.setHours(0,0,0,0),l.setHours(0,0,0,0);let r=e.getTime()-l.getTime(),i=s.getTime()-l.getTime();if(i<0)return{days:0,hours:0,minutes:0,seconds:0,totalDays:Math.ceil(i/864e5),isExpired:!0};if(r>0)return{days:0,hours:0,minutes:0,seconds:0,totalDays:Math.ceil(i/864e5),isExpired:!1};let c=Math.floor(i/864e5),o=Math.floor(i%864e5/36e5);return{days:c,hours:o,minutes:Math.floor(i%36e5/6e4),seconds:Math.floor(i%6e4/1e3),totalDays:Math.ceil(i/864e5),isExpired:!1}})();if(h(e),e.isExpired)x("expired");else{let n=new Date(t),s=new Date,a=new Date(s.getTime()+6e4*s.getTimezoneOffset()+252e5);n.setHours(0,0,0,0),a.setHours(0,0,0,0),n.getTime()-a.getTime()>0?x("safe"):e.totalDays<=l?x("danger"):x("warning")}};e();let s=setInterval(e,1e3);return()=>clearInterval(s)},[t,n,l]);let u=()=>{if(!d)return"Kh\xf4ng c\xf3 dữ liệu";if(d.isExpired)return"Đ\xe3 hết hạn";let e=new Date(t),n=new Date,s=new Date(n.getTime()+6e4*n.getTimezoneOffset()+252e5);e.setHours(0,0,0,0),s.setHours(0,0,0,0);let a=e.getTime()-s.getTime();if(a>0){let e=Math.ceil(a/864e5);return"C\xf2n ".concat(e," ng\xe0y")}if(1===d.totalDays)return"C\xf2n 1 ng\xe0y";{if(d.totalDays>1)return"C\xf2n ".concat(d.totalDays," ng\xe0y");if(0===d.totalDays)return"Hết hạn h\xf4m nay";let e=Math.abs(d.totalDays);return"Qu\xe1 hạn ".concat(e," ng\xe0y")}};if(!t||!d)return null;let m={expired:{bg:"bg-red-100",text:"text-red-800",border:"border-red-200",icon:_.A,color:"text-red-600"},danger:{bg:"bg-red-50",text:"text-red-700",border:"border-red-300",icon:E.A,color:"text-red-500"},warning:{bg:"bg-yellow-50",text:"text-yellow-700",border:"border-yellow-300",icon:E.A,color:"text-yellow-500"},safe:{bg:"bg-green-50",text:"text-green-700",border:"border-green-300",icon:B.A,color:"text-green-500"},none:{bg:"bg-gray-50",text:"text-gray-700",border:"border-gray-300",icon:A.A,color:"text-gray-500"}}[g],b={sm:{container:"px-2 py-1 text-xs",icon:12,gap:"gap-1"},md:{container:"px-3 py-1.5 text-sm",icon:14,gap:"gap-1.5"},lg:{container:"px-4 py-2 text-base",icon:16,gap:"gap-2"}}[c],p=m.icon;return(0,s.jsxs)("span",{className:"\n        inline-flex items-center ".concat(b.gap," ").concat(b.container,"\n        ").concat(m.bg," ").concat(m.text," ").concat(m.border,"\n        border rounded-full font-medium\n        ").concat(o,"\n      "),title:"".concat(u()," - Hạn: ").concat(new Date(t).toLocaleDateString("vi-VN",{timeZone:"Asia/Ho_Chi_Minh",year:"numeric",month:"2-digit",day:"2-digit"})," (GMT+7)"),children:[r&&(0,s.jsx)(p,{size:b.icon,className:m.color}),u()]})}let V=[{name:"ngayNhanVanThu",label:"Ng\xe0y nhận văn thư"},{name:"ngayThuLy",label:"Ng\xe0y thụ l\xfd"},{name:"ngayBanHanh",label:"Ng\xe0y ban h\xe0nh"}];function F(e){let{targetModel:t="CourtCase"}=e,[n,l]=(0,a.useState)([]),[r,i]=(0,a.useState)([]),[c,g]=(0,a.useState)([]),[x,u]=(0,a.useState)(!0),[y,f]=(0,a.useState)(null),[j,v]=(0,a.useState)(!1);(0,a.useEffect)(()=>{N()},[t]);let N=async()=>{try{u(!0);let e=localStorage.getItem("sessionToken")||"",[n,s]=await Promise.all([z.A.getDateCountdowns(t,e),p.A.getCustomFields(t,e)]);n.payload.success&&l(n.payload.countdowns),s.payload.success&&i(s.payload.fields);let a=s.payload.success?s.payload.fields.filter(e=>"date"===e.dataType||"datetime"===e.dataType):[],r=[...V.map(e=>({...e,isDefault:!0})),...a.map(e=>({name:e.name,label:e.label,isDefault:!1}))];g(r)}catch(e){console.error("Error fetching data:",e),o.oR.error("Lỗi khi tải dữ liệu")}finally{u(!1)}},w=async(e,n,s)=>{try{let a=localStorage.getItem("sessionToken")||"",l=await z.A.upsertDateCountdown({targetModel:t,fieldName:e,isBuiltIn:n,countdownConfig:s},a);l.payload.success?(o.oR.success("Tạo cấu h\xecnh đếm ngược th\xe0nh c\xf4ng"),v(!1),N()):o.oR.error(l.payload.message||"Kh\xf4ng thể tạo cấu h\xecnh đếm ngược")}catch(e){console.error("Error creating countdown:",e),o.oR.error("Lỗi khi tạo cấu h\xecnh đếm ngược")}},k=async(e,s)=>{try{let a=localStorage.getItem("sessionToken")||"",l=n.find(t=>t._id===e);if(!l)return;let r=await z.A.upsertDateCountdown({targetModel:t,fieldName:l.fieldName,isBuiltIn:l.isBuiltIn,countdownConfig:s},a);r.payload.success?(o.oR.success("Cập nhật cấu h\xecnh đếm ngược th\xe0nh c\xf4ng"),f(null),N()):o.oR.error(r.payload.message||"Kh\xf4ng thể cập nhật cấu h\xecnh đếm ngược")}catch(e){console.error("Error updating countdown:",e),o.oR.error("Lỗi khi cập nhật cấu h\xecnh đếm ngược")}},C=async e=>{if(confirm("Bạn c\xf3 chắc chắn muốn x\xf3a cấu h\xecnh đếm ngược n\xe0y?"))try{let t=localStorage.getItem("sessionToken")||"",n=await z.A.deleteDateCountdown(e,t);n.payload.success?(o.oR.success("X\xf3a cấu h\xecnh đếm ngược th\xe0nh c\xf4ng"),N()):o.oR.error(n.payload.message||"Kh\xf4ng thể x\xf3a cấu h\xecnh đếm ngược")}catch(e){console.error("Error deleting countdown:",e),o.oR.error("Lỗi khi x\xf3a cấu h\xecnh đếm ngược")}},T=e=>{let t=c.find(t=>t.name===e);return t?t.label:e},D=()=>{let e=n.map(e=>e.fieldName);return c.filter(t=>!e.includes(t.name))};return x?(0,s.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 flex items-center gap-2",children:[(0,s.jsx)(A.A,{size:20}),"Cấu h\xecnh đếm ngược ng\xe0y hết hạn"]}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Thiết lập cảnh b\xe1o v\xe0 đếm ngược cho c\xe1c trường ng\xe0y th\xe1ng (m\xfai giờ GMT+7)"}),(0,s.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-3",children:(0,s.jsxs)("div",{className:"flex items-start gap-2",children:[(0,s.jsx)("span",{className:"text-yellow-600 mt-0.5",children:"\uD83D\uDCA1"}),(0,s.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,s.jsx)("strong",{children:"C\xe1ch hoạt động:"})," Ng\xe0y được chọn l\xe0 ng\xe0y bắt đầu đếm ngược, cộng số ng\xe0y cảnh b\xe1o để t\xednh ng\xe0y hết hạn.",(0,s.jsx)("br",{}),(0,s.jsx)("strong",{children:"V\xed dụ:"})," Ng\xe0y bắt đầu 31/07/2025 + 90 ng\xe0y cảnh b\xe1o = Hết hạn 29/10/2025"]})]})}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,s.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:"\uD83C\uDF0F GMT+7 (Việt Nam)"}),(0,s.jsx)("span",{className:"text-xs text-gray-500",children:"Đếm ngược theo thời gian thực với độ ch\xednh x\xe1c đến gi\xe2y"})]})]}),(0,s.jsxs)("button",{onClick:()=>v(!0),disabled:0===D().length,className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,s.jsx)(h,{size:16}),"Th\xeam cấu h\xecnh"]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,s.jsx)(L,{size:20,className:"text-blue-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-900",children:c.length}),(0,s.jsx)("div",{className:"text-sm text-blue-700",children:"Trường ng\xe0y"})]})]})}),(0,s.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,s.jsx)(A.A,{size:20,className:"text-green-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-900",children:n.filter(e=>e.countdownConfig.enabled).length}),(0,s.jsx)("div",{className:"text-sm text-green-700",children:"Đang hoạt động"})]})]})}),(0,s.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,s.jsx)(E.A,{size:20,className:"text-yellow-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-yellow-900",children:n.filter(e=>e.countdownConfig.emailNotification.enabled).length}),(0,s.jsx)("div",{className:"text-sm text-yellow-700",children:"Email cảnh b\xe1o"})]})]})}),(0,s.jsx)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,s.jsx)(d.A,{size:20,className:"text-purple-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-purple-900",children:n.length}),(0,s.jsx)("div",{className:"text-sm text-purple-700",children:"Tổng cấu h\xecnh"})]})]})})]}),0===n.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(A.A,{size:48,className:"mx-auto text-gray-400 mb-4"}),(0,s.jsx)("p",{className:"text-gray-500 mb-4",children:"Chưa c\xf3 cấu h\xecnh đếm ngược n\xe0o"}),(0,s.jsx)("button",{onClick:()=>v(!0),disabled:0===D().length,className:"text-blue-600 hover:text-blue-700 font-medium disabled:text-gray-400",children:0===D().length?"Kh\xf4ng c\xf3 trường ng\xe0y n\xe0o để cấu h\xecnh":"Tạo cấu h\xecnh đầu ti\xean"})]}):(0,s.jsx)("div",{className:"space-y-4",children:n.map(e=>(0,s.jsx)("div",{className:"p-4 border rounded-lg ".concat(e.countdownConfig.enabled?"border-green-200 bg-green-50":"border-gray-200 bg-gray-50"),children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 rounded-lg ".concat(e.countdownConfig.enabled?"bg-green-100":"bg-gray-100"),children:(0,s.jsx)(A.A,{size:16,className:e.countdownConfig.enabled?"text-green-600":"text-gray-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900",children:T(e.fieldName)}),(0,s.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600 mt-1",children:[(0,s.jsxs)("span",{children:["Cảnh b\xe1o: ",e.countdownConfig.warningDays," ng\xe0y"]}),(0,s.jsxs)("span",{children:["Nguy hiểm: ",e.countdownConfig.dangerDays," ng\xe0y"]}),e.countdownConfig.emailNotification.enabled&&(0,s.jsx)("span",{className:"text-blue-600",children:"\uD83D\uDCE7 Email"})]}),e.countdownConfig.enabled&&(0,s.jsxs)("div",{className:"mt-2",children:[(0,s.jsx)("span",{className:"text-xs text-gray-500 mr-2",children:"Preview (đang đếm ngược):"}),(0,s.jsx)(R,{date:new Date(Date.now()+24*e.countdownConfig.dangerDays*36e5),warningDays:e.countdownConfig.warningDays,dangerDays:e.countdownConfig.dangerDays,size:"sm",showIcon:!0,showDetailed:!0}),(0,s.jsxs)("span",{className:"text-xs text-gray-500 ml-2",children:["(Ng\xe0y bắt đầu + ",e.countdownConfig.warningDays," ng\xe0y = Ng\xe0y hết hạn)"]})]})]})]})}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(e.countdownConfig.enabled?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"),children:e.countdownConfig.enabled?"Hoạt động":"Tắt"}),(0,s.jsx)("button",{onClick:()=>f(e),className:"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"Chỉnh sửa",children:(0,s.jsx)(m,{size:16})}),(0,s.jsx)("button",{onClick:()=>C(e._id),className:"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors",title:"X\xf3a",children:(0,s.jsx)(b,{size:16})})]})]})},e._id))}),(0,s.jsx)(M,{isOpen:j,onClose:()=>v(!1),onSubmit:w,availableFields:D()}),(0,s.jsx)(H,{isOpen:!!y,onClose:()=>f(null),onSubmit:k,countdown:y,getFieldLabel:T})]})}var O=n(48203);function K(){let e=(0,c.useRouter)(),[t,n]=(0,a.useState)("fields"),[l,o]=(0,a.useState)([]);return(0,s.jsx)(O.default,{requiredPermission:"custom_fields_view",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,s.jsx)("button",{onClick:()=>e.back(),className:"p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:(0,s.jsx)(r,{size:20})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Quản l\xfd trường t\xf9y chỉnh - Vụ việc t\xf2a \xe1n"}),(0,s.jsx)("p",{className:"text-gray-600 mt-1",children:"Tạo v\xe0 quản l\xfd c\xe1c trường dữ liệu t\xf9y chỉnh cho vụ việc t\xf2a \xe1n, giống như Excel"})]})]}),(0,s.jsxs)("div",{className:"flex space-x-1 mb-6",children:[(0,s.jsx)("button",{onClick:()=>n("fields"),className:"px-4 py-2 rounded-lg font-medium transition-colors ".concat("fields"===t?"bg-blue-600 text-white":"text-gray-600 hover:bg-gray-100"),children:"Quản l\xfd trường"}),(0,s.jsxs)("button",{onClick:()=>n("stats"),className:"flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ".concat("stats"===t?"bg-blue-600 text-white":"text-gray-600 hover:bg-gray-100"),children:[(0,s.jsx)(i,{size:16}),"Thống k\xea"]}),(0,s.jsx)("button",{onClick:()=>n("countdown"),className:"flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ".concat("countdown"===t?"bg-blue-600 text-white":"text-gray-600 hover:bg-gray-100"),children:"⏰ Đếm ngược"})]}),"fields"===t?(0,s.jsx)(C,{targetModel:"CourtCase",onFieldsChange:e=>{o(e)}}):"stats"===t?(0,s.jsx)(I,{targetModel:"CourtCase",fields:l}):(0,s.jsx)(F,{targetModel:"CourtCase"}),(0,s.jsxs)("div",{className:"mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-2",children:"\uD83D\uDCA1 T\xednh năng giống Excel"}),(0,s.jsxs)("div",{className:"text-blue-800 space-y-2",children:[(0,s.jsxs)("p",{children:["• ",(0,s.jsx)("strong",{children:"Tự động tạo cột:"})," Th\xeam trường mới sẽ tự động tạo cột trong bảng danh s\xe1ch"]}),(0,s.jsxs)("p",{children:["• ",(0,s.jsx)("strong",{children:"Nhiều kiểu dữ liệu:"})," Văn bản, số, ng\xe0y th\xe1ng, lựa chọn, tiền tệ, v.v."]}),(0,s.jsxs)("p",{children:["• ",(0,s.jsx)("strong",{children:"Thống k\xea tự động:"})," Hệ thống tự động t\xednh to\xe1n thống k\xea cho c\xe1c trường mới"]}),(0,s.jsxs)("p",{children:["• ",(0,s.jsx)("strong",{children:"K\xe9o thả sắp xếp:"})," Thay đổi thứ tự hiển thị cột bằng c\xe1ch k\xe9o thả"]}),(0,s.jsxs)("p",{children:["• ",(0,s.jsx)("strong",{children:"T\xf9y chỉnh hiển thị:"})," Ẩn/hiện cột, điều chỉnh độ rộng"]}),(0,s.jsxs)("p",{children:["• ",(0,s.jsx)("strong",{children:"Validation tự động:"})," Kiểm tra dữ liệu theo quy tắc đ\xe3 định"]})]})]})]})})}}},e=>{e.O(0,[9268,2739,4744,9696,8441,1255,7358],()=>e(e.s=30486)),_N_E=e.O()}]);