const CourtCase = require('../../models/CourtCase');
const { defineAbilityFor } = require('../permissions/abilities');
const { ForbiddenError } = require('@casl/ability');

// Get all court cases with pagination, filtering, and sorting
exports.getCourtCases = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  
  try {
    ForbiddenError.from(ability).throwUnlessCan('read', 'CourtCase');

    const {
      page = 1,
      limit = 20,
      search = '',
      loaiAn = '',
      trangThaiGiaiQuyet = '',
      thuTucApDung = '',
      fromDate = '',
      toDate = '',
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build filter object
    const filter = {};
    
    if (search) {
      filter.$or = [
        { soThuLy: { $regex: search, $options: 'i' } },
        { soBanAn: { $regex: search, $options: 'i' } },
        { biCaoNguoiKhieuKien: { $regex: search, $options: 'i' } },
        { toiDanhNoiDung: { $regex: search, $options: 'i' } },
        { thamPhanPhuTrach: { $regex: search, $options: 'i' } }
      ];
    }

    if (loaiAn) filter.loaiAn = loaiAn;
    if (trangThaiGiaiQuyet) filter.trangThaiGiaiQuyet = trangThaiGiaiQuyet;
    if (thuTucApDung) filter.thuTucApDung = thuTucApDung;

    // Date range filter
    if (fromDate || toDate) {
      filter.ngayThuLy = {};
      if (fromDate) filter.ngayThuLy.$gte = new Date(fromDate);
      if (toDate) filter.ngayThuLy.$lte = new Date(toDate);
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    // Handle special sorting for virtual fields
    const isVirtualFieldSort = sortBy === 'soNgayConLai';
    
    // For virtual field sorting, we need to get all data first then sort
    let cases, total;
    
    if (isVirtualFieldSort) {
      // Get all matching documents first
      const allCases = await CourtCase.find(filter)
        .populate('createdBy', 'username email')
        .populate('updatedBy', 'username email');
      
      total = allCases.length;
      
      // Sort by virtual field
      const sortedCases = allCases.sort((a, b) => {
        const aValue = a.soNgayConLai !== null ? a.soNgayConLai : (sortOrder === 'desc' ? -Infinity : Infinity);
        const bValue = b.soNgayConLai !== null ? b.soNgayConLai : (sortOrder === 'desc' ? -Infinity : Infinity);
        
        if (sortOrder === 'desc') {
          return bValue - aValue;
        } else {
          return aValue - bValue;
        }
      });
      
      // Apply pagination after sorting
      cases = sortedCases.slice(skip, skip + parseInt(limit));
    } else {
      // Normal sorting for database fields
      const sortOptions = {};
      sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;
      
      [cases, total] = await Promise.all([
        CourtCase.find(filter)
          .populate('createdBy', 'username email')
          .populate('updatedBy', 'username email')
          .sort(sortOptions)
          .skip(skip)
          .limit(parseInt(limit)),
        CourtCase.countDocuments(filter)
      ]);
    }

    const totalPages = Math.ceil(total / parseInt(limit));

    res.json({
      success: true,
      cases,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    });

  } catch (error) {
    console.error('Error getting court cases:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Get single court case by ID
exports.getCourtCaseById = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  
  try {
    const { id } = req.params;
    
    const courtCase = await CourtCase.findById(id)
      .populate('createdBy', 'username email')
      .populate('updatedBy', 'username email');

    if (!courtCase) {
      return res.status(404).json({ success: false, message: 'Court case not found' });
    }

    ForbiddenError.from(ability).throwUnlessCan('read', courtCase);

    res.json({
      success: true,
      case: courtCase
    });

  } catch (error) {
    console.error('Error getting court case:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Create new court case
exports.createCourtCase = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    ForbiddenError.from(ability).throwUnlessCan('create', 'CourtCase');

    // Always auto-generate STT based on the highest existing STT + 1
    const lastCase = await CourtCase.findOne({}).sort({ stt: -1 }).lean();
    const sttNumber = lastCase ? lastCase.stt + 1 : 1;

    // Clean up empty strings and convert to null for dates
    const cleanedData = { ...req.body };

    // Remove STT from input data if it exists (should be auto-generated only)
    delete cleanedData.stt;

    // Convert empty strings to null for date fields
    if (cleanedData.ngayNhanVanThu === '') cleanedData.ngayNhanVanThu = null;
    if (cleanedData.ngayThuLy === '') cleanedData.ngayThuLy = null;
    if (cleanedData.ngayBanHanh === '') cleanedData.ngayBanHanh = null;

    // Convert empty strings to empty string for text fields (keep consistent)
    Object.keys(cleanedData).forEach(key => {
      if (cleanedData[key] === undefined || cleanedData[key] === null) {
        cleanedData[key] = '';
      }
    });

    const courtCaseData = {
      ...cleanedData,
      stt: sttNumber,
      createdBy: req.user ? req.user.id : null
    };

    const courtCase = new CourtCase(courtCaseData);
    await courtCase.save();

    const populatedCase = await CourtCase.findById(courtCase._id)
      .populate('createdBy', 'username email')
      .lean();

    res.status(201).json({
      success: true,
      message: 'Court case created successfully',
      case: populatedCase
    });

  } catch (error) {
    console.error('Error creating court case:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else if (error.code === 11000) {
      // Handle duplicate key error
      const duplicateField = Object.keys(error.keyPattern)[0];
      const message = duplicateField === 'stt' ? 'STT already exists' :
                     duplicateField === 'soThuLy' ? 'Số thụ lý already exists' :
                     'Duplicate entry exists';
      res.status(400).json({ success: false, message });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error', details: error.message });
    }
  }
};

// Update court case
exports.updateCourtCase = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    const { id } = req.params;

    const existingCase = await CourtCase.findById(id);
    if (!existingCase) {
      return res.status(404).json({ success: false, message: 'Court case not found' });
    }

    ForbiddenError.from(ability).throwUnlessCan('update', existingCase);

    // Clean up empty strings and convert to null for dates
    const cleanedData = { ...req.body };

    // Convert empty strings to null for date fields
    if (cleanedData.ngayNhanVanThu === '') cleanedData.ngayNhanVanThu = null;
    if (cleanedData.ngayThuLy === '') cleanedData.ngayThuLy = null;
    if (cleanedData.ngayBanHanh === '') cleanedData.ngayBanHanh = null;

    // Convert empty strings to empty string for text fields (keep consistent)
    Object.keys(cleanedData).forEach(key => {
      if (cleanedData[key] === undefined || cleanedData[key] === null) {
        cleanedData[key] = '';
      }
    });

    const updateData = {
      ...cleanedData,
      updatedBy: req.user ? req.user.id : null
    };

    const courtCase = await CourtCase.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).populate('createdBy', 'username email')
     .populate('updatedBy', 'username email')
     .lean();

    res.json({
      success: true,
      message: 'Court case updated successfully',
      case: courtCase
    });

  } catch (error) {
    console.error('Error updating court case:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else if (error.code === 11000) {
      // Handle duplicate key error
      const duplicateField = Object.keys(error.keyPattern)[0];
      const message = duplicateField === 'stt' ? 'STT already exists' :
                     duplicateField === 'soThuLy' ? 'Số thụ lý already exists' :
                     'Duplicate entry exists';
      res.status(400).json({ success: false, message });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error', details: error.message });
    }
  }
};

// Delete court case
// Helper function to reorder STT after deletion
const reorderSTT = async () => {
  try {
    console.log('🔄 Starting STT reordering...');
    // Get all court cases ordered by creation date
    const allCases = await CourtCase.find({}).sort({ createdAt: 1 });
    console.log(`📊 Found ${allCases.length} court cases to reorder`);

    // Update STT for each case sequentially
    for (let i = 0; i < allCases.length; i++) {
      await CourtCase.findByIdAndUpdate(allCases[i]._id, { stt: i + 1 });
      console.log(`✅ Updated case ${allCases[i]._id} with STT: ${i + 1}`);
    }
    console.log('✅ STT reordering completed successfully');
  } catch (error) {
    console.error('❌ Error reordering STT:', error);
  }
};

exports.deleteCourtCase = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  
  try {
    const { id } = req.params;
    
    const courtCase = await CourtCase.findById(id);
    if (!courtCase) {
      return res.status(404).json({ success: false, message: 'Court case not found' });
    }

    ForbiddenError.from(ability).throwUnlessCan('delete', courtCase);

    await CourtCase.findByIdAndDelete(id);

    // Reorder STT after deletion
    await reorderSTT();

    res.json({
      success: true,
      message: 'Court case deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting court case:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Bulk delete court cases
exports.bulkDeleteCourtCases = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  
  try {
    const { ids } = req.body;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ success: false, message: 'No case IDs provided' });
    }

    // Check permissions for each case
    for (const id of ids) {
      const courtCase = await CourtCase.findById(id);
      if (courtCase) {
        ForbiddenError.from(ability).throwUnlessCan('delete', courtCase);
      }
    }

    // Delete all cases
    await CourtCase.deleteMany({ _id: { $in: ids } });

    // Reorder STT once after all deletions
    await reorderSTT();

    res.json({
      success: true,
      message: `Successfully deleted ${ids.length} court cases`
    });

  } catch (error) {
    console.error('Error bulk deleting court cases:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Get court case statistics
exports.getCourtCaseStats = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    ForbiddenError.from(ability).throwUnlessCan('read', 'CourtCase');
    ForbiddenError.from(ability).throwUnlessCan('read', 'CourtCaseStats');

    const stats = await CourtCase.aggregate([
      {
        $group: {
          _id: '$trangThaiGiaiQuyet',
          count: { $sum: 1 }
        }
      }
    ]);

    const totalCases = await CourtCase.countDocuments();

    const casesByType = await CourtCase.aggregate([
      {
        $group: {
          _id: '$loaiAn',
          count: { $sum: 1 }
        }
      }
    ]);

    const casesByProcedure = await CourtCase.aggregate([
      {
        $group: {
          _id: '$thuTucApDung',
          count: { $sum: 1 }
        }
      }
    ]);

    res.json({
      success: true,
      stats: {
        total: totalCases,
        byStatus: stats,
        byType: casesByType,
        byProcedure: casesByProcedure
      }
    });

  } catch (error) {
    console.error('Error getting court case stats:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Export court cases to Excel
exports.exportCourtCases = async (req, res) => {
  console.log('[DEBUG] Export request - User:', req.user ? {
    id: req.user._id,
    email: req.user.email,
    rule: req.user.rule,
    permissions: req.user.permissions
  } : 'No user');

  const ability = defineAbilityFor(req.user);

  try {
    ForbiddenError.from(ability).throwUnlessCan('read', 'CourtCase');
    ForbiddenError.from(ability).throwUnlessCan('export', 'CourtCase');

    const {
      search = '',
      loaiAn = '',
      trangThaiGiaiQuyet = '',
      thuTucApDung = '',
      fromDate = '',
      toDate = ''
    } = req.query;

    // Build filter object (same as getCourtCases)
    const filter = {};

    if (search) {
      filter.$or = [
        { soThuLy: { $regex: search, $options: 'i' } },
        { soBanAn: { $regex: search, $options: 'i' } },
        { biCaoNguoiKhieuKien: { $regex: search, $options: 'i' } },
        { toiDanhNoiDung: { $regex: search, $options: 'i' } },
        { thamPhanPhuTrach: { $regex: search, $options: 'i' } }
      ];
    }

    if (loaiAn) filter.loaiAn = loaiAn;
    if (trangThaiGiaiQuyet) filter.trangThaiGiaiQuyet = trangThaiGiaiQuyet;
    if (thuTucApDung) filter.thuTucApDung = thuTucApDung;

    // Date range filter
    if (fromDate || toDate) {
      filter.ngayThuLy = {};
      if (fromDate) filter.ngayThuLy.$gte = new Date(fromDate);
      if (toDate) filter.ngayThuLy.$lte = new Date(toDate);
    }

    // Get all matching cases
    const cases = await CourtCase.find(filter)
      .populate('createdBy', 'username email')
      .populate('updatedBy', 'username email')
      .sort({ stt: 1 })
      .lean();

    // Create Excel workbook
    const ExcelJS = require('exceljs');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Danh sách vụ việc');

    // Define columns based on the template format
    worksheet.columns = [
      { header: 'STT', key: 'stt', width: 10 },
      { header: 'SỐ VĂN THƯ', key: 'soVanThu', width: 20 },
      { header: 'NGÀY NHẬN VĂN THƯ', key: 'ngayNhanVanThu', width: 20 },
      { header: 'LOẠI ÁN', key: 'loaiAn', width: 15 },
      { header: 'SỐ THỤ LÝ', key: 'soThuLy', width: 20 },
      { header: 'NGÀY THỤ LÝ', key: 'ngayThuLy', width: 15 },
      { header: 'TAND', key: 'tand', width: 30 },
      { header: 'SỐ BẢN ÁN', key: 'soBanAn', width: 20 },
      { header: 'NGÀY BAN HÀNH BẢN ÁN', key: 'ngayBanHanh', width: 20 },
      { header: 'BỊ CÁO/NĐ/NKK', key: 'biCaoNguoiKhieuKien', width: 40 },
      { header: 'TỘI DANH/BĐ/NBK', key: 'toiDanhNoiDung', width: 40 },
      { header: 'TỘI DANH/QHPL', key: 'toiDanhQuanHePhatLuat', width: 30 },
      { header: 'HÌNH THỨC', key: 'hinhThucXuLy', width: 25 },
      { header: 'THỦ TỤC', key: 'thuTucApDung', width: 20 },
      { header: 'THẨM PHÁN', key: 'thamPhanPhuTrach', width: 25 },
      { header: 'TRƯỞNG/PHÓ PHÒNG KTNV/THẨM TRA VIÊN', key: 'truongPhoPhongKTNV', width: 35 },
      { header: 'GHI CHÚ', key: 'ghiChu', width: 30 },
      { header: 'GHI CHÚ KQ', key: 'ghiChuKetQua', width: 30 },
      { header: 'TRẠNG THÁI', key: 'trangThaiGiaiQuyet', width: 20 },
      { header: 'THỜI HẠN 90 NGÀY VỚI NGÀY NHẬN VĂN THƯ', key: 'thoiHan90NgayVoiNgayNhanVanThu', width: 35 },
      { header: 'THỜI GIAN CÒN LẠI CỦA THỜI HẠN 90 NGÀY NHẬN VĂN THƯ', key: 'thoiGianConLaiCuaThoiHan90NgayNhanVanThu', width: 40 }
    ];

    // Style header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE6F3FF' }
    };

    // Add data rows
    cases.forEach(courtCase => {
      worksheet.addRow({
        stt: courtCase.stt,
        soVanThu: courtCase.soVanThu || '',
        ngayNhanVanThu: courtCase.ngayNhanVanThu ? new Date(courtCase.ngayNhanVanThu).toLocaleDateString('vi-VN') : '',
        loaiAn: courtCase.loaiAn,
        soThuLy: courtCase.soThuLy,
        ngayThuLy: courtCase.ngayThuLy ? new Date(courtCase.ngayThuLy).toLocaleDateString('vi-VN') : '',
        tand: courtCase.tand,
        soBanAn: courtCase.soBanAn,
        ngayBanHanh: courtCase.ngayBanHanh ? new Date(courtCase.ngayBanHanh).toLocaleDateString('vi-VN') : '',
        biCaoNguoiKhieuKien: courtCase.biCaoNguoiKhieuKien,
        toiDanhNoiDung: courtCase.toiDanhNoiDung,
        toiDanhQuanHePhatLuat: courtCase.toiDanhQuanHePhatLuat || '',
        hinhThucXuLy: courtCase.hinhThucXuLy,
        thuTucApDung: courtCase.thuTucApDung,
        thamPhanPhuTrach: courtCase.thamPhanPhuTrach,
        truongPhoPhongKTNV: courtCase.truongPhoPhongKTNV,
        ghiChu: courtCase.ghiChu || '',
        ghiChuKetQua: courtCase.ghiChuKetQua || '',
        trangThaiGiaiQuyet: courtCase.trangThaiGiaiQuyet,
        thoiHan90NgayVoiNgayNhanVanThu: courtCase.ngayNhanVanThu ? 
          new Date(new Date(courtCase.ngayNhanVanThu).getTime() + 90 * 24 * 60 * 60 * 1000).toLocaleDateString('vi-VN') : '',
        thoiGianConLaiCuaThoiHan90NgayNhanVanThu: courtCase.ngayNhanVanThu ? 
          Math.ceil((new Date(new Date(courtCase.ngayNhanVanThu).getTime() + 90 * 24 * 60 * 60 * 1000).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)) : ''
      });
    });

    // Set response headers
    const fileName = `danh-sach-vu-viec-${new Date().toISOString().split('T')[0]}.xlsx`;
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);

    // Write to response
    await workbook.xlsx.write(res);
    res.end();

  } catch (error) {
    console.error('Error exporting court cases:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};

// Preview Excel file before import
exports.previewImport = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    ForbiddenError.from(ability).throwUnlessCan('create', 'CourtCase');
    ForbiddenError.from(ability).throwUnlessCan('import', 'CourtCase');

    if (!req.file) {
      return res.status(400).json({ success: false, message: 'No file uploaded' });
    }

    const ExcelJS = require('exceljs');
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(req.file.buffer);

    const worksheet = workbook.getWorksheet(1);
    if (!worksheet) {
      return res.status(400).json({ success: false, message: 'No worksheet found in Excel file' });
    }

    const previewData = [];
    const errors = [];
    const warnings = [];
    let totalRows = 0;

    // Get header row for validation
    const headerRow = worksheet.getRow(1);
    const expectedHeaders = [
      'STT', 'SỐ VĂN THƯ', 'NGÀY NHẬN VĂN THƯ', 'LOẠI ÁN', 'SỐ THỤ LÝ', 
      'NGÀY THỤ LÝ', 'TAND', 'SỐ BẢN ÁN', 'NGÀY BAN HÀNH BẢN ÁN', 'BỊ CÁO/NĐ/NKK',
      'TỘI DANH/NỘI DUNG', 'TỘI DANH/QHE PHÁP LUẬT', 'HÌNH THỨC XỬ LÝ', 'THỦ TỤC ÁP DỤNG',
      'THẨM PHÁN PHỤ TRÁCH', 'TRƯỞNG/PHÓ PHÒNG KTNV', 'GHI CHÚ', 'GHI CHÚ KẾT QUẢ', 'TRẠNG THÁI GIẢI QUYẾT'
    ];

    // Validate headers
    for (let i = 1; i <= expectedHeaders.length; i++) {
      const headerCell = headerRow.getCell(i);
      const actualHeader = headerCell.value ? headerCell.value.toString().trim() : '';
      const expectedHeader = expectedHeaders[i - 1];
      
      if (actualHeader !== expectedHeader) {
        warnings.push(`Cột ${i}: Tiêu đề không khớp. Mong đợi "${expectedHeader}", nhận được "${actualHeader}"`);
      }
    }

    // Process data rows (skip header)
    for (let rowNumber = 2; rowNumber <= Math.min(worksheet.rowCount, 102); rowNumber++) { // Limit preview to 100 rows
      const row = worksheet.getRow(rowNumber);

      // Skip empty rows
      if (!row.hasValues) continue;

      totalRows++;

      try {
        // Map Excel columns to database fields
        const courtCaseData = {
          stt: totalRows, // Auto-generate STT for preview
          soVanThu: row.getCell(2).value || '',
          ngayNhanVanThu: row.getCell(3).value ? new Date(row.getCell(3).value) : null,
          loaiAn: row.getCell(4).value || '',
          soThuLy: row.getCell(5).value || '',
          ngayThuLy: row.getCell(6).value ? new Date(row.getCell(6).value) : null,
          tand: row.getCell(7).value || '',
          soBanAn: row.getCell(8).value || '',
          ngayBanHanh: row.getCell(9).value ? new Date(row.getCell(9).value) : null,
          biCaoNguoiKhieuKien: row.getCell(10).value || '',
          toiDanhNoiDung: row.getCell(11).value || '',
          toiDanhQuanHePhatLuat: row.getCell(12).value || '',
          hinhThucXuLy: row.getCell(13).value || '',
          thuTucApDung: row.getCell(14).value || '',
          thamPhanPhuTrach: row.getCell(15).value || '',
          truongPhoPhongKTNV: row.getCell(16).value || '',
          ghiChu: row.getCell(17).value || '',
          ghiChuKetQua: row.getCell(18).value || '',
          trangThaiGiaiQuyet: row.getCell(19).value || 'Chưa giải quyết',
          rowNumber: rowNumber,
          status: 'valid',
          validationErrors: []
        };

        // Validate date fields
        ['ngayNhanVanThu', 'ngayThuLy', 'ngayBanHanh'].forEach(field => {
          if (courtCaseData[field] && courtCaseData[field] !== null) {
            const dateValue = courtCaseData[field];
            if (dateValue instanceof Date && isNaN(dateValue.getTime())) {
              courtCaseData[field] = null;
              courtCaseData.validationErrors.push(`${field}: Định dạng ngày không hợp lệ`);
              courtCaseData.status = 'warning';
            } else if (typeof dateValue === 'string') {
              const parsedDate = new Date(dateValue);
              if (isNaN(parsedDate.getTime())) {
                courtCaseData[field] = null;
                courtCaseData.validationErrors.push(`${field}: Không thể chuyển đổi định dạng ngày`);
                courtCaseData.status = 'warning';
              } else {
                courtCaseData[field] = parsedDate;
              }
            }
          }
        });

        // Validate enum fields - only validate trangThaiGiaiQuyet now
        const validTrangThaiGiaiQuyet = ['Chưa giải quyết', 'Đang giải quyết', 'Đã giải quyết', 'Tạm dừng', 'Đình chỉ'];

        if (courtCaseData.trangThaiGiaiQuyet && !validTrangThaiGiaiQuyet.includes(courtCaseData.trangThaiGiaiQuyet)) {
          courtCaseData.validationErrors.push(`Trạng thái giải quyết không hợp lệ: ${courtCaseData.trangThaiGiaiQuyet}`);
          courtCaseData.status = 'error';
        }

        // Check for required fields
        if (!courtCaseData.soThuLy) {
          courtCaseData.validationErrors.push('Số thụ lý là bắt buộc');
          courtCaseData.status = 'error';
        }

        // Check for duplicates in database
        if (courtCaseData.soThuLy) {
          const existingCase = await CourtCase.findOne({ soThuLy: courtCaseData.soThuLy });
          if (existingCase) {
            courtCaseData.validationErrors.push('Số thụ lý đã tồn tại trong hệ thống');
            courtCaseData.status = 'error';
          }
        }

        // Format dates for display
        ['ngayNhanVanThu', 'ngayThuLy', 'ngayBanHanh'].forEach(field => {
          if (courtCaseData[field]) {
            courtCaseData[`${field}Display`] = courtCaseData[field].toLocaleDateString('vi-VN');
          }
        });

        previewData.push(courtCaseData);

      } catch (error) {
        errors.push(`Dòng ${rowNumber}: ${error.message}`);
      }
    }

    // Count by status
    const validCount = previewData.filter(item => item.status === 'valid').length;
    const warningCount = previewData.filter(item => item.status === 'warning').length;
    const errorCount = previewData.filter(item => item.status === 'error').length;

    res.json({
      success: true,
      preview: {
        data: previewData,
        summary: {
          totalRows: totalRows,
          validRows: validCount,
          warningRows: warningCount,
          errorRows: errorCount,
          canImport: errorCount === 0
        },
        warnings: warnings,
        errors: errors
      }
    });

  } catch (error) {
    console.error('Error previewing import:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
    }
  }
};

// Import court cases from Excel
exports.importCourtCases = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    ForbiddenError.from(ability).throwUnlessCan('create', 'CourtCase');
    ForbiddenError.from(ability).throwUnlessCan('import', 'CourtCase');

    if (!req.file) {
      return res.status(400).json({ success: false, message: 'No file uploaded' });
    }

    const ExcelJS = require('exceljs');
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(req.file.buffer);

    const worksheet = workbook.getWorksheet(1);
    if (!worksheet) {
      return res.status(400).json({ success: false, message: 'No worksheet found in Excel file' });
    }

    const importResults = {
      total: 0,
      success: 0,
      errors: [],
      duplicates: 0
    };

    // Skip header row, start from row 2
    for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
      const row = worksheet.getRow(rowNumber);

      // Skip empty rows
      if (!row.hasValues) continue;

      importResults.total++;

      try {
        // Map Excel columns to database fields (updated for new template)
        const courtCaseData = {
          // STT is auto-generated, skip column 1
          soVanThu: row.getCell(2).value || '',
          ngayNhanVanThu: row.getCell(3).value ? new Date(row.getCell(3).value) : null,
          loaiAn: row.getCell(4).value || '',
          soThuLy: row.getCell(5).value || '',
          ngayThuLy: row.getCell(6).value ? new Date(row.getCell(6).value) : null,
          tand: row.getCell(7).value || '',
          soBanAn: row.getCell(8).value || '',
          ngayBanHanh: row.getCell(9).value ? new Date(row.getCell(9).value) : null,
          biCaoNguoiKhieuKien: row.getCell(10).value || '',
          toiDanhNoiDung: row.getCell(11).value || '',
          toiDanhQuanHePhatLuat: row.getCell(12).value || '',
          hinhThucXuLy: row.getCell(13).value || '',
          thuTucApDung: row.getCell(14).value || '',
          thamPhanPhuTrach: row.getCell(15).value || '',
          truongPhoPhongKTNV: row.getCell(16).value || '',
          ghiChu: row.getCell(17).value || '',
          ghiChuKetQua: row.getCell(18).value || '',
          trangThaiGiaiQuyet: row.getCell(19).value || 'Chưa giải quyết',
          createdBy: req.user ? req.user.id : null
        };

        // Validate date fields - handle various date formats
        ['ngayNhanVanThu', 'ngayThuLy', 'ngayBanHanh'].forEach(field => {
          if (courtCaseData[field] && courtCaseData[field] !== null) {
            const dateValue = courtCaseData[field];
            if (dateValue instanceof Date && isNaN(dateValue.getTime())) {
              courtCaseData[field] = null;
            } else if (typeof dateValue === 'string') {
              // Try to parse various date formats
              const parsedDate = new Date(dateValue);
              if (isNaN(parsedDate.getTime())) {
                courtCaseData[field] = null;
              } else {
                courtCaseData[field] = parsedDate;
              }
            }
          }
        });

        // Validate enum fields - only validate trangThaiGiaiQuyet now
        const validTrangThaiGiaiQuyet = ['Chưa giải quyết', 'Đang giải quyết', 'Đã giải quyết'];

        if (!validTrangThaiGiaiQuyet.includes(courtCaseData.trangThaiGiaiQuyet)) {
          courtCaseData.trangThaiGiaiQuyet = 'Chưa giải quyết';
        }

        // Auto-generate STT
        const lastCase = await CourtCase.findOne({ stt: { $exists: true, $ne: null } }).sort({ stt: -1 }).lean();
        courtCaseData.stt = lastCase ? lastCase.stt + 1 : importResults.success + 1;

        const courtCase = new CourtCase(courtCaseData);
        await courtCase.save();
        importResults.success++;

      } catch (error) {
        if (error.code === 11000) {
          importResults.duplicates++;
          importResults.errors.push(`Row ${rowNumber}: Duplicate entry (STT or Số thụ lý already exists)`);
        } else {
          importResults.errors.push(`Row ${rowNumber}: ${error.message}`);
        }
      }
    }

    res.json({
      success: true,
      message: 'Import completed',
      results: importResults
    });

  } catch (error) {
    console.error('Error importing court cases:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error', details: error.message });
    }
  }
};

// Download Excel import template
exports.downloadTemplate = async (req, res) => {
  try {
    const ExcelJS = require('exceljs');
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Mẫu Import Vụ Việc');

    // Define columns with proper Vietnamese headers
    worksheet.columns = [
      { header: 'STT', key: 'stt', width: 10 },
      { header: 'SỐ VĂN THƯ', key: 'soVanThu', width: 20 },
      { header: 'NGÀY NHẬN VĂN THƯ', key: 'ngayNhanVanThu', width: 20 },
      { header: 'LOẠI ÁN', key: 'loaiAn', width: 15 },
      { header: 'SỐ THỤ LÝ', key: 'soThuLy', width: 20 },
      { header: 'NGÀY THỤ LÝ', key: 'ngayThuLy', width: 15 },
      { header: 'TAND', key: 'tand', width: 30 },
      { header: 'SỐ BẢN ÁN', key: 'soBanAn', width: 20 },
      { header: 'NGÀY BAN HÀNH BẢN ÁN', key: 'ngayBanHanh', width: 20 },
      { header: 'BỊ CÁO/NĐ/NKK', key: 'biCaoNguoiKhieuKien', width: 40 },
      { header: 'TỘI DANH/BĐ/NBK', key: 'toiDanhNoiDung', width: 40 },
      { header: 'TỘI DANH/QHPL', key: 'toiDanhQuanHePhatLuat', width: 40 },
      { header: 'HÌNH THỨC', key: 'hinhThucXuLy', width: 25 },
      { header: 'THỦ TỤC', key: 'thuTucApDung', width: 20 },
      { header: 'THẨM PHÁN', key: 'thamPhanPhuTrach', width: 25 },
      { header: 'TRƯỞNG/PHÓ PHÒNG KTNV/THẨM TRA VIÊN', key: 'truongPhoPhongKTNV', width: 35 },
      { header: 'GHI CHÚ', key: 'ghiChu', width: 30 },
      { header: 'GHI CHÚ KQ', key: 'ghiChuKetQua', width: 30 },
      { header: 'TRẠNG THÁI', key: 'trangThaiGiaiQuyet', width: 20 }
    ];

    // Style header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FF4472C4' }
    };
    headerRow.alignment = { horizontal: 'center', vertical: 'middle' };
    headerRow.height = 30;

    // Add sample data with proper formatting
    worksheet.addRow({
      stt: 'Tự động tạo',
      soVanThu: '123/2024',
      ngayNhanVanThu: '01/01/2024',
      loaiAn: 'Hình sự',
      soThuLy: 'HS001/2024',
      ngayThuLy: '05/01/2024',
      tand: 'TAND Thành phố Hà Nội',
      soBanAn: 'BA001/2024',
      ngayBanHanh: '15/01/2024',
      biCaoNguoiKhieuKien: 'Nguyễn Văn A',
      toiDanhNoiDung: 'Trộm cắp tài sản',
      toiDanhQuanHePhatLuat: 'Điều 173 BLHS',
      hinhThucXuLy: '6 tháng tù',
      thuTucApDung: 'Sơ thẩm',
      thamPhanPhuTrach: 'Thẩm phán Nguyễn B',
      truongPhoPhongKTNV: 'Trưởng phòng KTNV',
      ghiChu: 'Ghi chú mẫu',
      ghiChuKetQua: 'Kết quả mẫu',
      trangThaiGiaiQuyet: 'Chưa giải quyết'
    });

    // Add validation rules as comments
    const validationSheet = workbook.addWorksheet('Hướng dẫn sử dụng');
    validationSheet.addRow(['HƯỚNG DẪN SỬ DỤNG MẪU IMPORT VỤ VIỆC TÒAS ÁN']);
    validationSheet.addRow(['']);
    validationSheet.addRow(['1. CÁC TRƯỜNG BẮT BUỘC:']);
    validationSheet.addRow(['   - Không có trường bắt buộc, tất cả đều tùy chọn']);
    validationSheet.addRow(['']);
    validationSheet.addRow(['2. ĐỊNH DẠNG NGÀY THÁNG:']);
    validationSheet.addRow(['   - Sử dụng định dạng: DD/MM/YYYY (ví dụ: 01/01/2024)']);
    validationSheet.addRow(['   - Hoặc MM/DD/YYYY (ví dụ: 01/01/2024)']);
    validationSheet.addRow(['']);
    validationSheet.addRow(['3. GIÁ TRỊ CHO TRƯỜNG "LOẠI ÁN":']);
    validationSheet.addRow(['   - Hình sự']);
    validationSheet.addRow(['   - Dân sự']);
    validationSheet.addRow(['   - Hành chính']);
    validationSheet.addRow(['   - Kinh tế']);
    validationSheet.addRow(['   - Lao động']);
    validationSheet.addRow(['   - Khác']);
    validationSheet.addRow(['   - (Để trống cũng được)']);
    validationSheet.addRow(['']);
    validationSheet.addRow(['4. GIÁ TRỊ CHO TRƯỜNG "THỦ TỤC":']);
    validationSheet.addRow(['   - Sơ thẩm']);
    validationSheet.addRow(['   - Phúc thẩm']);
    validationSheet.addRow(['   - Giám đốc thẩm']);
    validationSheet.addRow(['   - Tái thẩm']);
    validationSheet.addRow(['   - (Để trống cũng được)']);
    validationSheet.addRow(['']);
    validationSheet.addRow(['5. GIÁ TRỊ CHO TRƯỜNG "TRẠNG THÁI":']);
    validationSheet.addRow(['   - Chưa giải quyết']);
    validationSheet.addRow(['   - Đang giải quyết']);
    validationSheet.addRow(['   - Đã giải quyết']);
    validationSheet.addRow(['']);
    validationSheet.addRow(['6. LƯU Ý:']);
    validationSheet.addRow(['   - STT sẽ được tự động tạo, không cần nhập']);
    validationSheet.addRow(['   - Thời hạn 90 ngày được tính từ ngày nhận văn thư']);
    validationSheet.addRow(['   - Xóa dòng mẫu trước khi import dữ liệu thực']);

    // Style validation sheet
    const titleRow = validationSheet.getRow(1);
    titleRow.font = { bold: true, size: 14 };
    titleRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFFFE0' }
    };

    // Auto-fit columns
    validationSheet.columns = [
      { width: 50 }
    ];

    // Set response headers for download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename="mau-import-vu-viec-toa-an.xlsx"');

    // Send workbook
    await workbook.xlsx.write(res);
    res.end();

  } catch (error) {
    console.error('Error generating template:', error);
    res.status(500).json({ success: false, message: 'Lỗi tạo file mẫu', details: error.message });
  }
};

// Get detailed statistics
exports.getDetailedStats = async (req, res) => {
  const ability = defineAbilityFor(req.user);

  try {
    ForbiddenError.from(ability).throwUnlessCan('read', 'CourtCase');
    ForbiddenError.from(ability).throwUnlessCan('read', 'CourtCaseDetailedStats');

    const { fromDate, toDate, groupBy = 'month' } = req.query;

    // Build date filter
    const dateFilter = {};
    if (fromDate || toDate) {
      dateFilter.ngayThuLy = {};
      if (fromDate) dateFilter.ngayThuLy.$gte = new Date(fromDate);
      if (toDate) dateFilter.ngayThuLy.$lte = new Date(toDate);
    }

    // Basic stats
    const totalCases = await CourtCase.countDocuments(dateFilter);

    // Status distribution
    const statusStats = await CourtCase.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: '$trangThaiGiaiQuyet',
          count: { $sum: 1 }
        }
      }
    ]);

    // Case type distribution
    const typeStats = await CourtCase.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: '$loaiAn',
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Procedure distribution
    const procedureStats = await CourtCase.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: '$thuTucApDung',
          count: { $sum: 1 }
        }
      }
    ]);

    // Processing method distribution
    const processingStats = await CourtCase.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: '$hinhThucXuLy',
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Time-based trends
    let groupFormat;
    switch (groupBy) {
      case 'day':
        groupFormat = { $dateToString: { format: "%Y-%m-%d", date: "$ngayThuLy" } };
        break;
      case 'week':
        groupFormat = { $dateToString: { format: "%Y-W%U", date: "$ngayThuLy" } };
        break;
      case 'quarter':
        groupFormat = {
          $concat: [
            { $toString: { $year: "$ngayThuLy" } },
            "-Q",
            { $toString: { $ceil: { $divide: [{ $month: "$ngayThuLy" }, 3] } } }
          ]
        };
        break;
      case 'year':
        groupFormat = { $toString: { $year: "$ngayThuLy" } };
        break;
      default: // month
        groupFormat = { $dateToString: { format: "%Y-%m", date: "$ngayThuLy" } };
    }

    const trends = await CourtCase.aggregate([
      { $match: { ...dateFilter, ngayThuLy: { $ne: null } } },
      {
        $group: {
          _id: groupFormat,
          count: { $sum: 1 },
          resolved: {
            $sum: {
              $cond: [{ $eq: ['$trangThaiGiaiQuyet', 'Đã giải quyết'] }, 1, 0]
            }
          },
          pending: {
            $sum: {
              $cond: [{ $eq: ['$trangThaiGiaiQuyet', 'Chưa giải quyết'] }, 1, 0]
            }
          },
          inProgress: {
            $sum: {
              $cond: [{ $eq: ['$trangThaiGiaiQuyet', 'Đang giải quyết'] }, 1, 0]
            }
          }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    // Top judges by case count
    const topJudges = await CourtCase.aggregate([
      { $match: { ...dateFilter, thamPhanPhuTrach: { $ne: '' } } },
      {
        $group: {
          _id: '$thamPhanPhuTrach',
          count: { $sum: 1 },
          resolved: {
            $sum: {
              $cond: [{ $eq: ['$trangThaiGiaiQuyet', 'Đã giải quyết'] }, 1, 0]
            }
          }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    // Court distribution
    const courtStats = await CourtCase.aggregate([
      { $match: { ...dateFilter, tand: { $ne: '' } } },
      {
        $group: {
          _id: '$tand',
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } }
    ]);

    res.json({
      success: true,
      stats: {
        summary: {
          total: totalCases,
          period: fromDate && toDate ? `${fromDate} to ${toDate}` : 'All time'
        },
        byStatus: statusStats,
        byType: typeStats,
        byProcedure: procedureStats,
        byProcessingMethod: processingStats,
        trends: trends,
        topJudges: topJudges,
        byCourt: courtStats
      }
    });

  } catch (error) {
    console.error('Error getting detailed stats:', error);
    if (error.name === 'ForbiddenError') {
      res.status(403).json({ success: false, message: 'Access denied' });
    } else {
      res.status(500).json({ success: false, message: 'Internal server error' });
    }
  }
};
