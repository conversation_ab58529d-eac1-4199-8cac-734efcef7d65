/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/nextjs-toploader";
exports.ids = ["vendor-chunks/nextjs-toploader"];
exports.modules = {

/***/ "(rsc)/./node_modules/nextjs-toploader/dist/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/nextjs-toploader/dist/index.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// This file is generated by the Webpack next-flight-loader.\nconst { createProxy } = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\")\n\nmodule.exports = createProxy(\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\node_modules\\\\nextjs-toploader\\\\dist\\\\index.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dGpzLXRvcGxvYWRlci9kaXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJfTl9FLy4vbm9kZV9tb2R1bGVzL25leHRqcy10b3Bsb2FkZXIvZGlzdC9pbmRleC5qcy9fX25leHRqcy1pbnRlcm5hbC1wcm94eS5janMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gVGhpcyBmaWxlIGlzIGdlbmVyYXRlZCBieSB0aGUgV2VicGFjayBuZXh0LWZsaWdodC1sb2FkZXIuXG5jb25zdCB7IGNyZWF0ZVByb3h5IH0gPSByZXF1aXJlKFwibmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1sb2FkZXIvbW9kdWxlLXByb3h5XCIpXG5cbm1vZHVsZS5leHBvcnRzID0gY3JlYXRlUHJveHkoXCJDOlxcXFxVc2Vyc1xcXFxBZG1pblxcXFxEZXNrdG9wXFxcXGJsb2dcXFxcdGFuZHByb1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dGpzLXRvcGxvYWRlclxcXFxkaXN0XFxcXGluZGV4LmpzXCIpXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/nextjs-toploader/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/nextjs-toploader/dist/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/nextjs-toploader/dist/index.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar I = Object.create;\nvar y = Object.defineProperty;\nvar J = Object.getOwnPropertyDescriptor;\nvar X = Object.getOwnPropertyNames;\nvar _ = Object.getPrototypeOf, D = Object.prototype.hasOwnProperty;\nvar a = (r, o)=>y(r, \"name\", {\n        value: o,\n        configurable: !0\n    });\nvar G = (r, o)=>{\n    for(var i in o)y(r, i, {\n        get: o[i],\n        enumerable: !0\n    });\n}, A = (r, o, i, g)=>{\n    if (o && typeof o == \"object\" || typeof o == \"function\") for (let l of X(o))!D.call(r, l) && l !== i && y(r, l, {\n        get: ()=>o[l],\n        enumerable: !(g = J(o, l)) || g.enumerable\n    });\n    return r;\n};\nvar N = (r, o, i)=>(i = r != null ? I(_(r)) : {}, A(o || !r || !r.__esModule ? y(i, \"default\", {\n        value: r,\n        enumerable: !0\n    }) : i, r)), Q = (r)=>A(y({}, \"__esModule\", {\n        value: !0\n    }), r);\nvar Y = {};\nG(Y, {\n    default: ()=>V\n});\nmodule.exports = Q(Y);\nvar t = N(__webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\")), v = N(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\")), s = N(__webpack_require__(/*! nprogress */ \"(ssr)/./node_modules/nprogress/nprogress.js\"));\nvar M = a(({ color: r, height: o, showSpinner: i, crawl: g, crawlSpeed: l, initialPosition: L, easing: T, speed: E, shadow: x, template: k, zIndex: S = 1600, showAtBottom: H = !1 })=>{\n    let O = \"#29d\", m = r != null ? r : O, z = o != null ? o : 3, C = !x && x !== void 0 ? \"\" : x ? `box-shadow:${x}` : `box-shadow:0 0 10px ${m},0 0 5px ${m}`, K = v.createElement(\"style\", null, `#nprogress{pointer-events:none}#nprogress .bar{background:${m};position:fixed;z-index:${S};${H ? \"bottom: 0;\" : \"top: 0;\"}left:0;width:100%;height:${z}px}#nprogress .peg{display:block;position:absolute;right:0;width:100px;height:100%;${C};opacity:1;-webkit-transform:rotate(3deg) translate(0px,-4px);-ms-transform:rotate(3deg) translate(0px,-4px);transform:rotate(3deg) translate(0px,-4px)}#nprogress .spinner{display:block;position:fixed;z-index:${S};${H ? \"bottom: 15px;\" : \"top: 15px;\"}right:15px}#nprogress .spinner-icon{width:18px;height:18px;box-sizing:border-box;border:2px solid transparent;border-top-color:${m};border-left-color:${m};border-radius:50%;-webkit-animation:nprogress-spinner 400ms linear infinite;animation:nprogress-spinner 400ms linear infinite}.nprogress-custom-parent{overflow:hidden;position:relative}.nprogress-custom-parent #nprogress .bar,.nprogress-custom-parent #nprogress .spinner{position:absolute}@-webkit-keyframes nprogress-spinner{0%{-webkit-transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg)}}@keyframes nprogress-spinner{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}`), u = a((h)=>new URL(h, window.location.href).href, \"toAbsoluteURL\"), W = a((h, f)=>{\n        let c = new URL(u(h)), b = new URL(u(f));\n        return c.href.split(\"#\")[0] === b.href.split(\"#\")[0];\n    }, \"isHashAnchor\"), j = a((h, f)=>{\n        let c = new URL(u(h)), b = new URL(u(f));\n        return c.hostname.replace(/^www\\./, \"\") === b.hostname.replace(/^www\\./, \"\");\n    }, \"isSameHostName\");\n    return v.useEffect({\n        \"M.useEffect\": ()=>{\n            s.configure({\n                showSpinner: i != null ? i : !0,\n                trickle: g != null ? g : !0,\n                trickleSpeed: l != null ? l : 200,\n                minimum: L != null ? L : .08,\n                easing: T != null ? T : \"ease\",\n                speed: E != null ? E : 200,\n                template: k != null ? k : '<div class=\"bar\" role=\"bar\"><div class=\"peg\"></div></div><div class=\"spinner\" role=\"spinner\"><div class=\"spinner-icon\"></div></div>'\n            });\n            function h(e, d) {\n                let n = new URL(e), p = new URL(d);\n                if (n.hostname === p.hostname && n.pathname === p.pathname && n.search === p.search) {\n                    let w = n.hash, P = p.hash;\n                    return w !== P && n.href.replace(w, \"\") === p.href.replace(P, \"\");\n                }\n                return !1;\n            }\n            a(h, \"isAnchorOfCurrentUrl\");\n            var f = document.querySelectorAll(\"html\");\n            let c = a({\n                \"M.useEffect.c\": ()=>f.forEach({\n                        \"M.useEffect.c\": (e)=>e.classList.remove(\"nprogress-busy\")\n                    }[\"M.useEffect.c\"])\n            }[\"M.useEffect.c\"], \"removeNProgressClass\");\n            function b(e) {\n                for(; e && e.tagName.toLowerCase() !== \"a\";)e = e.parentElement;\n                return e;\n            }\n            a(b, \"findClosestAnchor\");\n            function U(e) {\n                try {\n                    let d = e.target, n = b(d), p = n == null ? void 0 : n.href;\n                    if (p) {\n                        let w = window.location.href, P = n.target === \"_blank\", B = [\n                            \"tel:\",\n                            \"mailto:\",\n                            \"sms:\",\n                            \"blob:\",\n                            \"download:\"\n                        ].some({\n                            \"M.useEffect.U.B\": (F)=>p.startsWith(F)\n                        }[\"M.useEffect.U.B\"]), q = h(w, p);\n                        if (!j(window.location.href, n.href)) return;\n                        p === w || q || P || B || e.ctrlKey || e.metaKey || e.shiftKey || e.altKey || W(window.location.href, n.href) || !u(n.href).startsWith(\"http\") ? (s.start(), s.done(), c()) : s.start();\n                    }\n                } catch (d) {\n                    s.start(), s.done();\n                }\n            }\n            a(U, \"handleClick\"), ({\n                \"M.useEffect\": (e)=>{\n                    let d = e.pushState;\n                    e.pushState = ({\n                        \"M.useEffect\": (...n)=>(s.done(), c(), d.apply(e, n))\n                    })[\"M.useEffect\"];\n                }\n            })[\"M.useEffect\"](window.history), ({\n                \"M.useEffect\": (e)=>{\n                    let d = e.replaceState;\n                    e.replaceState = ({\n                        \"M.useEffect\": (...n)=>(s.done(), c(), d.apply(e, n))\n                    })[\"M.useEffect\"];\n                }\n            })[\"M.useEffect\"](window.history);\n            function R() {\n                s.done(), c();\n            }\n            a(R, \"handlePageHide\");\n            function $() {\n                s.done();\n            }\n            return a($, \"handleBackAndForth\"), window.addEventListener(\"popstate\", $), document.addEventListener(\"click\", U), window.addEventListener(\"pagehide\", R), ({\n                \"M.useEffect\": ()=>{\n                    document.removeEventListener(\"click\", U), window.removeEventListener(\"pagehide\", R), window.removeEventListener(\"popstate\", $);\n                }\n            })[\"M.useEffect\"];\n        }\n    }[\"M.useEffect\"], []), K;\n}, \"NextTopLoader\"), V = M;\nM.propTypes = {\n    color: t.string,\n    height: t.number,\n    showSpinner: t.bool,\n    crawl: t.bool,\n    crawlSpeed: t.number,\n    initialPosition: t.number,\n    easing: t.string,\n    speed: t.number,\n    template: t.string,\n    shadow: t.oneOfType([\n        t.string,\n        t.bool\n    ]),\n    zIndex: t.number,\n    showAtBottom: t.bool\n}; //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/nextjs-toploader/dist/index.js\n");

/***/ })

};
;