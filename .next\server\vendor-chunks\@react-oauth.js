"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-oauth";
exports.ids = ["vendor-chunks/@react-oauth"];
exports.modules = {

/***/ "(rsc)/./node_modules/@react-oauth/google/dist/index.esm.js":
/*!************************************************************!*\
  !*** ./node_modules/@react-oauth/google/dist/index.esm.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GoogleLogin: () => (/* binding */ GoogleLogin),\n/* harmony export */   GoogleOAuthProvider: () => (/* binding */ GoogleOAuthProvider),\n/* harmony export */   googleLogout: () => (/* binding */ googleLogout),\n/* harmony export */   hasGrantedAllScopesGoogle: () => (/* binding */ hasGrantedAllScopesGoogle),\n/* harmony export */   hasGrantedAnyScopeGoogle: () => (/* binding */ hasGrantedAnyScopeGoogle),\n/* harmony export */   useGoogleLogin: () => (/* binding */ useGoogleLogin),\n/* harmony export */   useGoogleOAuth: () => (/* binding */ useGoogleOAuth),\n/* harmony export */   useGoogleOneTapLogin: () => (/* binding */ useGoogleOneTapLogin)\n/* harmony export */ });\n/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js\");\n/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);\n// This file is generated by the Webpack next-flight-loader.\n\nconst GoogleLogin = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call GoogleLogin() from the server but GoogleLogin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\node_modules\\\\@react-oauth\\\\google\\\\dist\\\\index.esm.js\",\n\"GoogleLogin\",\n);const GoogleOAuthProvider = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call GoogleOAuthProvider() from the server but GoogleOAuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\node_modules\\\\@react-oauth\\\\google\\\\dist\\\\index.esm.js\",\n\"GoogleOAuthProvider\",\n);const googleLogout = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call googleLogout() from the server but googleLogout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\node_modules\\\\@react-oauth\\\\google\\\\dist\\\\index.esm.js\",\n\"googleLogout\",\n);const hasGrantedAllScopesGoogle = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call hasGrantedAllScopesGoogle() from the server but hasGrantedAllScopesGoogle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\node_modules\\\\@react-oauth\\\\google\\\\dist\\\\index.esm.js\",\n\"hasGrantedAllScopesGoogle\",\n);const hasGrantedAnyScopeGoogle = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call hasGrantedAnyScopeGoogle() from the server but hasGrantedAnyScopeGoogle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\node_modules\\\\@react-oauth\\\\google\\\\dist\\\\index.esm.js\",\n\"hasGrantedAnyScopeGoogle\",\n);const useGoogleLogin = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call useGoogleLogin() from the server but useGoogleLogin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\node_modules\\\\@react-oauth\\\\google\\\\dist\\\\index.esm.js\",\n\"useGoogleLogin\",\n);const useGoogleOAuth = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call useGoogleOAuth() from the server but useGoogleOAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\node_modules\\\\@react-oauth\\\\google\\\\dist\\\\index.esm.js\",\n\"useGoogleOAuth\",\n);const useGoogleOneTapLogin = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call useGoogleOneTapLogin() from the server but useGoogleOneTapLogin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\node_modules\\\\@react-oauth\\\\google\\\\dist\\\\index.esm.js\",\n\"useGoogleOneTapLogin\",\n);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@react-oauth/google/dist/index.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-oauth/google/dist/index.esm.js":
/*!************************************************************!*\
  !*** ./node_modules/@react-oauth/google/dist/index.esm.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GoogleLogin: () => (/* binding */ GoogleLogin),\n/* harmony export */   GoogleOAuthProvider: () => (/* binding */ GoogleOAuthProvider),\n/* harmony export */   googleLogout: () => (/* binding */ googleLogout),\n/* harmony export */   hasGrantedAllScopesGoogle: () => (/* binding */ hasGrantedAllScopesGoogle),\n/* harmony export */   hasGrantedAnyScopeGoogle: () => (/* binding */ hasGrantedAnyScopeGoogle),\n/* harmony export */   useGoogleLogin: () => (/* binding */ useGoogleLogin),\n/* harmony export */   useGoogleOAuth: () => (/* binding */ useGoogleOAuth),\n/* harmony export */   useGoogleOneTapLogin: () => (/* binding */ useGoogleOneTapLogin)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ GoogleLogin,GoogleOAuthProvider,googleLogout,hasGrantedAllScopesGoogle,hasGrantedAnyScopeGoogle,useGoogleLogin,useGoogleOAuth,useGoogleOneTapLogin auto */ \nfunction useLoadGsiScript(options = {}) {\n    const { nonce, onScriptLoadSuccess, onScriptLoadError } = options;\n    const [scriptLoadedSuccessfully, setScriptLoadedSuccessfully] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const onScriptLoadSuccessRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(onScriptLoadSuccess);\n    onScriptLoadSuccessRef.current = onScriptLoadSuccess;\n    const onScriptLoadErrorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(onScriptLoadError);\n    onScriptLoadErrorRef.current = onScriptLoadError;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useLoadGsiScript.useEffect\": ()=>{\n            const scriptTag = document.createElement('script');\n            scriptTag.src = 'https://accounts.google.com/gsi/client';\n            scriptTag.async = true;\n            scriptTag.defer = true;\n            scriptTag.nonce = nonce;\n            scriptTag.onload = ({\n                \"useLoadGsiScript.useEffect\": ()=>{\n                    var _a;\n                    setScriptLoadedSuccessfully(true);\n                    (_a = onScriptLoadSuccessRef.current) === null || _a === void 0 ? void 0 : _a.call(onScriptLoadSuccessRef);\n                }\n            })[\"useLoadGsiScript.useEffect\"];\n            scriptTag.onerror = ({\n                \"useLoadGsiScript.useEffect\": ()=>{\n                    var _a;\n                    setScriptLoadedSuccessfully(false);\n                    (_a = onScriptLoadErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onScriptLoadErrorRef);\n                }\n            })[\"useLoadGsiScript.useEffect\"];\n            document.body.appendChild(scriptTag);\n            return ({\n                \"useLoadGsiScript.useEffect\": ()=>{\n                    document.body.removeChild(scriptTag);\n                }\n            })[\"useLoadGsiScript.useEffect\"];\n        }\n    }[\"useLoadGsiScript.useEffect\"], [\n        nonce\n    ]);\n    return scriptLoadedSuccessfully;\n}\nconst GoogleOAuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\nfunction GoogleOAuthProvider({ clientId, nonce, onScriptLoadSuccess, onScriptLoadError, children }) {\n    const scriptLoadedSuccessfully = useLoadGsiScript({\n        nonce,\n        onScriptLoadSuccess,\n        onScriptLoadError\n    });\n    const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"GoogleOAuthProvider.useMemo[contextValue]\": ()=>({\n                clientId,\n                scriptLoadedSuccessfully\n            })\n    }[\"GoogleOAuthProvider.useMemo[contextValue]\"], [\n        clientId,\n        scriptLoadedSuccessfully\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(GoogleOAuthContext.Provider, {\n        value: contextValue\n    }, children);\n}\nfunction useGoogleOAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(GoogleOAuthContext);\n    if (!context) {\n        throw new Error('Google OAuth components must be used within GoogleOAuthProvider');\n    }\n    return context;\n}\nfunction extractClientId(credentialResponse) {\n    var _a;\n    const clientId = (_a = credentialResponse === null || credentialResponse === void 0 ? void 0 : credentialResponse.clientId) !== null && _a !== void 0 ? _a : credentialResponse === null || credentialResponse === void 0 ? void 0 : credentialResponse.client_id;\n    return clientId;\n}\nconst containerHeightMap = {\n    large: 40,\n    medium: 32,\n    small: 20\n};\nfunction GoogleLogin({ onSuccess, onError, useOneTap, promptMomentNotification, type = 'standard', theme = 'outline', size = 'large', text, shape, logo_alignment, width, locale, click_listener, containerProps, ...props }) {\n    const btnContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { clientId, scriptLoadedSuccessfully } = useGoogleOAuth();\n    const onSuccessRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(onSuccess);\n    onSuccessRef.current = onSuccess;\n    const onErrorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(onError);\n    onErrorRef.current = onError;\n    const promptMomentNotificationRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(promptMomentNotification);\n    promptMomentNotificationRef.current = promptMomentNotification;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"GoogleLogin.useEffect\": ()=>{\n            var _a, _b, _c, _d, _e, _f, _g, _h, _j;\n            if (!scriptLoadedSuccessfully) return;\n            (_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.id) === null || _c === void 0 ? void 0 : _c.initialize({\n                client_id: clientId,\n                callback: {\n                    \"GoogleLogin.useEffect\": (credentialResponse)=>{\n                        var _a;\n                        if (!(credentialResponse === null || credentialResponse === void 0 ? void 0 : credentialResponse.credential)) {\n                            return (_a = onErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onErrorRef);\n                        }\n                        const { credential, select_by } = credentialResponse;\n                        onSuccessRef.current({\n                            credential,\n                            clientId: extractClientId(credentialResponse),\n                            select_by\n                        });\n                    }\n                }[\"GoogleLogin.useEffect\"],\n                ...props\n            });\n            (_f = (_e = (_d = window === null || window === void 0 ? void 0 : window.google) === null || _d === void 0 ? void 0 : _d.accounts) === null || _e === void 0 ? void 0 : _e.id) === null || _f === void 0 ? void 0 : _f.renderButton(btnContainerRef.current, {\n                type,\n                theme,\n                size,\n                text,\n                shape,\n                logo_alignment,\n                width,\n                locale,\n                click_listener\n            });\n            if (useOneTap) (_j = (_h = (_g = window === null || window === void 0 ? void 0 : window.google) === null || _g === void 0 ? void 0 : _g.accounts) === null || _h === void 0 ? void 0 : _h.id) === null || _j === void 0 ? void 0 : _j.prompt(promptMomentNotificationRef.current);\n            return ({\n                \"GoogleLogin.useEffect\": ()=>{\n                    var _a, _b, _c;\n                    if (useOneTap) (_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.id) === null || _c === void 0 ? void 0 : _c.cancel();\n                }\n            })[\"GoogleLogin.useEffect\"];\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"GoogleLogin.useEffect\"], [\n        clientId,\n        scriptLoadedSuccessfully,\n        useOneTap,\n        type,\n        theme,\n        size,\n        text,\n        shape,\n        logo_alignment,\n        width,\n        locale\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n        ...containerProps,\n        ref: btnContainerRef,\n        style: {\n            height: containerHeightMap[size],\n            ...containerProps === null || containerProps === void 0 ? void 0 : containerProps.style\n        }\n    });\n}\nfunction googleLogout() {\n    var _a, _b, _c;\n    (_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.id) === null || _c === void 0 ? void 0 : _c.disableAutoSelect();\n}\n/* eslint-disable import/export */ function useGoogleLogin({ flow = 'implicit', scope = '', onSuccess, onError, onNonOAuthError, overrideScope, state, ...props }) {\n    const { clientId, scriptLoadedSuccessfully } = useGoogleOAuth();\n    const clientRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    const onSuccessRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(onSuccess);\n    onSuccessRef.current = onSuccess;\n    const onErrorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(onError);\n    onErrorRef.current = onError;\n    const onNonOAuthErrorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(onNonOAuthError);\n    onNonOAuthErrorRef.current = onNonOAuthError;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useGoogleLogin.useEffect\": ()=>{\n            var _a, _b;\n            if (!scriptLoadedSuccessfully) return;\n            const clientMethod = flow === 'implicit' ? 'initTokenClient' : 'initCodeClient';\n            const client = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.oauth2[clientMethod]({\n                client_id: clientId,\n                scope: overrideScope ? scope : `openid profile email ${scope}`,\n                callback: {\n                    \"useGoogleLogin.useEffect\": (response)=>{\n                        var _a, _b;\n                        if (response.error) return (_a = onErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onErrorRef, response);\n                        (_b = onSuccessRef.current) === null || _b === void 0 ? void 0 : _b.call(onSuccessRef, response);\n                    }\n                }[\"useGoogleLogin.useEffect\"],\n                error_callback: {\n                    \"useGoogleLogin.useEffect\": (nonOAuthError)=>{\n                        var _a;\n                        (_a = onNonOAuthErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onNonOAuthErrorRef, nonOAuthError);\n                    }\n                }[\"useGoogleLogin.useEffect\"],\n                state,\n                ...props\n            });\n            clientRef.current = client;\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"useGoogleLogin.useEffect\"], [\n        clientId,\n        scriptLoadedSuccessfully,\n        flow,\n        scope,\n        state\n    ]);\n    const loginImplicitFlow = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useGoogleLogin.useCallback[loginImplicitFlow]\": (overrideConfig)=>{\n            var _a;\n            return (_a = clientRef.current) === null || _a === void 0 ? void 0 : _a.requestAccessToken(overrideConfig);\n        }\n    }[\"useGoogleLogin.useCallback[loginImplicitFlow]\"], []);\n    const loginAuthCodeFlow = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useGoogleLogin.useCallback[loginAuthCodeFlow]\": ()=>{\n            var _a;\n            return (_a = clientRef.current) === null || _a === void 0 ? void 0 : _a.requestCode();\n        }\n    }[\"useGoogleLogin.useCallback[loginAuthCodeFlow]\"], []);\n    return flow === 'implicit' ? loginImplicitFlow : loginAuthCodeFlow;\n}\nfunction useGoogleOneTapLogin({ onSuccess, onError, promptMomentNotification, cancel_on_tap_outside, prompt_parent_id, state_cookie_domain, hosted_domain, use_fedcm_for_prompt = false, use_fedcm_for_button = false, disabled, auto_select }) {\n    const { clientId, scriptLoadedSuccessfully } = useGoogleOAuth();\n    const onSuccessRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(onSuccess);\n    onSuccessRef.current = onSuccess;\n    const onErrorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(onError);\n    onErrorRef.current = onError;\n    const promptMomentNotificationRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(promptMomentNotification);\n    promptMomentNotificationRef.current = promptMomentNotification;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useGoogleOneTapLogin.useEffect\": ()=>{\n            var _a, _b, _c, _d, _e, _f, _g, _h, _j;\n            if (!scriptLoadedSuccessfully) return;\n            if (disabled) {\n                (_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.id) === null || _c === void 0 ? void 0 : _c.cancel();\n                return;\n            }\n            (_f = (_e = (_d = window === null || window === void 0 ? void 0 : window.google) === null || _d === void 0 ? void 0 : _d.accounts) === null || _e === void 0 ? void 0 : _e.id) === null || _f === void 0 ? void 0 : _f.initialize({\n                client_id: clientId,\n                callback: {\n                    \"useGoogleOneTapLogin.useEffect\": (credentialResponse)=>{\n                        var _a;\n                        if (!(credentialResponse === null || credentialResponse === void 0 ? void 0 : credentialResponse.credential)) {\n                            return (_a = onErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onErrorRef);\n                        }\n                        const { credential, select_by } = credentialResponse;\n                        onSuccessRef.current({\n                            credential,\n                            clientId: extractClientId(credentialResponse),\n                            select_by\n                        });\n                    }\n                }[\"useGoogleOneTapLogin.useEffect\"],\n                hosted_domain,\n                cancel_on_tap_outside,\n                prompt_parent_id,\n                state_cookie_domain,\n                use_fedcm_for_prompt,\n                use_fedcm_for_button,\n                auto_select\n            });\n            (_j = (_h = (_g = window === null || window === void 0 ? void 0 : window.google) === null || _g === void 0 ? void 0 : _g.accounts) === null || _h === void 0 ? void 0 : _h.id) === null || _j === void 0 ? void 0 : _j.prompt(promptMomentNotificationRef.current);\n            return ({\n                \"useGoogleOneTapLogin.useEffect\": ()=>{\n                    var _a, _b, _c;\n                    (_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.id) === null || _c === void 0 ? void 0 : _c.cancel();\n                }\n            })[\"useGoogleOneTapLogin.useEffect\"];\n        }\n    }[\"useGoogleOneTapLogin.useEffect\"], [\n        clientId,\n        scriptLoadedSuccessfully,\n        cancel_on_tap_outside,\n        prompt_parent_id,\n        state_cookie_domain,\n        hosted_domain,\n        use_fedcm_for_prompt,\n        use_fedcm_for_button,\n        disabled,\n        auto_select\n    ]);\n}\n/**\r\n * Checks if the user granted all the specified scope or scopes\r\n * @returns True if all the scopes are granted\r\n */ function hasGrantedAllScopesGoogle(tokenResponse, firstScope, ...restScopes) {\n    var _a, _b, _c;\n    if (!(window === null || window === void 0 ? void 0 : window.google)) return false;\n    return ((_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.oauth2) === null || _c === void 0 ? void 0 : _c.hasGrantedAllScopes(tokenResponse, firstScope, ...restScopes)) || false;\n}\n/**\r\n * Checks if the user granted any of the specified scope or scopes.\r\n * @returns True if any of the scopes are granted\r\n */ function hasGrantedAnyScopeGoogle(tokenResponse, firstScope, ...restScopes) {\n    var _a, _b, _c;\n    if (!(window === null || window === void 0 ? void 0 : window.google)) return false;\n    return ((_c = (_b = (_a = window === null || window === void 0 ? void 0 : window.google) === null || _a === void 0 ? void 0 : _a.accounts) === null || _b === void 0 ? void 0 : _b.oauth2) === null || _c === void 0 ? void 0 : _c.hasGrantedAnyScope(tokenResponse, firstScope, ...restScopes)) || false;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-oauth/google/dist/index.esm.js\n");

/***/ })

};
;