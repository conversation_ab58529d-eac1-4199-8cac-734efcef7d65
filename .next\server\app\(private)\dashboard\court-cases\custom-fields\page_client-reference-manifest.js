globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/(private)/dashboard/court-cases/custom-fields/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/@react-oauth/google/dist/index.esm.js":{"*":{"id":"(ssr)/./node_modules/@react-oauth/google/dist/index.esm.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/nextjs-toploader/dist/index.js":{"*":{"id":"(ssr)/./node_modules/nextjs-toploader/dist/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/react-toastify/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/react-toastify/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/app-provider.tsx":{"*":{"id":"(ssr)/./src/app/app-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/DynamicTitle.tsx":{"*":{"id":"(ssr)/./src/components/DynamicTitle.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Footer.tsx":{"*":{"id":"(ssr)/./src/components/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Header.tsx":{"*":{"id":"(ssr)/./src/components/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Security/SafeStyleOverride.tsx":{"*":{"id":"(ssr)/./src/components/Security/SafeStyleOverride.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/context/SettingContext.tsx":{"*":{"id":"(ssr)/./src/context/SettingContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/error.tsx":{"*":{"id":"(ssr)/./src/app/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Layout/DynamicLayout.tsx":{"*":{"id":"(ssr)/./src/components/Layout/DynamicLayout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Navigation/SecretHeader.tsx":{"*":{"id":"(ssr)/./src/components/Navigation/SecretHeader.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/SideMenu.tsx":{"*":{"id":"(ssr)/./src/components/SideMenu.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/context/SidebarContext.tsx":{"*":{"id":"(ssr)/./src/context/SidebarContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(private)/dashboard/court-cases/page.tsx":{"*":{"id":"(ssr)/./src/app/(private)/dashboard/court-cases/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/framework/boundary-components.js":{"*":{"id":"(ssr)/./node_modules/next/dist/lib/framework/boundary-components.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(private)/dashboard/court-cases/custom-fields/page.tsx":{"*":{"id":"(ssr)/./src/app/(private)/dashboard/court-cases/custom-fields/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\@react-oauth\\google\\dist\\index.esm.js":{"id":"(app-pages-browser)/./node_modules/@react-oauth/google/dist/index.esm.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Libre_Baskerville\",\"arguments\":[{\"subsets\":[\"latin\"],\"style\":[\"normal\",\"italic\"],\"weight\":[\"400\",\"700\"],\"variable\":\"--libre-baskerville\",\"display\":\"swap\"}],\"variableName\":\"libre_baskerville\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Libre_Baskerville\",\"arguments\":[{\"subsets\":[\"latin\"],\"style\":[\"normal\",\"italic\"],\"weight\":[\"400\",\"700\"],\"variable\":\"--libre-baskerville\",\"display\":\"swap\"}],\"variableName\":\"libre_baskerville\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\nextjs-toploader\\dist\\index.js":{"id":"(app-pages-browser)/./node_modules/nextjs-toploader/dist/index.js","name":"*","chunks":["app/(private)/layout","static/chunks/app/(private)/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\react-toastify\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/react-toastify/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\react-toastify\\dist\\ReactToastify.css":{"id":"(app-pages-browser)/./node_modules/react-toastify/dist/ReactToastify.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\app-provider.tsx":{"id":"(app-pages-browser)/./src/app/app-provider.tsx","name":"*","chunks":["app/(private)/layout","static/chunks/app/(private)/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\components\\DynamicTitle.tsx":{"id":"(app-pages-browser)/./src/components/DynamicTitle.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\components\\Footer.tsx":{"id":"(app-pages-browser)/./src/components/Footer.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\components\\Header.tsx":{"id":"(app-pages-browser)/./src/components/Header.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\components\\Security\\SafeStyleOverride.tsx":{"id":"(app-pages-browser)/./src/components/Security/SafeStyleOverride.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\context\\SettingContext.tsx":{"id":"(app-pages-browser)/./src/context/SettingContext.tsx","name":"*","chunks":["app/(private)/layout","static/chunks/app/(private)/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\styles\\global.css":{"id":"(app-pages-browser)/./src/styles/global.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\error.tsx":{"id":"(app-pages-browser)/./src/app/error.tsx","name":"*","chunks":["app/error","static/chunks/app/error.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\components\\Layout\\DynamicLayout.tsx":{"id":"(app-pages-browser)/./src/components/Layout/DynamicLayout.tsx","name":"*","chunks":["app/(private)/layout","static/chunks/app/(private)/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\components\\Navigation\\SecretHeader.tsx":{"id":"(app-pages-browser)/./src/components/Navigation/SecretHeader.tsx","name":"*","chunks":["app/(private)/layout","static/chunks/app/(private)/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\components\\SideMenu.tsx":{"id":"(app-pages-browser)/./src/components/SideMenu.tsx","name":"*","chunks":["app/(private)/layout","static/chunks/app/(private)/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\context\\SidebarContext.tsx":{"id":"(app-pages-browser)/./src/context/SidebarContext.tsx","name":"*","chunks":["app/(private)/layout","static/chunks/app/(private)/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\court-cases\\page.tsx":{"id":"(app-pages-browser)/./src/app/(private)/dashboard/court-cases/page.tsx","name":"*","chunks":["app/(private)/dashboard/court-cases/page","static/chunks/app/(private)/dashboard/court-cases/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\next\\dist\\client\\components\\builtin\\global-error.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\next\\dist\\esm\\client\\components\\builtin\\global-error.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\next\\dist\\lib\\framework\\boundary-components.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/framework/boundary-components.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\next\\dist\\esm\\lib\\framework\\boundary-components.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/framework/boundary-components.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\next\\dist\\lib\\metadata\\generate\\icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\next\\dist\\esm\\lib\\metadata\\generate\\icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\next\\dist\\next-devtools\\userspace\\app\\segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\node_modules\\next\\dist\\esm\\next-devtools\\userspace\\app\\segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\court-cases\\custom-fields\\page.tsx":{"id":"(app-pages-browser)/./src/app/(private)/dashboard/court-cases/custom-fields/page.tsx","name":"*","chunks":["app/(private)/dashboard/court-cases/custom-fields/page","static/chunks/app/(private)/dashboard/court-cases/custom-fields/page.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\":[],"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\error":[],"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\not-found":[],"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\layout":[],"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\court-cases\\page":[],"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\court-cases\\custom-fields\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./node_modules/@react-oauth/google/dist/index.esm.js":{"*":{"id":"(rsc)/./node_modules/@react-oauth/google/dist/index.esm.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/nextjs-toploader/dist/index.js":{"*":{"id":"(rsc)/./node_modules/nextjs-toploader/dist/index.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/react-toastify/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/react-toastify/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/react-toastify/dist/ReactToastify.css":{"*":{"id":"(rsc)/./node_modules/react-toastify/dist/ReactToastify.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/app-provider.tsx":{"*":{"id":"(rsc)/./src/app/app-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/DynamicTitle.tsx":{"*":{"id":"(rsc)/./src/components/DynamicTitle.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Footer.tsx":{"*":{"id":"(rsc)/./src/components/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Header.tsx":{"*":{"id":"(rsc)/./src/components/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Security/SafeStyleOverride.tsx":{"*":{"id":"(rsc)/./src/components/Security/SafeStyleOverride.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/context/SettingContext.tsx":{"*":{"id":"(rsc)/./src/context/SettingContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/styles/global.css":{"*":{"id":"(rsc)/./src/styles/global.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/error.tsx":{"*":{"id":"(rsc)/./src/app/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Layout/DynamicLayout.tsx":{"*":{"id":"(rsc)/./src/components/Layout/DynamicLayout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Navigation/SecretHeader.tsx":{"*":{"id":"(rsc)/./src/components/Navigation/SecretHeader.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/SideMenu.tsx":{"*":{"id":"(rsc)/./src/components/SideMenu.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/context/SidebarContext.tsx":{"*":{"id":"(rsc)/./src/context/SidebarContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(private)/dashboard/court-cases/page.tsx":{"*":{"id":"(rsc)/./src/app/(private)/dashboard/court-cases/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/framework/boundary-components.js":{"*":{"id":"(rsc)/./node_modules/next/dist/lib/framework/boundary-components.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(private)/dashboard/court-cases/custom-fields/page.tsx":{"*":{"id":"(rsc)/./src/app/(private)/dashboard/court-cases/custom-fields/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}