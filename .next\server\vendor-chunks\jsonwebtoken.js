/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jsonwebtoken";
exports.ids = ["vendor-chunks/jsonwebtoken"];
exports.modules = {

/***/ "(ssr)/./node_modules/jsonwebtoken/decode.js":
/*!*********************************************!*\
  !*** ./node_modules/jsonwebtoken/decode.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var jws = __webpack_require__(/*! jws */ \"(ssr)/./node_modules/jws/index.js\");\n\nmodule.exports = function (jwt, options) {\n  options = options || {};\n  var decoded = jws.decode(jwt, options);\n  if (!decoded) { return null; }\n  var payload = decoded.payload;\n\n  //try parse the payload\n  if(typeof payload === 'string') {\n    try {\n      var obj = JSON.parse(payload);\n      if(obj !== null && typeof obj === 'object') {\n        payload = obj;\n      }\n    } catch (e) { }\n  }\n\n  //return header if `complete` option is enabled.  header includes claims\n  //such as `kid` and `alg` used to select the key within a JWKS needed to\n  //verify the signature\n  if (options.complete === true) {\n    return {\n      header: decoded.header,\n      payload: payload,\n      signature: decoded.signature\n    };\n  }\n  return payload;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2RlY29kZS5qcyIsIm1hcHBpbmdzIjoiQUFBQSxVQUFVLG1CQUFPLENBQUMsOENBQUs7O0FBRXZCO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWRtaW5cXERlc2t0b3BcXGJsb2dcXHRhbmRwcm9cXG5vZGVfbW9kdWxlc1xcanNvbndlYnRva2VuXFxkZWNvZGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGp3cyA9IHJlcXVpcmUoJ2p3cycpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uIChqd3QsIG9wdGlvbnMpIHtcbiAgb3B0aW9ucyA9IG9wdGlvbnMgfHwge307XG4gIHZhciBkZWNvZGVkID0gandzLmRlY29kZShqd3QsIG9wdGlvbnMpO1xuICBpZiAoIWRlY29kZWQpIHsgcmV0dXJuIG51bGw7IH1cbiAgdmFyIHBheWxvYWQgPSBkZWNvZGVkLnBheWxvYWQ7XG5cbiAgLy90cnkgcGFyc2UgdGhlIHBheWxvYWRcbiAgaWYodHlwZW9mIHBheWxvYWQgPT09ICdzdHJpbmcnKSB7XG4gICAgdHJ5IHtcbiAgICAgIHZhciBvYmogPSBKU09OLnBhcnNlKHBheWxvYWQpO1xuICAgICAgaWYob2JqICE9PSBudWxsICYmIHR5cGVvZiBvYmogPT09ICdvYmplY3QnKSB7XG4gICAgICAgIHBheWxvYWQgPSBvYmo7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZSkgeyB9XG4gIH1cblxuICAvL3JldHVybiBoZWFkZXIgaWYgYGNvbXBsZXRlYCBvcHRpb24gaXMgZW5hYmxlZC4gIGhlYWRlciBpbmNsdWRlcyBjbGFpbXNcbiAgLy9zdWNoIGFzIGBraWRgIGFuZCBgYWxnYCB1c2VkIHRvIHNlbGVjdCB0aGUga2V5IHdpdGhpbiBhIEpXS1MgbmVlZGVkIHRvXG4gIC8vdmVyaWZ5IHRoZSBzaWduYXR1cmVcbiAgaWYgKG9wdGlvbnMuY29tcGxldGUgPT09IHRydWUpIHtcbiAgICByZXR1cm4ge1xuICAgICAgaGVhZGVyOiBkZWNvZGVkLmhlYWRlcixcbiAgICAgIHBheWxvYWQ6IHBheWxvYWQsXG4gICAgICBzaWduYXR1cmU6IGRlY29kZWQuc2lnbmF0dXJlXG4gICAgfTtcbiAgfVxuICByZXR1cm4gcGF5bG9hZDtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonwebtoken/decode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonwebtoken/index.js":
/*!********************************************!*\
  !*** ./node_modules/jsonwebtoken/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = {\n  decode: __webpack_require__(/*! ./decode */ \"(ssr)/./node_modules/jsonwebtoken/decode.js\"),\n  verify: __webpack_require__(/*! ./verify */ \"(ssr)/./node_modules/jsonwebtoken/verify.js\"),\n  sign: __webpack_require__(/*! ./sign */ \"(ssr)/./node_modules/jsonwebtoken/sign.js\"),\n  JsonWebTokenError: __webpack_require__(/*! ./lib/JsonWebTokenError */ \"(ssr)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\"),\n  NotBeforeError: __webpack_require__(/*! ./lib/NotBeforeError */ \"(ssr)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\"),\n  TokenExpiredError: __webpack_require__(/*! ./lib/TokenExpiredError */ \"(ssr)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\"),\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0EsVUFBVSxtQkFBTyxDQUFDLDZEQUFVO0FBQzVCLFVBQVUsbUJBQU8sQ0FBQyw2REFBVTtBQUM1QixRQUFRLG1CQUFPLENBQUMseURBQVE7QUFDeEIscUJBQXFCLG1CQUFPLENBQUMsMkZBQXlCO0FBQ3RELGtCQUFrQixtQkFBTyxDQUFDLHFGQUFzQjtBQUNoRCxxQkFBcUIsbUJBQU8sQ0FBQywyRkFBeUI7QUFDdEQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWRtaW5cXERlc2t0b3BcXGJsb2dcXHRhbmRwcm9cXG5vZGVfbW9kdWxlc1xcanNvbndlYnRva2VuXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IHtcbiAgZGVjb2RlOiByZXF1aXJlKCcuL2RlY29kZScpLFxuICB2ZXJpZnk6IHJlcXVpcmUoJy4vdmVyaWZ5JyksXG4gIHNpZ246IHJlcXVpcmUoJy4vc2lnbicpLFxuICBKc29uV2ViVG9rZW5FcnJvcjogcmVxdWlyZSgnLi9saWIvSnNvbldlYlRva2VuRXJyb3InKSxcbiAgTm90QmVmb3JlRXJyb3I6IHJlcXVpcmUoJy4vbGliL05vdEJlZm9yZUVycm9yJyksXG4gIFRva2VuRXhwaXJlZEVycm9yOiByZXF1aXJlKCcuL2xpYi9Ub2tlbkV4cGlyZWRFcnJvcicpLFxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonwebtoken/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js":
/*!************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/JsonWebTokenError.js ***!
  \************************************************************/
/***/ ((module) => {

eval("var JsonWebTokenError = function (message, error) {\n  Error.call(this, message);\n  if(Error.captureStackTrace) {\n    Error.captureStackTrace(this, this.constructor);\n  }\n  this.name = 'JsonWebTokenError';\n  this.message = message;\n  if (error) this.inner = error;\n};\n\nJsonWebTokenError.prototype = Object.create(Error.prototype);\nJsonWebTokenError.prototype.constructor = JsonWebTokenError;\n\nmodule.exports = JsonWebTokenError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Kc29uV2ViVG9rZW5FcnJvci5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBZG1pblxcRGVza3RvcFxcYmxvZ1xcdGFuZHByb1xcbm9kZV9tb2R1bGVzXFxqc29ud2VidG9rZW5cXGxpYlxcSnNvbldlYlRva2VuRXJyb3IuanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIEpzb25XZWJUb2tlbkVycm9yID0gZnVuY3Rpb24gKG1lc3NhZ2UsIGVycm9yKSB7XG4gIEVycm9yLmNhbGwodGhpcywgbWVzc2FnZSk7XG4gIGlmKEVycm9yLmNhcHR1cmVTdGFja1RyYWNlKSB7XG4gICAgRXJyb3IuY2FwdHVyZVN0YWNrVHJhY2UodGhpcywgdGhpcy5jb25zdHJ1Y3Rvcik7XG4gIH1cbiAgdGhpcy5uYW1lID0gJ0pzb25XZWJUb2tlbkVycm9yJztcbiAgdGhpcy5tZXNzYWdlID0gbWVzc2FnZTtcbiAgaWYgKGVycm9yKSB0aGlzLmlubmVyID0gZXJyb3I7XG59O1xuXG5Kc29uV2ViVG9rZW5FcnJvci5wcm90b3R5cGUgPSBPYmplY3QuY3JlYXRlKEVycm9yLnByb3RvdHlwZSk7XG5Kc29uV2ViVG9rZW5FcnJvci5wcm90b3R5cGUuY29uc3RydWN0b3IgPSBKc29uV2ViVG9rZW5FcnJvcjtcblxubW9kdWxlLmV4cG9ydHMgPSBKc29uV2ViVG9rZW5FcnJvcjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonwebtoken/lib/NotBeforeError.js":
/*!*********************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/NotBeforeError.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var JsonWebTokenError = __webpack_require__(/*! ./JsonWebTokenError */ \"(ssr)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\n\nvar NotBeforeError = function (message, date) {\n  JsonWebTokenError.call(this, message);\n  this.name = 'NotBeforeError';\n  this.date = date;\n};\n\nNotBeforeError.prototype = Object.create(JsonWebTokenError.prototype);\n\nNotBeforeError.prototype.constructor = NotBeforeError;\n\nmodule.exports = NotBeforeError;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Ob3RCZWZvcmVFcnJvci5qcyIsIm1hcHBpbmdzIjoiQUFBQSx3QkFBd0IsbUJBQU8sQ0FBQyx1RkFBcUI7O0FBRXJEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWRtaW5cXERlc2t0b3BcXGJsb2dcXHRhbmRwcm9cXG5vZGVfbW9kdWxlc1xcanNvbndlYnRva2VuXFxsaWJcXE5vdEJlZm9yZUVycm9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBKc29uV2ViVG9rZW5FcnJvciA9IHJlcXVpcmUoJy4vSnNvbldlYlRva2VuRXJyb3InKTtcblxudmFyIE5vdEJlZm9yZUVycm9yID0gZnVuY3Rpb24gKG1lc3NhZ2UsIGRhdGUpIHtcbiAgSnNvbldlYlRva2VuRXJyb3IuY2FsbCh0aGlzLCBtZXNzYWdlKTtcbiAgdGhpcy5uYW1lID0gJ05vdEJlZm9yZUVycm9yJztcbiAgdGhpcy5kYXRlID0gZGF0ZTtcbn07XG5cbk5vdEJlZm9yZUVycm9yLnByb3RvdHlwZSA9IE9iamVjdC5jcmVhdGUoSnNvbldlYlRva2VuRXJyb3IucHJvdG90eXBlKTtcblxuTm90QmVmb3JlRXJyb3IucHJvdG90eXBlLmNvbnN0cnVjdG9yID0gTm90QmVmb3JlRXJyb3I7XG5cbm1vZHVsZS5leHBvcnRzID0gTm90QmVmb3JlRXJyb3I7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js":
/*!************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/TokenExpiredError.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var JsonWebTokenError = __webpack_require__(/*! ./JsonWebTokenError */ \"(ssr)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\n\nvar TokenExpiredError = function (message, expiredAt) {\n  JsonWebTokenError.call(this, message);\n  this.name = 'TokenExpiredError';\n  this.expiredAt = expiredAt;\n};\n\nTokenExpiredError.prototype = Object.create(JsonWebTokenError.prototype);\n\nTokenExpiredError.prototype.constructor = TokenExpiredError;\n\nmodule.exports = TokenExpiredError;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Ub2tlbkV4cGlyZWRFcnJvci5qcyIsIm1hcHBpbmdzIjoiQUFBQSx3QkFBd0IsbUJBQU8sQ0FBQyx1RkFBcUI7O0FBRXJEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWRtaW5cXERlc2t0b3BcXGJsb2dcXHRhbmRwcm9cXG5vZGVfbW9kdWxlc1xcanNvbndlYnRva2VuXFxsaWJcXFRva2VuRXhwaXJlZEVycm9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBKc29uV2ViVG9rZW5FcnJvciA9IHJlcXVpcmUoJy4vSnNvbldlYlRva2VuRXJyb3InKTtcblxudmFyIFRva2VuRXhwaXJlZEVycm9yID0gZnVuY3Rpb24gKG1lc3NhZ2UsIGV4cGlyZWRBdCkge1xuICBKc29uV2ViVG9rZW5FcnJvci5jYWxsKHRoaXMsIG1lc3NhZ2UpO1xuICB0aGlzLm5hbWUgPSAnVG9rZW5FeHBpcmVkRXJyb3InO1xuICB0aGlzLmV4cGlyZWRBdCA9IGV4cGlyZWRBdDtcbn07XG5cblRva2VuRXhwaXJlZEVycm9yLnByb3RvdHlwZSA9IE9iamVjdC5jcmVhdGUoSnNvbldlYlRva2VuRXJyb3IucHJvdG90eXBlKTtcblxuVG9rZW5FeHBpcmVkRXJyb3IucHJvdG90eXBlLmNvbnN0cnVjdG9yID0gVG9rZW5FeHBpcmVkRXJyb3I7XG5cbm1vZHVsZS5leHBvcnRzID0gVG9rZW5FeHBpcmVkRXJyb3I7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js":
/*!************************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const semver = __webpack_require__(/*! semver */ \"(ssr)/./node_modules/semver/index.js\");\n\nmodule.exports = semver.satisfies(process.version, '>=15.7.0');\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9hc3ltbWV0cmljS2V5RGV0YWlsc1N1cHBvcnRlZC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxlQUFlLG1CQUFPLENBQUMsb0RBQVE7O0FBRS9CIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFkbWluXFxEZXNrdG9wXFxibG9nXFx0YW5kcHJvXFxub2RlX21vZHVsZXNcXGpzb253ZWJ0b2tlblxcbGliXFxhc3ltbWV0cmljS2V5RGV0YWlsc1N1cHBvcnRlZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBzZW12ZXIgPSByZXF1aXJlKCdzZW12ZXInKTtcblxubW9kdWxlLmV4cG9ydHMgPSBzZW12ZXIuc2F0aXNmaWVzKHByb2Nlc3MudmVyc2lvbiwgJz49MTUuNy4wJyk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonwebtoken/lib/psSupported.js":
/*!******************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/psSupported.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var semver = __webpack_require__(/*! semver */ \"(ssr)/./node_modules/semver/index.js\");\n\nmodule.exports = semver.satisfies(process.version, '^6.12.0 || >=8.0.0');\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9wc1N1cHBvcnRlZC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxhQUFhLG1CQUFPLENBQUMsb0RBQVE7O0FBRTdCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFkbWluXFxEZXNrdG9wXFxibG9nXFx0YW5kcHJvXFxub2RlX21vZHVsZXNcXGpzb253ZWJ0b2tlblxcbGliXFxwc1N1cHBvcnRlZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgc2VtdmVyID0gcmVxdWlyZSgnc2VtdmVyJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gc2VtdmVyLnNhdGlzZmllcyhwcm9jZXNzLnZlcnNpb24sICdeNi4xMi4wIHx8ID49OC4wLjAnKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonwebtoken/lib/psSupported.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js":
/*!********************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const semver = __webpack_require__(/*! semver */ \"(ssr)/./node_modules/semver/index.js\");\n\nmodule.exports = semver.satisfies(process.version, '>=16.9.0');\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9yc2FQc3NLZXlEZXRhaWxzU3VwcG9ydGVkLmpzIiwibWFwcGluZ3MiOiJBQUFBLGVBQWUsbUJBQU8sQ0FBQyxvREFBUTs7QUFFL0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWRtaW5cXERlc2t0b3BcXGJsb2dcXHRhbmRwcm9cXG5vZGVfbW9kdWxlc1xcanNvbndlYnRva2VuXFxsaWJcXHJzYVBzc0tleURldGFpbHNTdXBwb3J0ZWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3Qgc2VtdmVyID0gcmVxdWlyZSgnc2VtdmVyJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gc2VtdmVyLnNhdGlzZmllcyhwcm9jZXNzLnZlcnNpb24sICc+PTE2LjkuMCcpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonwebtoken/lib/timespan.js":
/*!***************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/timespan.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var ms = __webpack_require__(/*! ms */ \"(ssr)/./node_modules/ms/index.js\");\n\nmodule.exports = function (time, iat) {\n  var timestamp = iat || Math.floor(Date.now() / 1000);\n\n  if (typeof time === 'string') {\n    var milliseconds = ms(time);\n    if (typeof milliseconds === 'undefined') {\n      return;\n    }\n    return Math.floor(timestamp + milliseconds / 1000);\n  } else if (typeof time === 'number') {\n    return timestamp + time;\n  } else {\n    return;\n  }\n\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi90aW1lc3Bhbi5qcyIsIm1hcHBpbmdzIjoiQUFBQSxTQUFTLG1CQUFPLENBQUMsNENBQUk7O0FBRXJCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQWRtaW5cXERlc2t0b3BcXGJsb2dcXHRhbmRwcm9cXG5vZGVfbW9kdWxlc1xcanNvbndlYnRva2VuXFxsaWJcXHRpbWVzcGFuLmpzIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBtcyA9IHJlcXVpcmUoJ21zJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gKHRpbWUsIGlhdCkge1xuICB2YXIgdGltZXN0YW1wID0gaWF0IHx8IE1hdGguZmxvb3IoRGF0ZS5ub3coKSAvIDEwMDApO1xuXG4gIGlmICh0eXBlb2YgdGltZSA9PT0gJ3N0cmluZycpIHtcbiAgICB2YXIgbWlsbGlzZWNvbmRzID0gbXModGltZSk7XG4gICAgaWYgKHR5cGVvZiBtaWxsaXNlY29uZHMgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIHJldHVybiBNYXRoLmZsb29yKHRpbWVzdGFtcCArIG1pbGxpc2Vjb25kcyAvIDEwMDApO1xuICB9IGVsc2UgaWYgKHR5cGVvZiB0aW1lID09PSAnbnVtYmVyJykge1xuICAgIHJldHVybiB0aW1lc3RhbXAgKyB0aW1lO1xuICB9IGVsc2Uge1xuICAgIHJldHVybjtcbiAgfVxuXG59OyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonwebtoken/lib/timespan.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js":
/*!****************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js ***!
  \****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const ASYMMETRIC_KEY_DETAILS_SUPPORTED = __webpack_require__(/*! ./asymmetricKeyDetailsSupported */ \"(ssr)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js\");\nconst RSA_PSS_KEY_DETAILS_SUPPORTED = __webpack_require__(/*! ./rsaPssKeyDetailsSupported */ \"(ssr)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js\");\n\nconst allowedAlgorithmsForKeys = {\n  'ec': ['ES256', 'ES384', 'ES512'],\n  'rsa': ['RS256', 'PS256', 'RS384', 'PS384', 'RS512', 'PS512'],\n  'rsa-pss': ['PS256', 'PS384', 'PS512']\n};\n\nconst allowedCurves = {\n  ES256: 'prime256v1',\n  ES384: 'secp384r1',\n  ES512: 'secp521r1',\n};\n\nmodule.exports = function(algorithm, key) {\n  if (!algorithm || !key) return;\n\n  const keyType = key.asymmetricKeyType;\n  if (!keyType) return;\n\n  const allowedAlgorithms = allowedAlgorithmsForKeys[keyType];\n\n  if (!allowedAlgorithms) {\n    throw new Error(`Unknown key type \"${keyType}\".`);\n  }\n\n  if (!allowedAlgorithms.includes(algorithm)) {\n    throw new Error(`\"alg\" parameter for \"${keyType}\" key type must be one of: ${allowedAlgorithms.join(', ')}.`)\n  }\n\n  /*\n   * Ignore the next block from test coverage because it gets executed\n   * conditionally depending on the Node version. Not ignoring it would\n   * prevent us from reaching the target % of coverage for versions of\n   * Node under 15.7.0.\n   */\n  /* istanbul ignore next */\n  if (ASYMMETRIC_KEY_DETAILS_SUPPORTED) {\n    switch (keyType) {\n    case 'ec':\n      const keyCurve = key.asymmetricKeyDetails.namedCurve;\n      const allowedCurve = allowedCurves[algorithm];\n\n      if (keyCurve !== allowedCurve) {\n        throw new Error(`\"alg\" parameter \"${algorithm}\" requires curve \"${allowedCurve}\".`);\n      }\n      break;\n\n    case 'rsa-pss':\n      if (RSA_PSS_KEY_DETAILS_SUPPORTED) {\n        const length = parseInt(algorithm.slice(-3), 10);\n        const { hashAlgorithm, mgf1HashAlgorithm, saltLength } = key.asymmetricKeyDetails;\n\n        if (hashAlgorithm !== `sha${length}` || mgf1HashAlgorithm !== hashAlgorithm) {\n          throw new Error(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of \"alg\" ${algorithm}.`);\n        }\n\n        if (saltLength !== undefined && saltLength > length >> 3) {\n          throw new Error(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of \"alg\" ${algorithm}.`)\n        }\n      }\n      break;\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonwebtoken/sign.js":
/*!*******************************************!*\
  !*** ./node_modules/jsonwebtoken/sign.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const timespan = __webpack_require__(/*! ./lib/timespan */ \"(ssr)/./node_modules/jsonwebtoken/lib/timespan.js\");\nconst PS_SUPPORTED = __webpack_require__(/*! ./lib/psSupported */ \"(ssr)/./node_modules/jsonwebtoken/lib/psSupported.js\");\nconst validateAsymmetricKey = __webpack_require__(/*! ./lib/validateAsymmetricKey */ \"(ssr)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\");\nconst jws = __webpack_require__(/*! jws */ \"(ssr)/./node_modules/jws/index.js\");\nconst includes = __webpack_require__(/*! lodash.includes */ \"(ssr)/./node_modules/lodash.includes/index.js\");\nconst isBoolean = __webpack_require__(/*! lodash.isboolean */ \"(ssr)/./node_modules/lodash.isboolean/index.js\");\nconst isInteger = __webpack_require__(/*! lodash.isinteger */ \"(ssr)/./node_modules/lodash.isinteger/index.js\");\nconst isNumber = __webpack_require__(/*! lodash.isnumber */ \"(ssr)/./node_modules/lodash.isnumber/index.js\");\nconst isPlainObject = __webpack_require__(/*! lodash.isplainobject */ \"(ssr)/./node_modules/lodash.isplainobject/index.js\");\nconst isString = __webpack_require__(/*! lodash.isstring */ \"(ssr)/./node_modules/lodash.isstring/index.js\");\nconst once = __webpack_require__(/*! lodash.once */ \"(ssr)/./node_modules/lodash.once/index.js\");\nconst { KeyObject, createSecretKey, createPrivateKey } = __webpack_require__(/*! crypto */ \"crypto\")\n\nconst SUPPORTED_ALGS = ['RS256', 'RS384', 'RS512', 'ES256', 'ES384', 'ES512', 'HS256', 'HS384', 'HS512', 'none'];\nif (PS_SUPPORTED) {\n  SUPPORTED_ALGS.splice(3, 0, 'PS256', 'PS384', 'PS512');\n}\n\nconst sign_options_schema = {\n  expiresIn: { isValid: function(value) { return isInteger(value) || (isString(value) && value); }, message: '\"expiresIn\" should be a number of seconds or string representing a timespan' },\n  notBefore: { isValid: function(value) { return isInteger(value) || (isString(value) && value); }, message: '\"notBefore\" should be a number of seconds or string representing a timespan' },\n  audience: { isValid: function(value) { return isString(value) || Array.isArray(value); }, message: '\"audience\" must be a string or array' },\n  algorithm: { isValid: includes.bind(null, SUPPORTED_ALGS), message: '\"algorithm\" must be a valid string enum value' },\n  header: { isValid: isPlainObject, message: '\"header\" must be an object' },\n  encoding: { isValid: isString, message: '\"encoding\" must be a string' },\n  issuer: { isValid: isString, message: '\"issuer\" must be a string' },\n  subject: { isValid: isString, message: '\"subject\" must be a string' },\n  jwtid: { isValid: isString, message: '\"jwtid\" must be a string' },\n  noTimestamp: { isValid: isBoolean, message: '\"noTimestamp\" must be a boolean' },\n  keyid: { isValid: isString, message: '\"keyid\" must be a string' },\n  mutatePayload: { isValid: isBoolean, message: '\"mutatePayload\" must be a boolean' },\n  allowInsecureKeySizes: { isValid: isBoolean, message: '\"allowInsecureKeySizes\" must be a boolean'},\n  allowInvalidAsymmetricKeyTypes: { isValid: isBoolean, message: '\"allowInvalidAsymmetricKeyTypes\" must be a boolean'}\n};\n\nconst registered_claims_schema = {\n  iat: { isValid: isNumber, message: '\"iat\" should be a number of seconds' },\n  exp: { isValid: isNumber, message: '\"exp\" should be a number of seconds' },\n  nbf: { isValid: isNumber, message: '\"nbf\" should be a number of seconds' }\n};\n\nfunction validate(schema, allowUnknown, object, parameterName) {\n  if (!isPlainObject(object)) {\n    throw new Error('Expected \"' + parameterName + '\" to be a plain object.');\n  }\n  Object.keys(object)\n    .forEach(function(key) {\n      const validator = schema[key];\n      if (!validator) {\n        if (!allowUnknown) {\n          throw new Error('\"' + key + '\" is not allowed in \"' + parameterName + '\"');\n        }\n        return;\n      }\n      if (!validator.isValid(object[key])) {\n        throw new Error(validator.message);\n      }\n    });\n}\n\nfunction validateOptions(options) {\n  return validate(sign_options_schema, false, options, 'options');\n}\n\nfunction validatePayload(payload) {\n  return validate(registered_claims_schema, true, payload, 'payload');\n}\n\nconst options_to_payload = {\n  'audience': 'aud',\n  'issuer': 'iss',\n  'subject': 'sub',\n  'jwtid': 'jti'\n};\n\nconst options_for_objects = [\n  'expiresIn',\n  'notBefore',\n  'noTimestamp',\n  'audience',\n  'issuer',\n  'subject',\n  'jwtid',\n];\n\nmodule.exports = function (payload, secretOrPrivateKey, options, callback) {\n  if (typeof options === 'function') {\n    callback = options;\n    options = {};\n  } else {\n    options = options || {};\n  }\n\n  const isObjectPayload = typeof payload === 'object' &&\n                        !Buffer.isBuffer(payload);\n\n  const header = Object.assign({\n    alg: options.algorithm || 'HS256',\n    typ: isObjectPayload ? 'JWT' : undefined,\n    kid: options.keyid\n  }, options.header);\n\n  function failure(err) {\n    if (callback) {\n      return callback(err);\n    }\n    throw err;\n  }\n\n  if (!secretOrPrivateKey && options.algorithm !== 'none') {\n    return failure(new Error('secretOrPrivateKey must have a value'));\n  }\n\n  if (secretOrPrivateKey != null && !(secretOrPrivateKey instanceof KeyObject)) {\n    try {\n      secretOrPrivateKey = createPrivateKey(secretOrPrivateKey)\n    } catch (_) {\n      try {\n        secretOrPrivateKey = createSecretKey(typeof secretOrPrivateKey === 'string' ? Buffer.from(secretOrPrivateKey) : secretOrPrivateKey)\n      } catch (_) {\n        return failure(new Error('secretOrPrivateKey is not valid key material'));\n      }\n    }\n  }\n\n  if (header.alg.startsWith('HS') && secretOrPrivateKey.type !== 'secret') {\n    return failure(new Error((`secretOrPrivateKey must be a symmetric key when using ${header.alg}`)))\n  } else if (/^(?:RS|PS|ES)/.test(header.alg)) {\n    if (secretOrPrivateKey.type !== 'private') {\n      return failure(new Error((`secretOrPrivateKey must be an asymmetric key when using ${header.alg}`)))\n    }\n    if (!options.allowInsecureKeySizes &&\n      !header.alg.startsWith('ES') &&\n      secretOrPrivateKey.asymmetricKeyDetails !== undefined && //KeyObject.asymmetricKeyDetails is supported in Node 15+\n      secretOrPrivateKey.asymmetricKeyDetails.modulusLength < 2048) {\n      return failure(new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`));\n    }\n  }\n\n  if (typeof payload === 'undefined') {\n    return failure(new Error('payload is required'));\n  } else if (isObjectPayload) {\n    try {\n      validatePayload(payload);\n    }\n    catch (error) {\n      return failure(error);\n    }\n    if (!options.mutatePayload) {\n      payload = Object.assign({},payload);\n    }\n  } else {\n    const invalid_options = options_for_objects.filter(function (opt) {\n      return typeof options[opt] !== 'undefined';\n    });\n\n    if (invalid_options.length > 0) {\n      return failure(new Error('invalid ' + invalid_options.join(',') + ' option for ' + (typeof payload ) + ' payload'));\n    }\n  }\n\n  if (typeof payload.exp !== 'undefined' && typeof options.expiresIn !== 'undefined') {\n    return failure(new Error('Bad \"options.expiresIn\" option the payload already has an \"exp\" property.'));\n  }\n\n  if (typeof payload.nbf !== 'undefined' && typeof options.notBefore !== 'undefined') {\n    return failure(new Error('Bad \"options.notBefore\" option the payload already has an \"nbf\" property.'));\n  }\n\n  try {\n    validateOptions(options);\n  }\n  catch (error) {\n    return failure(error);\n  }\n\n  if (!options.allowInvalidAsymmetricKeyTypes) {\n    try {\n      validateAsymmetricKey(header.alg, secretOrPrivateKey);\n    } catch (error) {\n      return failure(error);\n    }\n  }\n\n  const timestamp = payload.iat || Math.floor(Date.now() / 1000);\n\n  if (options.noTimestamp) {\n    delete payload.iat;\n  } else if (isObjectPayload) {\n    payload.iat = timestamp;\n  }\n\n  if (typeof options.notBefore !== 'undefined') {\n    try {\n      payload.nbf = timespan(options.notBefore, timestamp);\n    }\n    catch (err) {\n      return failure(err);\n    }\n    if (typeof payload.nbf === 'undefined') {\n      return failure(new Error('\"notBefore\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n    }\n  }\n\n  if (typeof options.expiresIn !== 'undefined' && typeof payload === 'object') {\n    try {\n      payload.exp = timespan(options.expiresIn, timestamp);\n    }\n    catch (err) {\n      return failure(err);\n    }\n    if (typeof payload.exp === 'undefined') {\n      return failure(new Error('\"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n    }\n  }\n\n  Object.keys(options_to_payload).forEach(function (key) {\n    const claim = options_to_payload[key];\n    if (typeof options[key] !== 'undefined') {\n      if (typeof payload[claim] !== 'undefined') {\n        return failure(new Error('Bad \"options.' + key + '\" option. The payload already has an \"' + claim + '\" property.'));\n      }\n      payload[claim] = options[key];\n    }\n  });\n\n  const encoding = options.encoding || 'utf8';\n\n  if (typeof callback === 'function') {\n    callback = callback && once(callback);\n\n    jws.createSign({\n      header: header,\n      privateKey: secretOrPrivateKey,\n      payload: payload,\n      encoding: encoding\n    }).once('error', callback)\n      .once('done', function (signature) {\n        // TODO: Remove in favor of the modulus length check before signing once node 15+ is the minimum supported version\n        if(!options.allowInsecureKeySizes && /^(?:RS|PS)/.test(header.alg) && signature.length < 256) {\n          return callback(new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`))\n        }\n        callback(null, signature);\n      });\n  } else {\n    let signature = jws.sign({header: header, payload: payload, secret: secretOrPrivateKey, encoding: encoding});\n    // TODO: Remove in favor of the modulus length check before signing once node 15+ is the minimum supported version\n    if(!options.allowInsecureKeySizes && /^(?:RS|PS)/.test(header.alg) && signature.length < 256) {\n      throw new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`)\n    }\n    return signature\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonwebtoken/sign.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jsonwebtoken/verify.js":
/*!*********************************************!*\
  !*** ./node_modules/jsonwebtoken/verify.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const JsonWebTokenError = __webpack_require__(/*! ./lib/JsonWebTokenError */ \"(ssr)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\nconst NotBeforeError = __webpack_require__(/*! ./lib/NotBeforeError */ \"(ssr)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\");\nconst TokenExpiredError = __webpack_require__(/*! ./lib/TokenExpiredError */ \"(ssr)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\");\nconst decode = __webpack_require__(/*! ./decode */ \"(ssr)/./node_modules/jsonwebtoken/decode.js\");\nconst timespan = __webpack_require__(/*! ./lib/timespan */ \"(ssr)/./node_modules/jsonwebtoken/lib/timespan.js\");\nconst validateAsymmetricKey = __webpack_require__(/*! ./lib/validateAsymmetricKey */ \"(ssr)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\");\nconst PS_SUPPORTED = __webpack_require__(/*! ./lib/psSupported */ \"(ssr)/./node_modules/jsonwebtoken/lib/psSupported.js\");\nconst jws = __webpack_require__(/*! jws */ \"(ssr)/./node_modules/jws/index.js\");\nconst {KeyObject, createSecretKey, createPublicKey} = __webpack_require__(/*! crypto */ \"crypto\");\n\nconst PUB_KEY_ALGS = ['RS256', 'RS384', 'RS512'];\nconst EC_KEY_ALGS = ['ES256', 'ES384', 'ES512'];\nconst RSA_KEY_ALGS = ['RS256', 'RS384', 'RS512'];\nconst HS_ALGS = ['HS256', 'HS384', 'HS512'];\n\nif (PS_SUPPORTED) {\n  PUB_KEY_ALGS.splice(PUB_KEY_ALGS.length, 0, 'PS256', 'PS384', 'PS512');\n  RSA_KEY_ALGS.splice(RSA_KEY_ALGS.length, 0, 'PS256', 'PS384', 'PS512');\n}\n\nmodule.exports = function (jwtString, secretOrPublicKey, options, callback) {\n  if ((typeof options === 'function') && !callback) {\n    callback = options;\n    options = {};\n  }\n\n  if (!options) {\n    options = {};\n  }\n\n  //clone this object since we are going to mutate it.\n  options = Object.assign({}, options);\n\n  let done;\n\n  if (callback) {\n    done = callback;\n  } else {\n    done = function(err, data) {\n      if (err) throw err;\n      return data;\n    };\n  }\n\n  if (options.clockTimestamp && typeof options.clockTimestamp !== 'number') {\n    return done(new JsonWebTokenError('clockTimestamp must be a number'));\n  }\n\n  if (options.nonce !== undefined && (typeof options.nonce !== 'string' || options.nonce.trim() === '')) {\n    return done(new JsonWebTokenError('nonce must be a non-empty string'));\n  }\n\n  if (options.allowInvalidAsymmetricKeyTypes !== undefined && typeof options.allowInvalidAsymmetricKeyTypes !== 'boolean') {\n    return done(new JsonWebTokenError('allowInvalidAsymmetricKeyTypes must be a boolean'));\n  }\n\n  const clockTimestamp = options.clockTimestamp || Math.floor(Date.now() / 1000);\n\n  if (!jwtString){\n    return done(new JsonWebTokenError('jwt must be provided'));\n  }\n\n  if (typeof jwtString !== 'string') {\n    return done(new JsonWebTokenError('jwt must be a string'));\n  }\n\n  const parts = jwtString.split('.');\n\n  if (parts.length !== 3){\n    return done(new JsonWebTokenError('jwt malformed'));\n  }\n\n  let decodedToken;\n\n  try {\n    decodedToken = decode(jwtString, { complete: true });\n  } catch(err) {\n    return done(err);\n  }\n\n  if (!decodedToken) {\n    return done(new JsonWebTokenError('invalid token'));\n  }\n\n  const header = decodedToken.header;\n  let getSecret;\n\n  if(typeof secretOrPublicKey === 'function') {\n    if(!callback) {\n      return done(new JsonWebTokenError('verify must be called asynchronous if secret or public key is provided as a callback'));\n    }\n\n    getSecret = secretOrPublicKey;\n  }\n  else {\n    getSecret = function(header, secretCallback) {\n      return secretCallback(null, secretOrPublicKey);\n    };\n  }\n\n  return getSecret(header, function(err, secretOrPublicKey) {\n    if(err) {\n      return done(new JsonWebTokenError('error in secret or public key callback: ' + err.message));\n    }\n\n    const hasSignature = parts[2].trim() !== '';\n\n    if (!hasSignature && secretOrPublicKey){\n      return done(new JsonWebTokenError('jwt signature is required'));\n    }\n\n    if (hasSignature && !secretOrPublicKey) {\n      return done(new JsonWebTokenError('secret or public key must be provided'));\n    }\n\n    if (!hasSignature && !options.algorithms) {\n      return done(new JsonWebTokenError('please specify \"none\" in \"algorithms\" to verify unsigned tokens'));\n    }\n\n    if (secretOrPublicKey != null && !(secretOrPublicKey instanceof KeyObject)) {\n      try {\n        secretOrPublicKey = createPublicKey(secretOrPublicKey);\n      } catch (_) {\n        try {\n          secretOrPublicKey = createSecretKey(typeof secretOrPublicKey === 'string' ? Buffer.from(secretOrPublicKey) : secretOrPublicKey);\n        } catch (_) {\n          return done(new JsonWebTokenError('secretOrPublicKey is not valid key material'))\n        }\n      }\n    }\n\n    if (!options.algorithms) {\n      if (secretOrPublicKey.type === 'secret') {\n        options.algorithms = HS_ALGS;\n      } else if (['rsa', 'rsa-pss'].includes(secretOrPublicKey.asymmetricKeyType)) {\n        options.algorithms = RSA_KEY_ALGS\n      } else if (secretOrPublicKey.asymmetricKeyType === 'ec') {\n        options.algorithms = EC_KEY_ALGS\n      } else {\n        options.algorithms = PUB_KEY_ALGS\n      }\n    }\n\n    if (options.algorithms.indexOf(decodedToken.header.alg) === -1) {\n      return done(new JsonWebTokenError('invalid algorithm'));\n    }\n\n    if (header.alg.startsWith('HS') && secretOrPublicKey.type !== 'secret') {\n      return done(new JsonWebTokenError((`secretOrPublicKey must be a symmetric key when using ${header.alg}`)))\n    } else if (/^(?:RS|PS|ES)/.test(header.alg) && secretOrPublicKey.type !== 'public') {\n      return done(new JsonWebTokenError((`secretOrPublicKey must be an asymmetric key when using ${header.alg}`)))\n    }\n\n    if (!options.allowInvalidAsymmetricKeyTypes) {\n      try {\n        validateAsymmetricKey(header.alg, secretOrPublicKey);\n      } catch (e) {\n        return done(e);\n      }\n    }\n\n    let valid;\n\n    try {\n      valid = jws.verify(jwtString, decodedToken.header.alg, secretOrPublicKey);\n    } catch (e) {\n      return done(e);\n    }\n\n    if (!valid) {\n      return done(new JsonWebTokenError('invalid signature'));\n    }\n\n    const payload = decodedToken.payload;\n\n    if (typeof payload.nbf !== 'undefined' && !options.ignoreNotBefore) {\n      if (typeof payload.nbf !== 'number') {\n        return done(new JsonWebTokenError('invalid nbf value'));\n      }\n      if (payload.nbf > clockTimestamp + (options.clockTolerance || 0)) {\n        return done(new NotBeforeError('jwt not active', new Date(payload.nbf * 1000)));\n      }\n    }\n\n    if (typeof payload.exp !== 'undefined' && !options.ignoreExpiration) {\n      if (typeof payload.exp !== 'number') {\n        return done(new JsonWebTokenError('invalid exp value'));\n      }\n      if (clockTimestamp >= payload.exp + (options.clockTolerance || 0)) {\n        return done(new TokenExpiredError('jwt expired', new Date(payload.exp * 1000)));\n      }\n    }\n\n    if (options.audience) {\n      const audiences = Array.isArray(options.audience) ? options.audience : [options.audience];\n      const target = Array.isArray(payload.aud) ? payload.aud : [payload.aud];\n\n      const match = target.some(function (targetAudience) {\n        return audiences.some(function (audience) {\n          return audience instanceof RegExp ? audience.test(targetAudience) : audience === targetAudience;\n        });\n      });\n\n      if (!match) {\n        return done(new JsonWebTokenError('jwt audience invalid. expected: ' + audiences.join(' or ')));\n      }\n    }\n\n    if (options.issuer) {\n      const invalid_issuer =\n              (typeof options.issuer === 'string' && payload.iss !== options.issuer) ||\n              (Array.isArray(options.issuer) && options.issuer.indexOf(payload.iss) === -1);\n\n      if (invalid_issuer) {\n        return done(new JsonWebTokenError('jwt issuer invalid. expected: ' + options.issuer));\n      }\n    }\n\n    if (options.subject) {\n      if (payload.sub !== options.subject) {\n        return done(new JsonWebTokenError('jwt subject invalid. expected: ' + options.subject));\n      }\n    }\n\n    if (options.jwtid) {\n      if (payload.jti !== options.jwtid) {\n        return done(new JsonWebTokenError('jwt jwtid invalid. expected: ' + options.jwtid));\n      }\n    }\n\n    if (options.nonce) {\n      if (payload.nonce !== options.nonce) {\n        return done(new JsonWebTokenError('jwt nonce invalid. expected: ' + options.nonce));\n      }\n    }\n\n    if (options.maxAge) {\n      if (typeof payload.iat !== 'number') {\n        return done(new JsonWebTokenError('iat required when maxAge is specified'));\n      }\n\n      const maxAgeTimestamp = timespan(options.maxAge, payload.iat);\n      if (typeof maxAgeTimestamp === 'undefined') {\n        return done(new JsonWebTokenError('\"maxAge\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n      }\n      if (clockTimestamp >= maxAgeTimestamp + (options.clockTolerance || 0)) {\n        return done(new TokenExpiredError('maxAge exceeded', new Date(maxAgeTimestamp * 1000)));\n      }\n    }\n\n    if (options.complete === true) {\n      const signature = decodedToken.signature;\n\n      return done(null, {\n        header: header,\n        payload: payload,\n        signature: signature\n      });\n    }\n\n    return done(null, payload);\n  });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsonwebtoken/verify.js\n");

/***/ })

};
;