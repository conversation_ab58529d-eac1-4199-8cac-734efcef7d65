(()=>{var a={};a.id=2659,a.ids=[2659],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30072:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>u});var d=c(21124),e=c(38301),f=c.n(e),g=c(42378),h=c(2537),i=c(73729),j=c(7372),k=c.n(j);function l(){return(l=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var m=(0,e.forwardRef)(function(a,b){var c=a.color,d=a.size,e=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return f().createElement("svg",l({ref:b,xmlns:"http://www.w3.org/2000/svg",width:e,height:e,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),f().createElement("path",{d:"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"}),f().createElement("circle",{cx:"12",cy:"7",r:"4"}))});function n(){return(n=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}m.propTypes={color:k().string,size:k().oneOfType([k().string,k().number])},m.displayName="User";var o=(0,e.forwardRef)(function(a,b){var c=a.color,d=a.size,e=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return f().createElement("svg",n({ref:b,xmlns:"http://www.w3.org/2000/svg",width:e,height:e,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),f().createElement("path",{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}),f().createElement("line",{x1:"12",y1:"9",x2:"12",y2:"13"}),f().createElement("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"}))});function p(){return(p=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}o.propTypes={color:k().string,size:k().oneOfType([k().string,k().number])},o.displayName="AlertTriangle";var q=(0,e.forwardRef)(function(a,b){var c=a.color,d=a.size,e=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return f().createElement("svg",p({ref:b,xmlns:"http://www.w3.org/2000/svg",width:e,height:e,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),f().createElement("path",{d:"M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"}))});function r(){return(r=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}q.propTypes={color:k().string,size:k().oneOfType([k().string,k().number])},q.displayName="Key";var s=(0,e.forwardRef)(function(a,b){var c=a.color,d=a.size,e=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return f().createElement("svg",r({ref:b,xmlns:"http://www.w3.org/2000/svg",width:e,height:e,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),f().createElement("polyline",{points:"3 6 5 6 21 6"}),f().createElement("path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"}),f().createElement("line",{x1:"10",y1:"11",x2:"10",y2:"17"}),f().createElement("line",{x1:"14",y1:"11",x2:"14",y2:"17"}))});s.propTypes={color:k().string,size:k().oneOfType([k().string,k().number])},s.displayName="Trash2";var t=c(71697);function u(){let a=(0,g.useRouter)(),b=(0,g.useParams)(),c=b.id,f=b.memberId,[j,k]=(0,e.useState)(!1),[l,n]=(0,e.useState)(!0),[p,r]=(0,e.useState)([]),[u,v]=(0,e.useState)(null),[w,x]=(0,e.useState)({permissions:[],departmentRole:"member"}),[y,z]=(0,e.useState)(!1),[A,B]=(0,e.useState)(!1),[C,D]=(0,e.useState)(""),[E,F]=(0,e.useState)(!1),[G,H]=(0,e.useState)(!1),I=async b=>{b.preventDefault();try{k(!0);let b=localStorage.getItem("sessionToken")||"",d=await Object(function(){var a=Error("Cannot find module '@/apiRequests/department'");throw a.code="MODULE_NOT_FOUND",a}())(c,f,w,b);d.payload.success?(h.oR.success("Cập nhật quyền th\xe0nh vi\xean th\xe0nh c\xf4ng"),a.push(`/dashboard/departments/${c}`)):h.oR.error(d.payload.message||"Kh\xf4ng thể cập nhật quyền th\xe0nh vi\xean")}catch(a){console.error("Error updating member permissions:",a),h.oR.error("Lỗi khi cập nhật quyền th\xe0nh vi\xean")}finally{k(!1)}},J=async()=>{try{F(!0);let b=localStorage.getItem("sessionToken")||"",d=await Object(function(){var a=Error("Cannot find module '@/apiRequests/department'");throw a.code="MODULE_NOT_FOUND",a}())(c,f,b);d.payload.success?(h.oR.success("X\xf3a th\xe0nh vi\xean khỏi ph\xf2ng ban th\xe0nh c\xf4ng"),a.push(`/dashboard/departments/${c}`)):h.oR.error(d.payload.message||"Kh\xf4ng thể x\xf3a th\xe0nh vi\xean")}catch(a){console.error("Error deleting member:",a),h.oR.error("Lỗi khi x\xf3a th\xe0nh vi\xean")}finally{F(!1),z(!1)}},K=async()=>{if(!C.trim())return void h.oR.error("Vui l\xf2ng nhập mật khẩu mới");try{H(!0);let a=localStorage.getItem("sessionToken")||"",b=await Object(function(){var a=Error("Cannot find module '@/apiRequests/user'");throw a.code="MODULE_NOT_FOUND",a}())({userId:f,password:C},a);b.payload.success?(h.oR.success("Đổi mật khẩu th\xe0nh c\xf4ng"),B(!1),D("")):h.oR.error(b.payload.message||"Kh\xf4ng thể đổi mật khẩu")}catch(a){console.error("Error resetting password:",a),h.oR.error("Lỗi khi đổi mật khẩu")}finally{H(!1)}},L=p.reduce((a,b)=>(a[b.category]||(a[b.category]=[]),a[b.category].push(b),a),{});return l?(0,d.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):u?(0,d.jsx)(Object(function(){var a=Error("Cannot find module '@/components/PermissionGuard'");throw a.code="MODULE_NOT_FOUND",a}()),{requiredPermission:"admin",children:(0,d.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,d.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,d.jsx)("button",{onClick:()=>a.push(`/dashboard/departments/${c}`),className:"p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:(0,d.jsx)(i.A,{size:20})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Chỉnh sửa quyền th\xe0nh vi\xean"}),(0,d.jsx)("p",{className:"text-gray-600 mt-1",children:"Cập nhật quyền hạn cho th\xe0nh vi\xean ph\xf2ng ban"})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 mb-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,d.jsx)(m,{size:20}),"Th\xf4ng tin th\xe0nh vi\xean"]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"T\xean người d\xf9ng"}),(0,d.jsx)("p",{className:"text-gray-900 font-medium",children:u.username})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),(0,d.jsx)("p",{className:"text-gray-900",children:u.email})]}),u.phonenumber&&(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số điện thoại"}),(0,d.jsx)("p",{className:"text-gray-900",children:u.phonenumber})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Vai tr\xf2 hiện tại"}),(0,d.jsx)(Object(function(){var a=Error("Cannot find module '@/components/ui/Badge'");throw a.code="MODULE_NOT_FOUND",a}()),{variant:"info",children:"manager"===u.departmentRole?"Quản l\xfd ph\xf2ng ban":"Th\xe0nh vi\xean"})]})]})]}),(0,d.jsxs)("form",{onSubmit:I,className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Vai tr\xf2 trong ph\xf2ng ban"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("label",{className:"flex items-center space-x-3",children:[(0,d.jsx)("input",{type:"radio",name:"departmentRole",value:"member",checked:"member"===w.departmentRole,onChange:a=>x(b=>({...b,departmentRole:a.target.value})),className:"text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium text-gray-900",children:"Th\xe0nh vi\xean"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"Th\xe0nh vi\xean thường của ph\xf2ng ban"})]})]}),(0,d.jsxs)("label",{className:"flex items-center space-x-3",children:[(0,d.jsx)("input",{type:"radio",name:"departmentRole",value:"manager",checked:"manager"===w.departmentRole,onChange:a=>x(b=>({...b,departmentRole:a.target.value})),className:"text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium text-gray-900",children:"Quản l\xfd ph\xf2ng ban"}),(0,d.jsxs)("p",{className:"text-sm text-gray-500",children:["C\xf3 quyền quản l\xfd th\xe0nh vi\xean trong ph\xf2ng ban","department_manager"===u.rule&&"manager"===w.departmentRole&&(0,d.jsx)("span",{className:"text-green-600 ml-1",children:"(Đang l\xe0 quản l\xfd)"}),"department_manager"===u.rule&&"manager"!==w.departmentRole&&(0,d.jsx)("span",{className:"text-orange-600 ml-1",children:"(Sẽ bị hạ cấp)"})]})]})]})]}),"department_manager"===u.rule&&"manager"!==w.departmentRole&&(0,d.jsx)("div",{className:"mt-4 p-4 bg-orange-50 border border-orange-200 rounded-lg",children:(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)(o,{size:20,className:"text-orange-500 mt-0.5 mr-3 flex-shrink-0"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-orange-800 mb-1",children:"Cảnh b\xe1o thay đổi vai tr\xf2"}),(0,d.jsxs)("p",{className:"text-sm text-orange-700",children:["Th\xe0nh vi\xean n\xe0y hiện đang l\xe0 ",(0,d.jsx)("strong",{children:"Quản l\xfd ph\xf2ng ban"}),'. Nếu thay đổi về "Th\xe0nh vi\xean", họ sẽ mất quyền quản l\xfd ph\xf2ng ban v\xe0 vai tr\xf2 hệ thống sẽ được thay đổi về "Th\xe0nh vi\xean ph\xf2ng ban".']})]})]})}),"manager"===w.departmentRole&&"department_manager"!==u.rule&&(0,d.jsx)("div",{className:"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)(o,{size:20,className:"text-blue-500 mt-0.5 mr-3 flex-shrink-0"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-blue-800 mb-1",children:"Th\xf4ng b\xe1o thăng cấp"}),(0,d.jsxs)("p",{className:"text-sm text-blue-700",children:["Th\xe0nh vi\xean n\xe0y sẽ được thăng cấp th\xe0nh ",(0,d.jsx)("strong",{children:"Quản l\xfd ph\xf2ng ban"}),". Nếu ph\xf2ng ban đ\xe3 c\xf3 quản l\xfd kh\xe1c, họ sẽ tự động bị hạ cấp về th\xe0nh vi\xean."]})]})]})})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quyền hạn cụ thể"}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Chọn c\xe1c quyền cụ thể cho th\xe0nh vi\xean n\xe0y (bổ sung v\xe0o quyền mặc định của ph\xf2ng ban)"}),(0,d.jsx)("div",{className:"space-y-4",children:Object.entries(L).map(([a,b])=>(0,d.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:a}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2",children:b.map(a=>(0,d.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,d.jsx)("input",{type:"checkbox",checked:w.permissions.includes(a.key),onChange:b=>{var c,d;return c=a.key,d=b.target.checked,void x(a=>({...a,permissions:d?[...a.permissions,c]:a.permissions.filter(a=>a!==c)}))},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsx)("span",{className:"text-sm text-gray-700",children:a.name})]},a.key))})]},a))}),(0,d.jsx)("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg",children:(0,d.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,d.jsx)("strong",{children:"Lưu \xfd:"})," Th\xe0nh vi\xean sẽ c\xf3 tổng cộng ",w.permissions.length," quyền được chọn cộng với c\xe1c quyền mặc định của ph\xf2ng ban."]})})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,d.jsx)(o,{size:20,className:"text-orange-500"}),"Quản l\xfd t\xe0i khoản"]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"border border-blue-200 rounded-lg p-4",children:[(0,d.jsxs)("h3",{className:"font-medium text-gray-900 mb-2 flex items-center gap-2",children:[(0,d.jsx)(q,{size:16,className:"text-blue-500"}),"Đổi mật khẩu"]}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"Đặt lại mật khẩu cho th\xe0nh vi\xean n\xe0y"}),A?(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)("input",{type:"password",value:C,onChange:a=>D(a.target.value),placeholder:"Nhập mật khẩu mới",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)("button",{type:"button",onClick:K,disabled:G,className:"px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:G?"Đang cập nhật...":"Cập nhật"}),(0,d.jsx)("button",{type:"button",onClick:()=>{B(!1),D("")},className:"px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"})]})]}):(0,d.jsx)("button",{type:"button",onClick:()=>B(!0),className:"px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors",children:"Đổi mật khẩu"})]}),(0,d.jsxs)("div",{className:"border border-red-200 rounded-lg p-4",children:[(0,d.jsxs)("h3",{className:"font-medium text-gray-900 mb-2 flex items-center gap-2",children:[(0,d.jsx)(s,{size:16,className:"text-red-500"}),"X\xf3a khỏi ph\xf2ng ban"]}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"X\xf3a th\xe0nh vi\xean n\xe0y khỏi ph\xf2ng ban (kh\xf4ng x\xf3a t\xe0i khoản)"}),y?(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)("div",{className:"p-3 bg-red-50 rounded-lg",children:(0,d.jsxs)("p",{className:"text-sm text-red-800",children:[(0,d.jsx)("strong",{children:"Cảnh b\xe1o:"})," Th\xe0nh vi\xean sẽ bị x\xf3a khỏi ph\xf2ng ban v\xe0 mất tất cả quyền li\xean quan. T\xe0i khoản vẫn tồn tại nhưng kh\xf4ng thuộc ph\xf2ng ban n\xe0o."]})}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)("button",{type:"button",onClick:J,disabled:E,className:"px-3 py-2 bg-red-600 text-white text-sm rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50",children:E?"Đang x\xf3a...":"X\xe1c nhận x\xf3a"}),(0,d.jsx)("button",{type:"button",onClick:()=>z(!1),className:"px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"})]})]}):(0,d.jsx)("button",{type:"button",onClick:()=>z(!0),className:"px-3 py-2 bg-red-600 text-white text-sm rounded-lg hover:bg-red-700 transition-colors",children:"X\xf3a khỏi ph\xf2ng ban"})]})]})]}),(0,d.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,d.jsx)("button",{type:"button",onClick:()=>a.push(`/dashboard/departments/${c}`),className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,d.jsxs)("button",{type:"submit",disabled:j,className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:[(0,d.jsx)(t.A,{size:16}),j?"Đang cập nhật...":"Cập nhật quyền"]})]})]})]})}):(0,d.jsx)("div",{className:"text-center py-8",children:(0,d.jsx)("p",{className:"text-gray-500",children:"Kh\xf4ng t\xecm thấy th\xe0nh vi\xean"})})}!function(){var a=Error("Cannot find module '@/apiRequests/department'");throw a.code="MODULE_NOT_FOUND",a}(),function(){var a=Error("Cannot find module '@/apiRequests/user'");throw a.code="MODULE_NOT_FOUND",a}(),function(){var a=Error("Cannot find module '@/components/PermissionGuard'");throw a.code="MODULE_NOT_FOUND",a}(),function(){var a=Error("Cannot find module '@/components/ui/Badge'");throw a.code="MODULE_NOT_FOUND",a}()},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},48112:(a,b,c)=>{Promise.resolve().then(c.bind(c,87710))},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(49754),e=c(9117),f=c(46595),g=c(32324),h=c(39326),i=c(38928),j=c(20175),k=c(12),l=c(54290),m=c(12696),n=c(82802),o=c(77533),p=c(45229),q=c(32822),r=c(261),s=c(26453),t=c(52474),u=c(26713),v=c(51356),w=c(62685),x=c(36225),y=c(63446),z=c(2762),A=c(45742),B=c(86439),C=c(81170),D=c.n(C),E=c(62506),F=c(91203),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H={children:["",{children:["(private)",{children:["dashboard",{children:["departments",{children:["[id]",{children:["members",{children:["[memberId]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,87710)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\departments\\[id]\\members\\[memberId]\\edit\\page.tsx"]}]},{}]},{}]},{}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,11841)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,51472)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,5682)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,81170,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.bind(c,59732)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,I=["C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\departments\\[id]\\members\\[memberId]\\edit\\page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(private)/dashboard/departments/[id]/members/[memberId]/edit/page",pathname:"/dashboard/departments/[id]/members/[memberId]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/(private)/dashboard/departments/[id]/members/[memberId]/edit/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},82960:(a,b,c)=>{Promise.resolve().then(c.bind(c,30072))},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},87710:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\src\\\\app\\\\(private)\\\\dashboard\\\\departments\\\\[id]\\\\members\\\\[memberId]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\departments\\[id]\\members\\[memberId]\\edit\\page.tsx","default")}};var b=require("../../../../../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[5873,1428,5607,7652],()=>b(b.s=78440));module.exports=c})();