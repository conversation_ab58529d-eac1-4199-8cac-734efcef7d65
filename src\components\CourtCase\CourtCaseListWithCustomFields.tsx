"use client";

import { useState, useEffect } from "react";
import { CourtCaseItemType } from "@/schemaValidations/courtCase.schema";
import { formatDate } from "@/utils/formatters";
import { usePermissions } from "@/hooks/usePermissions";
import { toast } from "react-toastify";
import customFieldApiRequest, { CustomField } from "@/apiRequests/customField";
import fieldConfigurationApiRequest, { FieldConfiguration } from "@/apiRequests/fieldConfiguration";
import dateCountdownApiRequest, { DateCountdown } from "@/apiRequests/dateCountdown";
import DateCountdownBadge from "@/components/DateCountdown/DateCountdownBadge";
import DetailedCountdownBadge from "@/components/DateCountdown/DetailedCountdownBadge";

interface CourtCaseListWithCustomFieldsProps {
  cases: CourtCaseItemType[];
  onCaseSelect: (courtCase: CourtCaseItemType) => void;
  onCaseEdit: (courtCase: CourtCaseItemType) => void;
  onCaseDelete: (caseId: string) => void;
  onBulkAction: (caseIds: string[], action: "delete") => void;
  onSort?: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
  currentSort?: { sortBy: string; sortOrder: 'asc' | 'desc' };
  loading?: boolean;
}

// Default fields configuration
const DEFAULT_FIELDS = [
  { name: 'stt', label: 'STT', dataType: 'number', width: 'w-16', showInList: true },
  { name: 'soVanThu', label: 'SỐ VĂN THƯ', dataType: 'text', width: 'w-32', showInList: true },
  { name: 'ngayNhanVanThu', label: 'NGÀY NHẬN VĂN THƯ', dataType: 'date', width: 'w-36', showInList: true },
  { name: 'loaiAn', label: 'LOẠI ÁN', dataType: 'text', width: 'w-28', showInList: true },
  { name: 'soThuLy', label: 'SỐ THỤ LÝ', dataType: 'text', width: 'w-32', showInList: true },
  { name: 'ngayThuLy', label: 'NGÀY THỤ LÝ', dataType: 'date', width: 'w-32', showInList: true },
  { name: 'tand', label: 'TAND', dataType: 'text', width: 'w-32', showInList: true },
  { name: 'soBanAn', label: 'SỐ BẢN ÁN', dataType: 'text', width: 'w-32', showInList: true },
  { name: 'ngayBanHanh', label: 'NGÀY BAN HÀNH', dataType: 'date', width: 'w-36', showInList: true },
  { name: 'biCaoNguoiKhieuKien', label: 'BỊ CÁO/NĐ/NKK', dataType: 'text', width: 'w-44', showInList: true },
  { name: 'toiDanhNoiDung', label: 'TỘI DANH/BĐ/NBK', dataType: 'textarea', width: 'w-44', showInList: true },
  { name: 'quanHePhatLuat', label: 'TỘI DANH/QHPL', dataType: 'text', width: 'w-44', showInList: true },
  { name: 'hinhThucXuLy', label: 'HÌNH THỨC', dataType: 'text', width: 'w-32', showInList: true },
  { name: 'thuTucApDung', label: 'THỦ TỤC', dataType: 'text', width: 'w-28', showInList: true },
  { name: 'thamPhanPhuTrach', label: 'THẨM PHÁN', dataType: 'text', width: 'w-32', showInList: true },
  { name: 'truongPhoPhongKTNV', label: 'TRƯỞNG/PHÓ PHÒNG', dataType: 'text', width: 'w-44', showInList: true },
  { name: 'ghiChu', label: 'GHI CHÚ', dataType: 'textarea', width: 'w-32', showInList: true },
  { name: 'ghiChuKetQua', label: 'GHI CHÚ KQ', dataType: 'textarea', width: 'w-32', showInList: true },
  { name: 'trangThaiGiaiQuyet', label: 'TRẠNG THÁI', dataType: 'select', width: 'w-32', showInList: true },
];

const CourtCaseListWithCustomFields: React.FC<CourtCaseListWithCustomFieldsProps> = ({
  cases,
  onCaseSelect,
  onCaseEdit,
  onCaseDelete,
  onBulkAction,
  onSort,
  currentSort,
  loading = false
}) => {
  const { hasPermission } = usePermissions();
  const [selectedCases, setSelectedCases] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [customFields, setCustomFields] = useState<CustomField[]>([]);
  const [allFields, setAllFields] = useState<any[]>([]);
  const [fieldConfiguration, setFieldConfiguration] = useState<FieldConfiguration | null>(null);
  const [dateCountdowns, setDateCountdowns] = useState<DateCountdown[]>([]);

  useEffect(() => {
    fetchCustomFields();

    // Refresh field configuration every 30 seconds to sync changes
    const interval = setInterval(() => {
      fetchCustomFields();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const fetchCustomFields = async () => {
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";

      // Fetch custom fields, field configuration và date countdowns song song
      const [customFieldsResult, configResult, countdownsResult] = await Promise.all([
        customFieldApiRequest.getCustomFields('CourtCase', sessionToken),
        fieldConfigurationApiRequest.getFieldConfiguration('CourtCase', sessionToken),
        dateCountdownApiRequest.getDateCountdowns('CourtCase', sessionToken)
      ]);

      if (customFieldsResult.payload.success && configResult.payload.success) {
        setCustomFields(customFieldsResult.payload.fields);
        setFieldConfiguration(configResult.payload.configuration);

        if (countdownsResult.payload.success) {
          setDateCountdowns(countdownsResult.payload.countdowns);
        }

        // Combine built-in fields với custom fields theo cấu hình
        const fieldConfigs = configResult.payload.configuration.fieldConfigs;
        const combined: any[] = [];

        // Thêm default fields theo cấu hình
        DEFAULT_FIELDS.forEach((defaultField) => {
          const config = fieldConfigs.find(fc => fc.fieldName === defaultField.name);
          if (config?.showInList !== false) { // Chỉ hiển thị nếu không bị ẩn
            combined.push({
              ...defaultField,
              _id: `default_${defaultField.name}`,
              isDefault: true,
              config: {
                showInList: config?.showInList ?? true,
                columnWidth: config?.columnWidth ?? 150,
                sortOrder: config?.sortOrder ?? 0
              }
            });
          }
        });

        // Thêm custom fields theo cấu hình
        customFieldsResult.payload.fields.forEach((customField: CustomField) => {
          const config = fieldConfigs.find(fc => fc.fieldName === customField.name);
          const showInList = config?.showInList ?? customField.config.showInList;

          if (showInList) { // Chỉ hiển thị nếu được bật
            combined.push({
              ...customField,
              isDefault: false,
              config: {
                ...customField.config,
                showInList,
                columnWidth: config?.columnWidth ?? customField.config.columnWidth,
                sortOrder: config?.sortOrder ?? customField.config.sortOrder
              }
            });
          }
        });

        // Sắp xếp theo sortOrder
        combined.sort((a, b) => a.config.sortOrder - b.config.sortOrder);

        setAllFields(combined);
      }
    } catch (error) {
      console.error("Error fetching custom fields:", error);
    }
  };

  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedCases(cases.map(c => c._id));
    } else {
      setSelectedCases([]);
    }
  };

  const handleSelectCase = (caseId: string, checked: boolean) => {
    if (checked) {
      setSelectedCases(prev => [...prev, caseId]);
    } else {
      setSelectedCases(prev => prev.filter(id => id !== caseId));
      setSelectAll(false);
    }
  };

  const handleBulkDelete = () => {
    if (selectedCases.length === 0) {
      toast.warning("Vui lòng chọn ít nhất một vụ việc để xóa");
      return;
    }

    if (confirm(`Bạn có chắc chắn muốn xóa ${selectedCases.length} vụ việc đã chọn?`)) {
      onBulkAction(selectedCases, "delete");
      setSelectedCases([]);
      setSelectAll(false);
    }
  };

  const handleSort = (sortBy: string) => {
    if (!onSort) return;
    
    let newSortOrder: 'asc' | 'desc' = 'asc';
    
    if (currentSort?.sortBy === sortBy) {
      newSortOrder = currentSort.sortOrder === 'asc' ? 'desc' : 'asc';
    }
    
    onSort(sortBy, newSortOrder);
  };

  const SortButton: React.FC<{ field: string; children: React.ReactNode }> = ({ field, children }) => {
    const isActive = currentSort?.sortBy === field;
    const isAsc = isActive && currentSort?.sortOrder === 'asc';
    const isDesc = isActive && currentSort?.sortOrder === 'desc';

    return (
      <button
        onClick={() => handleSort(field)}
        className="flex items-center gap-1 hover:text-gray-700 transition-colors"
      >
        {children}
        <span className="flex flex-col">
          <span className={`text-xs leading-none ${isAsc ? 'text-blue-600' : 'text-gray-400'}`}>▲</span>
          <span className={`text-xs leading-none ${isDesc ? 'text-blue-600' : 'text-gray-400'}`}>▼</span>
        </span>
      </button>
    );
  };

  const getFieldValue = (courtCase: any, field: any) => {
    if (field.isDefault) {
      // Default field
      const value = courtCase[field.name];
      if (field.dataType === 'date' && value) {
        return formatDate(value);
      }
      return value || '';
    } else {
      // Custom field
      const value = courtCase.customFields?.[field.name];
      if (!value) return '';

      // Format based on field type
      switch (field.dataType) {
        case 'date':
          return value ? formatDate(value) : '';
        case 'datetime':
          return value ? new Date(value).toLocaleString('vi-VN') : '';
        case 'currency':
          return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
          }).format(value);
        case 'percentage':
          return `${value}%`;
        case 'boolean':
          return value ? 'Có' : 'Không';
        case 'select':
          const option = field.config.options?.find((opt: any) => opt.value === value);
          return option ? option.label : value;
        case 'multiselect':
          if (Array.isArray(value)) {
            return value.map(v => {
              const option = field.config.options?.find((opt: any) => opt.value === v);
              return option ? option.label : v;
            }).join(', ');
          }
          return value;
        default:
          return value.toString();
      }
    }
  };

  const getDateCountdownConfig = (fieldName: string) => {
    return dateCountdowns.find(dc => dc.fieldName === fieldName && dc.countdownConfig.enabled);
  };

  const isDateField = (field: any) => {
    if (field.isDefault) {
      return ['ngayNhanVanThu', 'ngayThuLy', 'ngayBanHanh'].includes(field.name);
    } else {
      return field.dataType === 'date' || field.dataType === 'datetime';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Chưa giải quyết':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'Đang giải quyết':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Đã giải quyết':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100">
        <div className="p-6">
          <div className="space-y-4">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg animate-pulse">
                <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100">
      {/* Header */}
      <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Danh sách vụ việc tòa án
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              Hiển thị {cases?.length || 0} vụ việc với {allFields?.filter(f => f?.config?.showInList)?.length || 0} cột
            </p>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">
              Tổng: <span className="font-semibold text-gray-900">{cases?.length || 0}</span> vụ việc
            </span>
          </div>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedCases.length > 0 && (
        <div className="bg-blue-50 border-b border-blue-200 px-6 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-sm text-blue-700 font-medium">
                Đã chọn {selectedCases.length} vụ việc
              </span>
            </div>
            <div className="flex gap-2">
              <button
                onClick={handleBulkDelete}
                className="flex items-center gap-1 px-3 py-1.5 bg-red-500 text-white text-sm rounded-lg hover:bg-red-600 transition-colors font-medium"
              >
                Xóa đã chọn
              </button>
              <button
                onClick={() => {
                  setSelectedCases([]);
                  setSelectAll(false);
                }}
                className="flex items-center gap-1 px-3 py-1.5 bg-gray-500 text-white text-sm rounded-lg hover:bg-gray-600 transition-colors"
              >
                Bỏ chọn
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {hasPermission('court_case_delete') && (
                <th className="px-4 py-4 text-left w-12">
                  <input
                    type="checkbox"
                    checked={selectAll}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                  />
                </th>
              )}
              
              {/* Dynamic Headers */}
              {allFields.filter(field => field.config.showInList).map((field) => (
                <th 
                  key={field._id}
                  className={`px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider ${field.width || 'w-32'}`}
                  style={{ width: field.config.columnWidth || 150 }}
                >
                  {onSort ? (
                    <SortButton field={field.name}>
                      {field.label}
                    </SortButton>
                  ) : (
                    field.label
                  )}
                </th>
              ))}
              
              {/* Actions */}
              <th className="px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32">
                THAO TÁC
              </th>
            </tr>
          </thead>
          
          <tbody className="bg-white divide-y divide-gray-200">
            {cases.map((courtCase, index) => (
              <tr 
                key={courtCase._id} 
                className="hover:bg-gray-50 transition-colors cursor-pointer"
                onClick={() => onCaseSelect(courtCase)}
              >
                {hasPermission('court_case_delete') && (
                  <td className="px-4 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      checked={selectedCases.includes(courtCase._id)}
                      onChange={(e) => {
                        e.stopPropagation();
                        handleSelectCase(courtCase._id, e.target.checked);
                      }}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                    />
                  </td>
                )}
                
                {/* Dynamic Data */}
                {allFields.filter(field => field.config.showInList).map((field) => (
                  <td 
                    key={field._id}
                    className="px-4 py-4 whitespace-nowrap text-sm text-gray-900"
                    style={{ maxWidth: field.config.columnWidth || 150 }}
                  >
                    {field.name === 'stt' ? (
                      <span className="font-medium">{courtCase.stt || index + 1}</span>
                    ) : field.name === 'trangThaiGiaiQuyet' ? (
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(getFieldValue(courtCase, field))}`}>
                        {getFieldValue(courtCase, field)}
                      </span>
                    ) : isDateField(field) ? (
                      <div className="space-y-1">
                        <div className="truncate" title={getFieldValue(courtCase, field)}>
                          {getFieldValue(courtCase, field)}
                        </div>
                        {(() => {
                          const countdownConfig = getDateCountdownConfig(field.name);
                          if (countdownConfig && countdownConfig.countdownConfig.showCountdownBadge) {
                            const dateValue = field.isDefault
                              ? courtCase[field.name]
                              : courtCase.customFields?.[field.name];

                            if (dateValue) {
                              return (
                                <DateCountdownBadge
                                  date={dateValue}
                                  warningDays={countdownConfig.countdownConfig.warningDays}
                                  dangerDays={countdownConfig.countdownConfig.dangerDays}
                                  size="sm"
                                  showIcon={true}
                                  showText={true}
                                />
                              );
                            }
                          }
                          return null;
                        })()}
                      </div>
                    ) : (
                      <div className="truncate" title={getFieldValue(courtCase, field)}>
                        {getFieldValue(courtCase, field)}
                      </div>
                    )}
                  </td>
                ))}
                
                {/* Actions */}
                <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex items-center gap-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onCaseSelect(courtCase);
                      }}
                      className="text-blue-600 hover:text-blue-900 transition-colors"
                      title="Xem chi tiết"
                    >
                      👁️
                    </button>
                    {hasPermission('court_case_edit') && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onCaseEdit(courtCase);
                        }}
                        className="text-green-600 hover:text-green-900 transition-colors"
                        title="Chỉnh sửa"
                      >
                        ✏️
                      </button>
                    )}
                    {hasPermission('court_case_delete') && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onCaseDelete(courtCase._id);
                        }}
                        className="text-red-600 hover:text-red-900 transition-colors"
                        title="Xóa"
                      >
                        🗑️
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {cases.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">📋</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Không có vụ việc nào</h3>
          <p className="text-gray-500">Chưa có dữ liệu vụ việc tòa án để hiển thị.</p>
        </div>
      )}
    </div>
  );
};

export default CourtCaseListWithCustomFields;
