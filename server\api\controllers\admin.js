const mongoose = require("mongoose");
const User = require("../models/user");
// Analytics removed
// const Payment = require("../models/payment");
var moment = require("moment");
const { defineAbilityFor } = require("../permissions/abilities");
const { ForbiddenError } = require("@casl/ability");
const bcrypt = require("bcryptjs");
const { customAlphabet } = require("nanoid");
const UserLog = require("../models/UserLog");
const Department = require("../models/department");
// const CourtCase = require("../../models/CourtCase"); // Temporarily disabled
const { validatePassword, getPasswordErrorMessage } = require("../utils/passwordValidator");
const { syncUserPermissions } = require("../../utils/permissionSyncWrapper");

exports.userSignup = async (req, res) => {
 
  try {
    const { password, username, phonenumber, department, permissions } = req.body;
    const email = req.body.email?.toLowerCase(); // Ensure email is defined before calling `toLowerCase()`

    if (!email || !password || !username) {
      return res.status(403).json({
        success: false,
        message: "Vui lòng điền đầy đủ thông tin.",
      });
    }

    // Kiểm tra quyền tạo user
    if (req.user && req.user.rule === 'department_manager') {
      // Department manager chỉ có thể tạo user trong phòng ban của mình
      if (!department || department !== req.user.department.toString()) {
        return res.status(403).json({
          success: false,
          message: "Bạn chỉ có thể tạo thành viên trong phòng ban của mình.",
        });
      }
    }

    // Validate password
    if (!validatePassword(password)) {
      return res.status(403).json({
        success: false,
        message: getPasswordErrorMessage(),
      });
    }

    // Check if phone number exists
    if (phonenumber) {
      const existingUserPhone = await User.findOne({ phonenumber });
      if (existingUserPhone) {
        return res.status(403).json({
          success: false,
          message: "Số điện thoại đã được sử dụng.",
          userErr: true,
          mailErr: false,
        });
      }
    }

    // Check if email exists
    const existingUserEmail = await User.findOne({ email });
    if (existingUserEmail) {
      return res.status(403).json({
        success: false,
        message: "Địa chỉ email này đã được sử dụng.",
        userErr: false,
        mailErr: true,
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Generate user code
    const nanoid = customAlphabet("1234567890abcdef", 8);

    // Determine user role and permissions
    let userRule = 'user';
    let userDepartmentRole = 'member';
    let userPermissions = [];

    if (department) {
      userRule = 'department_member';
      userDepartmentRole = 'member';

      // Get department default permissions if no specific permissions provided
      if (!permissions || permissions.length === 0) {
        const Department = require("../models/department");
        const dept = await Department.findById(department);
        if (dept) {
          userPermissions = dept.defaultPermissions || [];
        }
      } else {
        userPermissions = permissions;
      }
    }

    const user = new User({
      username,
      email,
      password: hashedPassword,
      code: nanoid(),
      department: department || null,
      rule: userRule,
      departmentRole: userDepartmentRole,
      permissions: userPermissions,
      ...(phonenumber && { phonenumber }), // Only include `phonenumber` if it's provided
    });

    await user.save();

    // Update department member count if applicable
    if (department) {
      const Department = require("../models/department");
      const dept = await Department.findById(department);
      if (dept) {
        await dept.updateMemberCount();
      }
    }

    res.status(201).json({
      success: true,
      message: "Đã tạo tài khoản mới thành công.",
      user,
    });
  } catch (err) {
    console.error("Signup Error:", err);
    res.status(500).json({
      success: false,
      message: "Đã xảy ra lỗi, vui lòng thử lại sau.",
      error: err.message,
    });
  }
};


exports.get_all_user = async (req, res) => {
  console.log('[DEBUG] get_all_user called');
  console.log('[DEBUG] User:', req.user ? { 
    id: req.user._id, 
    email: req.user.email, 
    rule: req.user.rule, 
    permissions: req.user.permissions 
  } : 'No user');
  
  const ability = defineAbilityFor(req.user);
  const { page, perPage } = req.body;
  // let type = "desc";
  // const field = sort.field;
  // if (sort.type !== "none") {
  //   type = sort.type;
  // }
  try {
    let filter = {};

    // Nếu không phải admin, chỉ xem được user trong phòng ban của mình
    if (req.user.rule !== 'admin') {
      if (req.user.rule === 'department_manager' && req.user.department) {
        filter.department = req.user.department;
      } else {
        // Nếu không phải admin hoặc department manager, không được xem danh sách user
        return res.status(403).json({
          success: false,
          message: "Bạn không có quyền xem danh sách thành viên.",
        });
      }
    }

    let onTotal = User.countDocuments(filter);
    let onUsers = User.find(filter)
      .select("_id username phonenumber email rule departmentRole gender private createdAt")
      .populate('department', 'name code')
      .sort({ createdAt: "desc" })
      .skip(perPage * page - perPage)
      .limit(perPage);
    let [total, users] = await Promise.all([onTotal, onUsers]);

    console.log('[DEBUG] Found users:', users.length);
    console.log('[DEBUG] Can read users:', ability.can("read", "User"));

    ForbiddenError.from(ability).throwUnlessCan("read", users);
    res.json({
      success: true,
      users,
      total,
    });
  } catch (err) {
    console.error('[DEBUG] Error in get_all_user:', err.message);
    res.json({
      success: false,
      message: "something wrong",
    });
  }
};

exports.search_user = async (req, res) => {
  let email = { $regex: new RegExp("^" + req.body.email.toLowerCase()) };
  try {
    let users = await User.find({ email })
      .sort({ createdAt: "desc" })
      .limit(20);
    res.json({
      success: true,
      users,
    });
  } catch (err) {
    res.status(500).json({
      message: "Please check correctness and try again",
      error: err,
      success: false,
    });
  }
};

exports.get_single_user = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  let _id = req.params.id;
  console.log("Fetching user with ID:", _id);
  console.log("Request user:", req.user?.username, req.user?.rule);

  try {
    var user = await User.findById({ _id })
      .sort({ createdAt: "desc" })
      .select(
        "username email rule Wallet private address phonenumber private rank bio gender categories permissions"
      )
      .exec();

    console.log("Found user:", user ? user.username : "null");
    console.log("User permissions:", user ? user.permissions : "no user");

    if (!user) {
      return res.status(404).json({
        status: false,
        success: false,
        message: "User not found"
      });
    }

    ForbiddenError.from(ability).throwUnlessCan("read", user);
    console.log("User access granted");
    res.status(200).json({ status: true, user, success: true });
  } catch (err) {
    console.error("Error in get_single_user:", err);
    if (err.name === 'ForbiddenError') {
      res.status(403).json({
        status: false,
        success: false,
        message: "Access denied"
      });
    } else if (err.name === 'CastError') {
      res.status(400).json({
        status: false,
        success: false,
        message: "Invalid user ID format"
      });
    } else {
      res.status(500).json({
        status: false,
        success: false,
        message: "Internal server error"
      });
    }
  }
};

exports.delete_user = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  let _id = req.params.id;
  try {
    var user = await User.findById({ _id }).exec();
    ForbiddenError.from(ability).throwUnlessCan("delete", user);
    await User.findByIdAndDelete(_id);
    res.status(200).json({ success: true });
  } catch (err) {
    res.status(500).send(err);
  }
};

exports.admin_change_password = async (req, res, next) => {
  const ability = defineAbilityFor(req.user);
  const _id = req.body._id;
  const newPassword = req.body.password;
  try {
    let foundUser = await User.findById({ _id });
    if (!validatePassword(newPassword)) {
      return res.status(403).json({
        success: false,
        message: getPasswordErrorMessage(),
      });
    }
    ForbiddenError.from(ability).throwUnlessCan("update", foundUser);

    bcrypt.hash(newPassword, 10, async (err, hash) => {
      if (err) {
        return res.status(500).json({
          error: err,
          message: "Something wrong",
        });
      } else {
        foundUser.password = hash;
        await foundUser.save();
        // const html = PassChanged;
        // // Send email
        // mailer.sendEmail(
        //   "<EMAIL>",
        //   foundUser.email,
        //   "Password have been change by Administartor!",
        //   html
        // );
        res.status(200).json({
          success: true,
          message: "Password has been change",
        });
      }
    });
  } catch (err) {
    res.json(500).status({
      error: err,
      success: false,
      message: "Server Error",
    });
  }
};

exports.add_deposit = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  const nanoid = customAlphabet("1234567890abcdef", 6);
  let { amount, buyer } = req.body;
  try {
    let method = await Payment.findOne({});
    let deposit = new Deposit({
      code: nanoid(),
      amount: amount,
      buyer,
      method: method._id,
      paymentAmount: amount,
      status: "Completed",
    });
    ForbiddenError.from(ability).throwUnlessCan("create", deposit);
    await deposit.save();
    builUserBalance(buyer);
    res.status(200).json({
      success: true,
      deposit,
    });
  } catch (err) {
    res.status(500).json({
      message: err,
      success: false,
    });
  }
};

exports.get_deposit_by_date = async (req, res, next) => {
  let rule = req.user.rule;
  let ability = defineAbilityFor(req.user);
  let perPage = 20; // số lượng sản phẩm xuất hiện trên 1 page
  let page = parseInt(req.query.page) || 1;
  let { start, end } = req.body;
  let startFormat = moment.utc(String(start)).toDate();
  let endFormat = moment.utc(String(end)).add(24, "hours").toDate();
  try {
    if (rule == "admin") {
      let getTotal = Deposit.countDocuments({
        updatedAt: {
          $gte: startFormat,
          $lte: endFormat,
        },
      });
      let getDeposits = Deposit.find({
        updatedAt: {
          $gte: startFormat,
          $lte: endFormat,
        },
      })
        .populate("method", "methodname")
        .populate("buyer", "email")
        .sort({ updatedAt: "desc" })
        .skip(perPage * page - perPage) // Trong page đầu tiên sẽ bỏ qua giá trị là 0
        .limit(perPage);

      let getResult = Deposit.aggregate([
        {
          $match: {
            $and: [
              { updatedAt: { $gte: startFormat } },
              { updatedAt: { $lte: endFormat } },
            ],
          },
        },
        {
          $group: {
            _id: "$status",
            total: { $sum: { $multiply: ["$amount"] } },
          },
        },
      ]);
      let [total, deposits, result] = await Promise.all([
        getTotal,
        getDeposits,
        getResult,
      ]);
      ForbiddenError.from(ability).throwUnlessCan("read", deposits);
      let completed = result.find(({ _id }) => _id === "Completed") || {
        total: 0,
      };
      let onHold = result.find(({ _id }) => _id === "On hold") || { total: 0 };
      let refunded = result.find(({ _id }) => _id === "Refunded") || {
        total: 0,
      };
      let canceled = result.find(({ _id }) => _id === "Canceled") || {
        total: 0,
      };
      let confirmed = result.find(({ _id }) => _id === "Confirmed") || {
        total: 0,
      };
      let status = {
        completed,
        onHold,
        canceled,
        refunded,
        confirmed,
      };
      res.status(200).json({
        total,
        deposits,
        success: true,
        status,
      });
    } else {
      res.status(500).json({
        success: false,
        mesage: "You can't accept this action",
      });
    }
  } catch (err) {
    res.status(500).json({
      error: err,
    });
  }
};

exports.get_withdraw_by_date = async (req, res, next) => {
  let rule = req.user.rule;
  let ability = defineAbilityFor(req.user);
  let perPage = 20; // số lượng sản phẩm xuất hiện trên 1 page
  let page = parseInt(req.query.page) || 1;
  let { start, end } = req.body;
  let startFormat = moment.utc(String(start)).toDate();
  let endFormat = moment.utc(String(end)).add(24, "hours").toDate();
  try {
    if (rule == "admin") {
      let getTotal = Withdraw.countDocuments({
        updatedAt: {
          $gte: startFormat,
          $lte: endFormat,
        },
      });
      let getResult = Withdraw.aggregate([
        {
          $match: {
            $and: [
              { updatedAt: { $gte: startFormat } },
              { updatedAt: { $lte: endFormat } },
            ],
          },
        },
        {
          $group: {
            _id: "$status",
            total: { $sum: { $multiply: ["$amount"] } },
          },
        },
      ]);
      let getWithdraws = Withdraw.find({
        updatedAt: {
          $gte: startFormat,
          $lte: endFormat,
        },
      })
        .populate("method", "methodname")
        .populate("buyer", "email")
        .sort({ updatedAt: "desc" })
        .skip(perPage * page - perPage) // Trong page đầu tiên sẽ bỏ qua giá trị là 0
        .limit(perPage);
      let [total, withdraws, result] = await Promise.all([
        getTotal,
        getWithdraws,
        getResult,
      ]);
      ForbiddenError.from(ability).throwUnlessCan("read", withdraws);
      let completed = result.find(({ _id }) => _id === "Completed") || {
        total: 0,
      };
      let onHold = result.find(({ _id }) => _id === "On hold") || { total: 0 };
      let confirmed = result.find(({ _id }) => _id === "Confirmed") || {
        total: 0,
      };
      let canceled = result.find(({ _id }) => _id === "Canceled") || {
        total: 0,
      };
      let refunded = result.find(({ _id }) => _id === "Refunded") || {
        total: 0,
      };
      let status = {
        completed,
        onHold,
        confirmed,
        canceled,
        refunded,
      };
      res.status(200).json({
        total,
        withdraws,
        success: true,
        status,
      });
    } else {
      res.status(500).json({
        success: false,
        mesage: "You can't accept this action",
      });
    }
  } catch (err) {
    res.status(500).json({
      error: err,
    });
  }
};

exports.get_partner = async (req, res, next) => {
  const ability = defineAbilityFor(req.user);
  try {
    let onGetBalance = User.aggregate([
      {
        $group: {
          _id: null,
          deposit: { $sum: { $multiply: ["$Wallet.deposit"] } },
          interest: { $sum: { $multiply: ["$Wallet.interest"] } },
          widthdrawAll: { $sum: { $multiply: ["$Wallet.withdraw"] } },
          widthdrawInterest: {
            $sum: { $multiply: ["$Wallet.withdrawInterest"] },
          },
        },
      },
    ]);
    let [balance] = await Promise.all([onGetBalance]);
    ForbiddenError.from(ability).throwUnlessCan("read", balance);
    let allPartnerData = {
      partnerDeposit: 0,
      partnerInterest: 0,
      partnerWidthdrawAll: 0,
      partnerWidthdrawInterest: 0,
      partnerRevenue: 0,
    };
    if (balance.length > 0) {
      (allPartnerData.partnerDeposit = balance[0].deposit),
        (allPartnerData.partnerInterest = balance[0].interest),
        (allPartnerData.partnerWidthdrawAll = balance[0].widthdrawAll),
        (allPartnerData.partnerWidthdrawInterest =
          balance[0].widthdrawInterest),
        (allPartnerData.partnerRevenue =
          allPartnerData.partnerDeposit - allPartnerData.partnerWidthdrawAll);
    }
    res.json({
      success: true,
      allPartnerData,
    });
  } catch (err) {
    res.status(500).json({ error: err });
  }
};

exports.get_partner_by_date = async (req, res, next) => {
  let rule = req.user.rule;
  let ability = defineAbilityFor(req.user);
  let { start, end } = req.body;
  let startFormat = moment.utc(String(start)).toDate();
  let endFormat = moment.utc(String(end)).add(24, "hours").toDate();
  try {
    if (rule == "admin") {
      let onWithdrawAll = Withdraw.aggregate([
        {
          $match: {
            $and: [
              { updatedAt: { $gte: startFormat } },
              { updatedAt: { $lte: endFormat } },
              { type: "deposit" },
              { status: "Completed" },
            ],
          },
        },
        {
          $group: {
            _id: "$status",
            total: { $sum: { $multiply: ["$amount"] } },
          },
        },
      ]);
      let onWithdrawInterest = Withdraw.aggregate([
        {
          $match: {
            $and: [
              { updatedAt: { $gte: startFormat } },
              { updatedAt: { $lte: endFormat } },
              { type: "interest" },
              { status: "Completed" },
            ],
          },
        },
        {
          $group: {
            _id: "$status",
            total: { $sum: { $multiply: ["$amount"] } },
          },
        },
      ]);
      let onDeposit = Deposit.aggregate([
        {
          $match: {
            $and: [
              { updatedAt: { $gte: startFormat } },
              { updatedAt: { $lte: endFormat } },
            ],
          },
        },
        {
          $group: {
            _id: "$status",
            total: { $sum: { $multiply: ["$amount"] } },
          },
        },
      ]);
      let onInterest = Interest.aggregate([
        {
          $match: {
            $and: [
              { updatedAt: { $gte: startFormat } },
              { updatedAt: { $lte: endFormat } },
            ],
          },
        },
        {
          $group: {
            _id: null,
            total: { $sum: { $multiply: ["$amount"] } },
          },
        },
      ]);
      let [getWithdrawAll, getWithdrawInterest, getDeposit, getInterest] =
        await Promise.all([
          onWithdrawAll,
          onWithdrawInterest,
          onDeposit,
          onInterest,
        ]);
      ForbiddenError.from(ability).throwUnlessCan("read", getWithdrawAll);
      let WithdrawAll = getWithdrawAll.find(
        ({ _id }) => _id === "Completed"
      ) || { total: 0 };
      let WithdrawInterest = getWithdrawInterest.find(
        ({ _id }) => _id === "Completed"
      ) || { total: 0 };
      let deposit = getDeposit.find(({ _id }) => _id === "Completed") || {
        total: 0,
      };
      let interest = 0;
      if (getInterest.length > 0) {
        interest = getInterest[0].total;
      }
      let data = {
        WithdrawAll,
        WithdrawInterest,
        deposit,
        interest,
      };
      res.status(200).json({
        success: true,
        data,
      });
    } else {
      res.status(500).json({
        success: false,
        mesage: "You can't accept this action",
      });
    }
  } catch (err) {
    res.status(500).json({
      error: err,
    });
  }
};

exports.refesh_user = async (req, res, next) => {
  try {
    let users = await User.find().select("Wallet").exec();
    for (user of users) {
      builUserBalance(user._id);
    }
    res.json({
      success: true,
    });
  } catch (err) {
    res.status(500).json({ error: err });
  }
};

exports.get_revenue = async (req, res, next) => {
  const ability = defineAbilityFor(req.user);
  let perPage = 20; // số lượng sản phẩm xuất hiện trên 1 page
  let page = parseInt(req.query.page) || 1;
  try {
    let onTotal = Revenue.countDocuments({});
    let onRevenue = Revenue.find({})
      .populate("host", "email")
      .populate("partner", "email")
      .populate("interest", "amount")
      .sort({ updatedAt: "desc" })
      .skip(perPage * page - perPage) // Trong page đầu tiên sẽ bỏ qua giá trị là 0
      .limit(perPage);
    let [total, revenue] = await Promise.all([onTotal, onRevenue]);
    ForbiddenError.from(ability).throwUnlessCan("read", revenue);
    res.json({
      success: true,
      total,
      revenue,
    });
  } catch (err) {
    res.status(500).json({ error: err });
  }
};

exports.delete_revenue = async (req, res, next) => {
  const ability = defineAbilityFor(req.user);
  let id = req.params.id;
  try {
    let onrevenue = await Revenue.findById({ _id: id });
    ForbiddenError.from(ability).throwUnlessCan("delete", onrevenue);
    await Revenue.findByIdAndDelete(id);
    res.json({
      success: true,
    });
  } catch (err) {
    res.status(500).json({ error: err });
  }
};

exports.get_interest = async (req, res, next) => {
  const ability = defineAbilityFor(req.user);
  let perPage = 20; // số lượng sản phẩm xuất hiện trên 1 page
  let page = parseInt(req.query.page) || 1;
  try {
    let onTotal = Interest.countDocuments({});
    let oninterest = Interest.find({})
      .populate("user", "email")
      .sort({ updatedAt: "desc" })
      .skip(perPage * page - perPage) // Trong page đầu tiên sẽ bỏ qua giá trị là 0
      .limit(perPage);
    let [total, interest] = await Promise.all([onTotal, oninterest]);
    ForbiddenError.from(ability).throwUnlessCan("read", interest);
    res.json({
      success: true,
      total,
      interest,
    });
  } catch (err) {
    res.status(500).json({ error: err });
  }
};

exports.delete_interest = async (req, res, next) => {
  const ability = defineAbilityFor(req.user);
  let id = req.params.id;
  try {
    let oninterest = await Interest.findById({ _id: id });
    ForbiddenError.from(ability).throwUnlessCan("delete", oninterest);
    await Interest.findByIdAndDelete(id);
    res.json({
      success: true,
    });
  } catch (err) {
    res.status(500).json({ error: err });
  }
};

exports.admin_change_email = async (req, res, next) => {
  const ability = defineAbilityFor(req.user);
  let _id = req.params.id;
  let email = req.body.email.toLowerCase();
  try {
    let user = User.findById({ _id });
    let findEmail = User.findOne({ email: email });
    let [foundUser, foundEmail] = await Promise.all([user, findEmail]);
    if (foundEmail) {
      return res.status(403).json({
        success: false,
        message: "Email is already in use",
      });
    }
    ForbiddenError.from(ability).throwUnlessCan("update", foundUser);
    foundUser.email = email;
    await foundUser.save();
    res.status(200).json({
      success: true,
      message: "Email has been change",
    });
  } catch (err) {
    res.json(500).status({
      error: err,
      success: false,
      message: "Server Error",
    });
  }
};

exports.admin_get_user_revenue_history = async (req, res, next) => {
  let host = mongoose.Types.ObjectId(req.params.id);
  let perPage = 20; // số lượng sản phẩm xuất hiện trên 1 page
  let page = parseInt(req.query.page) || 1;
  try {
    let onUser = User.findById({ _id: host }).select("email");
    let onTotal = Revenue.countDocuments({ host: host });
    let onRevenue = Revenue.find({
      host: host,
    })
      .sort({ updatedAt: "desc" })
      .skip(perPage * page - perPage) // Trong page đầu tiên sẽ bỏ qua giá trị là 0
      .limit(perPage)
      .populate("interest", "amount")
      .populate("partner", "email")
      .exec();
    let [total, revenue, user] = await Promise.all([
      onTotal,
      onRevenue,
      onUser,
    ]);
    res.status(200).json({
      revenue,
      success: true,
      total,
      user,
    });
  } catch (err) {
    res.status(500).json({ error: err });
  }
};

exports.admin_get_chart = async (req, res, next) => {
  let rule = req.user.rule;
  let ability = defineAbilityFor(req.user);
  let startFormat = new Date();
  let endFormat = new Date();
  startFormat.setDate(startFormat.getDate() - 7); // set to 'now' minus 7 days.
  startFormat.setHours(0, 0, 0, 0); // set to midnight.
  try {
    if (rule == "admin") {
      let onDepositChart = Deposit.aggregate([
        {
          $match: {
            $and: [
              { updatedAt: { $gte: startFormat } },
              { updatedAt: { $lte: endFormat } },
              { status: "Completed" },
            ],
          },
        },
        {
          $group: {
            _id: { $dateToString: { format: "%Y-%m-%d", date: "$updatedAt" } },
            list: { $push: "$$ROOT" },
            total: { $sum: { $multiply: ["$amount"] } },
            count: { $sum: 1 },
          },
        },
      ]);
      let [getDepositChart] = await Promise.all([onDepositChart]);
      ForbiddenError.from(ability).throwUnlessCan("read", getDepositChart);

      res.status(200).json({
        success: true,
        data: getDepositChart,
      });
    } else {
      res.status(500).json({
        success: false,
        mesage: "You can't accept this action",
      });
    }
  } catch (err) {
    res.status(500).json({
      error: err,
    });
  }
};

exports.admin_update_user_private = async (req, res, next) => {
  let _id = req.body.id;
  const ability = defineAbilityFor(req.user);
  try {
    let user = await User.findById({ _id });
    ForbiddenError.from(ability).throwUnlessCan("update", user);
    if (user) {
      user.private = !user.private;
    }
    await user.save();

    res.status(200).json({
      success: true,
      user,
    });
  } catch (err) {
    res.status(404).json({
      error: err,
    });
  }
};

exports.getAllUserForSelect = async (req, res) => {
  const email = { $regex: new RegExp(`^${req.query.q.toLowerCase()}`) };
  try {
    // console.log(email)
    // const onUsers = User.find({email})
    //   .sort({ createdAt: 'desc' })
    //   .select('email');
    // const [users] = await Promise.all([onUsers]);
    // console.log(users)
    const users = await User.aggregate([
      { $match: { email: email } },
      {
        $group: {
          _id: "$_id",
          email: { $first: "$email" },
          username: { $first: "$username" },
          phonenumber: { $first: "$phonenumber" },
        },
      },
      { $limit: 20 },
    ]);
    res.json({
      success: true,
      users,
    });
  } catch (err) {
    res.json({
      success: false,
      message: "something wrong",
    });
  }
};



exports.getDashboardStats = async (req, res) => {
  console.log('[DEBUG] getDashboardStats called');
  console.log('[DEBUG] User:', req.user ? {
    id: req.user._id,
    email: req.user.email,
    rule: req.user.rule,
    permissions: req.user.permissions
  } : 'No user');

  const ability = defineAbilityFor(req.user);
  console.log('[DEBUG] Can read dashboard:', ability.can("read", "dashboard"));

  try {
    // Get total users
    const totalUsers = User.countDocuments();

    // Get active users (users who logged in within last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const activeUsers = User.countDocuments({
      lastLoginAt: { $gte: thirtyDaysAgo }
    });

    const [
      totalUsersCount,
      activeUsersCount
    ] = await Promise.all([
      totalUsers,
      activeUsers
    ]);

    console.log('[DEBUG] Before CASL check');
    ForbiddenError.from(ability).throwUnlessCan("read", "dashboard");
    console.log('[DEBUG] CASL check passed');

    const stats = {
      totalUsers: totalUsersCount,
      activeUsers: activeUsersCount
    };

    // Disable caching for dashboard stats to ensure fresh data
    res.set({
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });

    res.status(200).json({
      success: true,
      stats
    });
  } catch (err) {
    console.log(err);
    res.status(500).json({
      message: "Cannot get dashboard statistics",
      error: err,
      success: false,
    });
  }
};

exports.getRecentActivities = async (req, res) => {
  console.log('[DEBUG] getRecentActivities called');
  console.log('[DEBUG] User:', req.user ? { 
    id: req.user._id, 
    email: req.user.email, 
    rule: req.user.rule, 
    permissions: req.user.permissions 
  } : 'No user');
  
  const ability = defineAbilityFor(req.user);
  console.log('[DEBUG] Can read dashboard for recent activities:', ability.can("read", "dashboard"));
  
  try {
    console.log('[DEBUG] Before CASL check in getRecentActivities');
    ForbiddenError.from(ability).throwUnlessCan("read", "dashboard");
    console.log('[DEBUG] CASL check passed in getRecentActivities');

    const limit = parseInt(req.query.limit) || 10;

    // Get recent users (registered)
    const recentUsers = User.find()
      .sort({ createdAt: -1 })
      .limit(limit)
      .select('username email createdAt rule');

    const [users] = await Promise.all([
      recentUsers
    ]);

    // Combine and format activities
    const activities = [];

    // Add user activities
    users.forEach(user => {
      activities.push({
        id: user._id,
        type: 'user_registered',
        action: 'Thành viên mới đăng ký',
        title: user.username,
        user: user.username,
        userId: user._id,
        time: user.createdAt,
        role: user.rule
      });
    });

    // Sort by time (most recent first) and limit
    activities.sort((a, b) => new Date(b.time) - new Date(a.time));
    const recentActivities = activities.slice(0, limit);

    // Disable caching for recent activities to ensure fresh data
    res.set({
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });

    res.status(200).json({
      success: true,
      activities: recentActivities
    });
  } catch (err) {
    console.log(err);
    res.status(500).json({
      message: "Cannot get recent activities",
      error: err,
      success: false,
    });
  }
};






exports.adminChangeInfo = async (req, res, next) => {
  const ability = defineAbilityFor(req.user);
  const email = req.body.email.toLowerCase();
  const { username, phonenumber, gender, bio, idname, rank, private, _id, rule, categories, permissions } =
    req.body;
  
  // Debug log để xem data nhận được
  console.log('=== adminChangeInfo Debug ===');
  console.log('User ID:', _id);
  console.log('Permissions received:', permissions);
  console.log('Request body:', JSON.stringify(req.body, null, 2));
  console.log('=============================');
    
  try {
    const onUser = User.findById({ _id }).select(
      "username email avatar bio phonenumber gender idname store rule private permissions rank"
    );
    const onEmail = User.findOne({ email });
    const onPhone = User.findOne({ phonenumber });
    const [user, inEmail, inPhone] = await Promise.all([
      onUser,
      onEmail,
      onPhone,
    ]);
    if (inEmail && inEmail.email !== user.email) {
      res.status(501).json({
        success: false,
        message: "Email này đã được dùng cho tài khoản khác",
      });
      return;
    } else if (inPhone && inPhone.phonenumber !== user.phonenumber) {
      res.status(502).json({
        success: false,
        message: "Số điện thoại này đã được dùng cho tài khoản khác",
      });
      return;
    } else {
      ForbiddenError.from(ability).throwUnlessCan("update", user);
      if (user) {
        user.email = email;
        user.username = username;
        user.idname = idname;
        user.phonenumber = phonenumber;
        user.bio = bio;
        user.gender = gender;
        user.rank = rank;
        user.private = private;
        user.rule = rule;
        user.permissions = permissions || [];
      }
      user.categories = [];
      if (typeof categories === "object") {
        for (const u of categories) {
          const catId = {};
          catId._id = u;
          user.categories.push(catId);
        }
      } else {
        user.categories.push({
          _id: categories,
        });
      }
      await user.save();

      // Sync permissions to all user sessions
      await syncUserPermissions(user._id.toString());

      res.status(200).json({
        success: true,
        message: "Success",
        user
      });
    }
  } catch (err) {
    res.json(500).status({
      error: err,
      success: false,
      message: "Server Error",
    });
  }
};

exports.getUserLog = async (req, res, next) => {

  const ability = defineAbilityFor(req.user);
  const _id = req.params.id;

  try {
    const user = await User.findById(_id).select('username email')

    if (!user) {
      return res.status(404).json({ status: false, message: "User not found" });
    }

    ForbiddenError.from(ability).throwUnlessCan("read", user);

    // Fetch latest 10 user logs
    const userLogs = await UserLog.find({ user: user._id })
      .sort({ loginTime: -1 }) // Sort by newest first
      .limit(10) // Limit to last 10 logs
      .lean(); // Convert Mongoose objects to plain JS
    res.status(200).json({ status: true, userLogs, success: true, user });
  } catch (err) {
    console.error("Error fetching user logs:", err);
    res.status(500).json({ status: false, message: "Internal Server Error", error: err.message });
  }
};

// Get dashboard stats based on user role and permissions
exports.getUserDashboardStats = async (req, res) => {
  try {
    // Get user from passport authentication (req.user is set by passport middleware)
    const user = req.user;

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // console.log('[DEBUG] Mock User for testing:', user); // Debug removed

    // Base stats object
    const stats = {
      userInfo: {
        username: user.username,
        email: user.email,
        rule: user.rule,
        permissions: user.permissions || [],
        department: user.department?.name || user.department || 'Chưa có phòng ban',
        departmentRole: user.departmentRole
      },
      permissions: {
        available: user.permissions || [],
        categories: {}
      },
      departments: [],
      systemStats: {},
      dashboardType: 'user' // default
    };

    // Determine dashboard type
    if (user.rule === 'admin') {
      stats.dashboardType = 'admin';
    } else if (user.rule === 'department_manager' || user.departmentRole === 'manager') {
      stats.dashboardType = 'department_manager';
    } else {
      stats.dashboardType = 'user';
    }

    // Categorize permissions
    if (user.permissions) {
      const permissionCategories = {
        user_management: [],
        court_case: [],
        file_management: [],
        department: [],
        system: [],
        custom_fields: []
      };

      user.permissions.forEach(permission => {
        if (permission.startsWith('user_')) {
          permissionCategories.user_management.push(permission);
        } else if (permission.startsWith('court_case_')) {
          permissionCategories.court_case.push(permission);
        } else if (permission.startsWith('file_')) {
          permissionCategories.file_management.push(permission);
        } else if (permission.startsWith('department_')) {
          permissionCategories.department.push(permission);
        } else if (permission.startsWith('system_')) {
          permissionCategories.system.push(permission);
        } else if (permission.startsWith('custom_fields_')) {
          permissionCategories.custom_fields.push(permission);
        }
      });

      stats.permissions.categories = permissionCategories;
    }

    // Get departments info based on role
    try {
      if (user.rule === 'admin') {
        // Admin sees all departments
        const departments = await Department.find({})
          .select('name description memberCount isActive createdAt')
          .lean();

        stats.departments = departments.map(dept => ({
          _id: dept._id,
          name: dept.name,
          description: dept.description,
          memberCount: dept.memberCount || 0,
          isActive: dept.isActive,
          createdAt: dept.createdAt
        }));
      } else if (user.rule === 'department_manager' || user.departmentRole === 'manager') {
        // Department manager sees their department + basic info of others
        const allDepartments = await Department.find({})
          .select('name description memberCount isActive createdAt')
          .lean();

        stats.departments = allDepartments.map(dept => {
          const isOwnDepartment = dept._id.toString() === user.department?.toString();
          return {
            _id: dept._id,
            name: dept.name,
            description: isOwnDepartment ? dept.description : 'Phòng ban khác',
            memberCount: isOwnDepartment ? (dept.memberCount || 0) : null,
            isActive: dept.isActive,
            createdAt: dept.createdAt,
            isOwnDepartment
          };
        });
      } else if (user.department) {
        // Regular user sees only their department
        const userDepartment = await Department.findById(user.department)
          .select('name description memberCount isActive createdAt')
          .lean();

        if (userDepartment) {
          stats.departments = [{
            _id: userDepartment._id,
            name: userDepartment.name,
            description: userDepartment.description,
            memberCount: userDepartment.memberCount || 0,
            isActive: userDepartment.isActive,
            createdAt: userDepartment.createdAt,
            isOwnDepartment: true
          }];
        }
      }
    } catch (error) {
      console.error('Error fetching departments:', error);
      stats.departments = [];
    }

    // Set dashboard type and stats based on role
    if (user.rule === 'admin') {
      stats.dashboardType = 'admin';
      // Admin gets full system stats
      try {
        const [totalUsers, totalDepartments] = await Promise.all([
          User.countDocuments({}),
          Department.countDocuments({})
        ]);

        stats.systemStats = {
          totalUsers,
          totalDepartments,
          totalCourtCases: 25, // Mock data
          activeUsers: await User.countDocuments({ isActive: { $ne: false } }),
          activeDepartments: await Department.countDocuments({ isActive: true })
        };
      } catch (error) {
        console.error('Error fetching admin stats:', error);
        stats.systemStats = {};
      }
    } else if (user.rule === 'department_manager' || user.departmentRole === 'manager') {
      stats.dashboardType = 'department_manager';
      // Department manager gets department-specific stats
      try {
        const departmentStats = {};

        if (user.department) {
          departmentStats.departmentMembers = await User.countDocuments({
            department: user.department
          });
          departmentStats.activeDepartmentMembers = await User.countDocuments({
            department: user.department,
            isActive: { $ne: false }
          });
        }

        // Court case stats if has permission
        if (user.permissions?.includes('court_case_view') || user.permissions?.includes('court_case_stats')) {
          departmentStats.totalCourtCases = 15; // Mock data
          departmentStats.recentCourtCases = 5; // Mock data
        }

        stats.systemStats = departmentStats;
      } catch (error) {
        console.error('Error fetching department manager stats:', error);
        stats.systemStats = {};
      }
    } else {
      stats.dashboardType = 'user';
      // Regular user gets no system stats - only account info
      stats.systemStats = {};
    }

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Error getting user dashboard stats:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Get permission descriptions for display
exports.getPermissionDescriptions = async (req, res) => {
  try {
    const permissionDescriptions = {
      // User Management
      'user_view': 'Quản lý người dùng',
      'user_add': 'Thêm người dùng mới',
      'user_edit': 'Chỉnh sửa thông tin người dùng',
      'user_delete': 'Xóa người dùng',
      'user_import': 'Import danh sách người dùng',

      // Court Case Management
      'court_case_view': 'Xem danh sách vụ án',
      'court_case_create': 'Tạo vụ án mới',
      'court_case_edit': 'Chỉnh sửa vụ án',
      'court_case_delete': 'Xóa vụ án',
      'court_case_export': 'Xuất danh sách vụ án',
      'court_case_import': 'Import danh sách vụ án',
      'court_case_stats': 'Xem thống kê vụ án',

      // File Management
      'file_view': 'Xem danh sách file',
      'file_upload': 'Tải file lên',
      'file_delete': 'Xóa file',
      'file_download': 'Tải file xuống',

      // Department Management
      'department_view': 'Xem danh sách phòng ban',
      'department_create': 'Tạo phòng ban mới',
      'department_edit': 'Chỉnh sửa phòng ban',
      'department_delete': 'Xóa phòng ban',
      'department_member_manage': 'Quản lý thành viên phòng ban',

      // Custom Fields
      'custom_fields_view': 'Xem trường tùy chỉnh',
      'custom_fields_create': 'Tạo trường tùy chỉnh',
      'custom_fields_edit': 'Chỉnh sửa trường tùy chỉnh',
      'custom_fields_delete': 'Xóa trường tùy chỉnh',
      'custom_fields_manage': 'Quản lý trường tùy chỉnh',

      // System
      'system_settings_view': 'Xem cài đặt hệ thống',
      'system_settings_edit': 'Chỉnh sửa cài đặt hệ thống',
      'system_logs_view': 'Xem nhật ký hệ thống',
      'permissions_manage': 'Quản lý phân quyền',
      'system_admin_full_access': 'Quyền quản trị toàn hệ thống',
      'system_departments_manage': 'Quản lý phòng ban hệ thống',
      'system_users_manage': 'Quản lý người dùng hệ thống',
      'system_settings_manage': 'Quản lý cài đặt hệ thống'
    };

    res.json({
      success: true,
      data: permissionDescriptions
    });

  } catch (error) {
    console.error('Error getting permission descriptions:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
