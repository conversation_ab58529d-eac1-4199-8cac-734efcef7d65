'use client';

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
// import { Separator } from '@/components/ui/separator';
import { Users, Building, FileText, Shield, Settings, Eye } from 'lucide-react';
import adminApiRequest, { UserDashboardData, PermissionDescriptions } from '@/apiRequests/admin';

// Types are now imported from adminApiRequest

const RoleDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<UserDashboardData | null>(null);
  const [permissionDescriptions, setPermissionDescriptions] = useState<PermissionDescriptions>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sessionToken, setSessionToken] = useState<string>("");

  useEffect(() => {
    // Get session token on client side only
    const token = localStorage.getItem("sessionToken") || "";
    setSessionToken(token);
  }, []);

  useEffect(() => {
    if (!sessionToken) return; // Wait for session token

    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        const [dashboardResponse, descriptionsResponse] = await Promise.all([
          adminApiRequest.getUserDashboardStats(sessionToken),
          adminApiRequest.getPermissionDescriptions(sessionToken)
        ]);

        if (dashboardResponse.payload.success) {
          setDashboardData(dashboardResponse.payload.data);
        }

        if (descriptionsResponse.payload.success) {
          setPermissionDescriptions(descriptionsResponse.payload.data);
        }
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [sessionToken]);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'user_management':
        return <Users className="h-4 w-4" />;
      case 'court_case':
        return <FileText className="h-4 w-4" />;
      case 'file_management':
        return <FileText className="h-4 w-4" />;
      case 'department':
        return <Building className="h-4 w-4" />;
      case 'system':
        return <Settings className="h-4 w-4" />;
      case 'custom_fields':
        return <Shield className="h-4 w-4" />;
      default:
        return <Eye className="h-4 w-4" />;
    }
  };

  const getCategoryName = (category: string) => {
    switch (category) {
      case 'user_management':
        return 'Quản lý người dùng';
      case 'court_case':
        return 'Quản lý vụ án';
      case 'file_management':
        return 'Quản lý file';
      case 'department':
        return 'Quản lý phòng ban';
      case 'system':
        return 'Hệ thống';
      case 'custom_fields':
        return 'Trường tùy chỉnh';
      default:
        return category;
    }
  };

  const getRoleDisplayName = (rule: string) => {
    switch (rule) {
      case 'admin':
        return 'Quản trị viên';
      case 'department_manager':
        return 'Quản lý phòng ban';
      case 'department_member':
        return 'Thành viên phòng ban';
      case 'user':
        return 'Người dùng';
      default:
        return rule;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Đang tải...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-red-600">Lỗi: {error}</div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Không có dữ liệu</div>
      </div>
    );
  }

  const { userInfo, permissions, departments, systemStats, dashboardType } = dashboardData;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Thông tin tài khoản</h1>
          <p className="text-muted-foreground">
            Chào mừng {userInfo.username} - {getRoleDisplayName(userInfo.rule)}
          </p>
        </div>
        <Badge variant={dashboardType === 'admin' ? 'default' : 'secondary'}>
          {getRoleDisplayName(userInfo.rule)}
        </Badge>
      </div>

      {/* Account Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Thông tin tài khoản
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Tên đăng nhập</label>
              <p className="text-lg font-semibold">{userInfo.username}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Email</label>
              <p className="text-lg">{userInfo.email}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">Vai trò</label>
              <p className="text-lg">
                <Badge variant="outline">{getRoleDisplayName(userInfo.rule)}</Badge>
              </p>
            </div>
            {userInfo.department && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Phòng ban</label>
                <p className="text-lg">{userInfo.department}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Removed System Stats - only for admin */}

      <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2">
        {/* Permissions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Quyền và chức năng của bạn
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {Object.entries(permissions.categories).map(([category, perms]) => {
              if (perms.length === 0) return null;

              return (
                <div key={category} className="space-y-2">
                  <div className="flex items-center gap-2 font-medium">
                    {getCategoryIcon(category)}
                    {getCategoryName(category)}
                  </div>
                  <div className="grid gap-1 pl-6">
                    {perms.map((permission) => {
                      const displayName = permissionDescriptions[permission] || permission;
                      return (
                        <div key={permission} className="flex items-center">
                          <span className="text-sm">{displayName}</span>
                        </div>
                      );
                    })}
                  </div>
                  <hr className="my-2" />
                </div>
              );
            })}

            {permissions.available.length === 0 && (
              <p className="text-muted-foreground text-center py-4">
                Bạn chưa được cấp quyền nào
              </p>
            )}
          </CardContent>
        </Card>

        {/* Departments - Only show if user has department info */}
        {departments.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Phòng ban của bạn
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {departments.map((dept) => (
                <div key={dept._id} className="border rounded-lg p-4 space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium">{dept.name}</h3>
                    <div className="flex gap-2">
                      {dept.isOwnDepartment && (
                        <Badge variant="default" className="text-xs">
                          Phòng ban của bạn
                        </Badge>
                      )}
                      <Badge variant={dept.isActive ? 'default' : 'secondary'} className="text-xs">
                        {dept.isActive ? 'Hoạt động' : 'Không hoạt động'}
                      </Badge>
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground">{dept.description}</p>
                  {dept.memberCount !== null && (
                    <p className="text-sm">
                      <strong>Số thành viên:</strong> {dept.memberCount}
                    </p>
                  )}
                  <p className="text-xs text-muted-foreground">
                    Tạo ngày: {new Date(dept.createdAt).toLocaleDateString('vi-VN')}
                  </p>
                </div>
              ))}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default RoleDashboard;
