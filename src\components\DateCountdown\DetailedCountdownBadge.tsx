"use client";

import { useState, useEffect } from "react";
import { Clock, AlertTriangle, CheckCircle, XCircle } from "lucide-react";

interface DetailedCountdownBadgeProps {
  date: string | Date | null;
  warningDays?: number;
  dangerDays?: number;
  showIcon?: boolean;
  showDetailed?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

interface TimeRemaining {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  totalDays: number;
  isExpired: boolean;
}

export default function DetailedCountdownBadge({
  date,
  warningDays = 30,
  dangerDays = 7,
  showIcon = true,
  showDetailed = false,
  size = 'md',
  className = ''
}: DetailedCountdownBadgeProps) {
  const [timeRemaining, setTimeRemaining] = useState<TimeRemaining | null>(null);
  const [status, setStatus] = useState<'expired' | 'danger' | 'warning' | 'safe' | 'none'>('none');

  useEffect(() => {
    if (!date) {
      setTimeRemaining(null);
      setStatus('none');
      return;
    }

    const calculateTimeRemaining = () => {
      const countdownStartDate = new Date(date); // Ngày bắt đầu đếm ngược

      // Tính ngày hết hạn = ngày bắt đầu + số ngày cảnh báo
      const deadlineDate = new Date(countdownStartDate);
      deadlineDate.setDate(deadlineDate.getDate() + warningDays);

      // Chuyển đổi sang múi giờ GMT+7 (Việt Nam)
      const vietnamOffset = 7 * 60; // GMT+7 in minutes
      const currentDate = new Date();
      const currentUTC = currentDate.getTime() + (currentDate.getTimezoneOffset() * 60000);
      const vietnamTime = new Date(currentUTC + (vietnamOffset * 60000));

      // Reset thời gian về đầu ngày để so sánh chính xác
      countdownStartDate.setHours(0, 0, 0, 0);
      deadlineDate.setHours(0, 0, 0, 0);
      vietnamTime.setHours(0, 0, 0, 0);

      // Kiểm tra xem đã bắt đầu đếm ngược chưa
      const timeToCountdownStart = countdownStartDate.getTime() - vietnamTime.getTime();

      // Tính thời gian còn lại từ hiện tại đến ngày hết hạn
      const timeDiff = deadlineDate.getTime() - vietnamTime.getTime();

      // Kiểm tra hết hạn: nếu ngày hiện tại > ngày hết hạn
      if (timeDiff < 0) {
        return {
          days: 0,
          hours: 0,
          minutes: 0,
          seconds: 0,
          totalDays: Math.ceil(timeDiff / (1000 * 60 * 60 * 24)), // Số âm
          isExpired: true
        };
      }

      // Nếu chưa đến thời điểm bắt đầu đếm ngược
      if (timeToCountdownStart > 0) {
        return {
          days: 0,
          hours: 0,
          minutes: 0,
          seconds: 0,
          totalDays: Math.ceil(timeDiff / (1000 * 60 * 60 * 24)),
          isExpired: false
        };
      }

      // Đã bắt đầu đếm ngược - tính thời gian còn lại đến ngày hết hạn
      const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

      return {
        days,
        hours,
        minutes,
        seconds,
        totalDays: Math.ceil(timeDiff / (1000 * 60 * 60 * 24)),
        isExpired: false
      };
    };

    const updateTime = () => {
      const time = calculateTimeRemaining();
      setTimeRemaining(time);

      // Determine status based on total days
      if (time.isExpired) {
        setStatus('expired');
      } else {
        // Tính ngày bắt đầu đếm ngược để xác định trạng thái
        const countdownStartDate = new Date(date);

        const vietnamOffset = 7 * 60;
        const currentDate = new Date();
        const currentUTC = currentDate.getTime() + (currentDate.getTimezoneOffset() * 60000);
        const vietnamTime = new Date(currentUTC + (vietnamOffset * 60000));

        countdownStartDate.setHours(0, 0, 0, 0);
        vietnamTime.setHours(0, 0, 0, 0);

        const timeToCountdownStart = countdownStartDate.getTime() - vietnamTime.getTime();

        if (timeToCountdownStart > 0) {
          // Chưa đến thời điểm bắt đầu đếm ngược
          setStatus('safe');
        } else if (time.totalDays <= dangerDays) {
          setStatus('danger');
        } else {
          setStatus('warning');
        }
      }
    };

    // Update immediately
    updateTime();

    // Update every second for real-time countdown
    const interval = setInterval(updateTime, 1000);

    return () => clearInterval(interval);
  }, [date, warningDays, dangerDays]);

  const getStatusConfig = () => {
    const configs = {
      expired: {
        bg: 'bg-red-100',
        text: 'text-red-800',
        border: 'border-red-200',
        icon: XCircle,
        color: 'text-red-600'
      },
      danger: {
        bg: 'bg-red-50',
        text: 'text-red-700',
        border: 'border-red-300',
        icon: AlertTriangle,
        color: 'text-red-500'
      },
      warning: {
        bg: 'bg-yellow-50',
        text: 'text-yellow-700',
        border: 'border-yellow-300',
        icon: AlertTriangle,
        color: 'text-yellow-500'
      },
      safe: {
        bg: 'bg-green-50',
        text: 'text-green-700',
        border: 'border-green-300',
        icon: CheckCircle,
        color: 'text-green-500'
      },
      none: {
        bg: 'bg-gray-50',
        text: 'text-gray-700',
        border: 'border-gray-300',
        icon: Clock,
        color: 'text-gray-500'
      }
    };
    return configs[status];
  };

  const getSizeClasses = () => {
    const sizes = {
      sm: {
        container: 'px-2 py-1 text-xs',
        icon: 12,
        gap: 'gap-1'
      },
      md: {
        container: 'px-3 py-1.5 text-sm',
        icon: 14,
        gap: 'gap-1.5'
      },
      lg: {
        container: 'px-4 py-2 text-base',
        icon: 16,
        gap: 'gap-2'
      }
    };
    return sizes[size];
  };

  const formatCountdownText = () => {
    if (!timeRemaining) return 'Không có dữ liệu';

    if (timeRemaining.isExpired) {
      return 'Đã hết hạn';
    }

    // Kiểm tra xem đã bắt đầu đếm ngược chưa
    const countdownStartDate = new Date(date!);

    const vietnamOffset = 7 * 60;
    const currentDate = new Date();
    const currentUTC = currentDate.getTime() + (currentDate.getTimezoneOffset() * 60000);
    const vietnamTime = new Date(currentUTC + (vietnamOffset * 60000));

    countdownStartDate.setHours(0, 0, 0, 0);
    vietnamTime.setHours(0, 0, 0, 0);

    const timeToCountdownStart = countdownStartDate.getTime() - vietnamTime.getTime();

    if (timeToCountdownStart > 0) {
      // Chưa đến thời điểm bắt đầu đếm ngược - nhưng vẫn hiển thị "Còn X ngày" để nhất quán
      const daysToStart = Math.ceil(timeToCountdownStart / (1000 * 60 * 60 * 24));
      return `Còn ${daysToStart} ngày`;
    }

    // Đã bắt đầu đếm ngược - luôn hiển thị theo ngày để nhất quán
    if (timeRemaining.totalDays === 1) {
      return 'Còn 1 ngày';
    } else if (timeRemaining.totalDays > 1) {
      return `Còn ${timeRemaining.totalDays} ngày`;
    } else if (timeRemaining.totalDays === 0) {
      return 'Hết hạn hôm nay';
    } else {
      // Quá hạn
      const overdueDays = Math.abs(timeRemaining.totalDays);
      return `Quá hạn ${overdueDays} ngày`;
    }
  };

  if (!date || !timeRemaining) {
    return null;
  }

  const config = getStatusConfig();
  const sizeConfig = getSizeClasses();
  const IconComponent = config.icon;

  // Format ngày theo múi giờ GMT+7
  const formatDateVN = (dateStr: string | Date) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('vi-VN', {
      timeZone: 'Asia/Ho_Chi_Minh',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  return (
    <span
      className={`
        inline-flex items-center ${sizeConfig.gap} ${sizeConfig.container}
        ${config.bg} ${config.text} ${config.border}
        border rounded-full font-medium
        ${className}
      `}
      title={`${formatCountdownText()} - Hạn: ${formatDateVN(date)} (GMT+7)`}
    >
      {showIcon && (
        <IconComponent 
          size={sizeConfig.icon} 
          className={config.color}
        />
      )}
      {formatCountdownText()}
    </span>
  );
}

// Hook để sử dụng countdown logic với GMT+7
export const useDetailedCountdown = (date: string | Date | null, warningDays = 30, dangerDays = 7) => {
  const [timeRemaining, setTimeRemaining] = useState<TimeRemaining | null>(null);
  const [status, setStatus] = useState<'expired' | 'danger' | 'warning' | 'safe' | 'none'>('none');

  useEffect(() => {
    if (!date) {
      setTimeRemaining(null);
      setStatus('none');
      return;
    }

    const calculateTimeRemaining = () => {
      const countdownStartDate = new Date(date); // Ngày bắt đầu đếm ngược

      // Tính ngày hết hạn = ngày bắt đầu + số ngày cảnh báo
      const deadlineDate = new Date(countdownStartDate);
      deadlineDate.setDate(deadlineDate.getDate() + warningDays);

      // Chuyển đổi sang múi giờ GMT+7 (Việt Nam)
      const vietnamOffset = 7 * 60; // GMT+7 in minutes
      const currentDate = new Date();
      const currentUTC = currentDate.getTime() + (currentDate.getTimezoneOffset() * 60000);
      const vietnamTime = new Date(currentUTC + (vietnamOffset * 60000));

      // Reset thời gian về đầu ngày để so sánh chính xác
      countdownStartDate.setHours(0, 0, 0, 0);
      deadlineDate.setHours(0, 0, 0, 0);
      vietnamTime.setHours(0, 0, 0, 0);

      // Kiểm tra xem đã bắt đầu đếm ngược chưa
      const timeToCountdownStart = countdownStartDate.getTime() - vietnamTime.getTime();

      // Tính thời gian còn lại từ hiện tại đến ngày hết hạn
      const timeDiff = deadlineDate.getTime() - vietnamTime.getTime();

      // Kiểm tra hết hạn: nếu ngày hiện tại > ngày hết hạn
      if (timeDiff < 0) {
        return {
          days: 0,
          hours: 0,
          minutes: 0,
          seconds: 0,
          totalDays: Math.ceil(timeDiff / (1000 * 60 * 60 * 24)), // Số âm
          isExpired: true
        };
      }

      // Nếu chưa đến thời điểm bắt đầu đếm ngược
      if (timeToCountdownStart > 0) {
        return {
          days: 0,
          hours: 0,
          minutes: 0,
          seconds: 0,
          totalDays: Math.ceil(timeDiff / (1000 * 60 * 60 * 24)),
          isExpired: false
        };
      }

      // Đã bắt đầu đếm ngược
      const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

      return {
        days,
        hours,
        minutes,
        seconds,
        totalDays: Math.ceil(timeDiff / (1000 * 60 * 60 * 24)),
        isExpired: false
      };
    };

    const updateTime = () => {
      const time = calculateTimeRemaining();
      setTimeRemaining(time);

      // Determine status based on total days
      if (time.isExpired) {
        setStatus('expired');
      } else {
        // Tính ngày bắt đầu đếm ngược để xác định trạng thái
        const countdownStartDate = new Date(date);

        const vietnamOffset = 7 * 60;
        const currentDate = new Date();
        const currentUTC = currentDate.getTime() + (currentDate.getTimezoneOffset() * 60000);
        const vietnamTime = new Date(currentUTC + (vietnamOffset * 60000));

        countdownStartDate.setHours(0, 0, 0, 0);
        vietnamTime.setHours(0, 0, 0, 0);

        const timeToCountdownStart = countdownStartDate.getTime() - vietnamTime.getTime();

        if (timeToCountdownStart > 0) {
          // Chưa đến thời điểm bắt đầu đếm ngược
          setStatus('safe');
        } else if (time.totalDays <= dangerDays) {
          setStatus('danger');
        } else {
          setStatus('warning');
        }
      }
    };

    updateTime();
    const interval = setInterval(updateTime, 1000);

    return () => clearInterval(interval);
  }, [date, warningDays, dangerDays]);

  return { timeRemaining, status };
};
